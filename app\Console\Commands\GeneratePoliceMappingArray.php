<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel; // Ensure this is imported if using Laravel Excel
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GeneratePoliceMappingArray extends Command
{
    protected $signature = 'generate:police-mapping';
    protected $description = 'Generates a PHP array mapping old to new police numbers from an Excel file.';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Construct the path to the Excel file relative to the project's base path
        $excelFileName = './cegema_04.xlsx';
        $excelFilePath = base_path($excelFileName);
        
        $outputFilePath = config_path('police_mapping.php');

        if (!File::exists($excelFilePath)) {
            $this->error("Excel file not found at: " . $excelFilePath);
            $this->error("Please ensure the file '{$excelFileName}' exists in the project root directory.");
            return 1;
        }

        $this->info("Reading Excel file: " . $excelFilePath);
        
        try {
            $collection = Excel::toCollection(null, $excelFilePath)->first();
        } catch (\Exception $e) {
            $this->error("Error reading Excel file: " . $e->getMessage());
            return 1;
        }

        if ($collection === null || $collection->isEmpty()) {
            $this->error("Excel file is empty, the first sheet is empty, or the file could not be read correctly.");
            return 1;
        }

        $headerRow = $collection->first();
        if ($headerRow === null) {
            $this->error("Could not retrieve the header row from the Excel sheet.");
            return 1;
        }
        
        $headers = $headerRow->map(function ($header) {
            return Str::lower(trim(strval($header))); // Normalize headers, ensure string
        });
        
        $dataRows = $collection->slice(1); // Skip header row

        // Define potential names for the columns
        $ancienColNames = ['num police ancien crm', 'num police crm', 'ancien numero police'];
        $nouveauColNames = ['nouveau numéro de contrat', 'nouveau num police', 'nouveau numero police'];

        $ancienColKey = null;
        $nouveauColKey = null;

        // Find ancien num police column index
        foreach ($ancienColNames as $name) {
            $key = $headers->search($name);
            if ($key !== false) {
                $ancienColKey = $key;
                break;
            }
        }

        // Find new num police column index
        foreach ($nouveauColNames as $name) {
            $key = $headers->search($name);
            if ($key !== false) {
                $nouveauColKey = $key;
                break;
            }
        }

        if (is_null($ancienColKey)) {
            $this->error("Could not find the 'ancien num police' column.");
            $this->line("Looked for: " . implode(', ', $ancienColNames));
            $this->line("Actual headers found: " . $headers->implode(', '));
            return 1;
        }
        if (is_null($nouveauColKey)) {
            $this->error("Could not find the 'new num police' column.");
            $this->line("Looked for: " . implode(', ', $nouveauColNames));
            $this->line("Actual headers found: " . $headers->implode(', '));
            return 1;
        }
        
        $this->info("Found 'ancien num police' column as: '" . $headers[$ancienColKey] . "' (index $ancienColKey).");
        $this->info("Found 'new num police' column as: '" . $headers[$nouveauColKey] . "' (index $nouveauColKey).");

        $policeMapping = [];
        $rowCount = 0;
        $processedCount = 0;
        $skippedCount = 0;

        foreach ($dataRows as $row) {
            $rowCount++;
            if ($row === null) { // Check if row is null (e.g. completely empty row)
                $this->warn("Skipping empty row number: $rowCount");
                $skippedCount++;
                continue;
            }
            
            // Ensure keys exist, otherwise it might be a sparse row
            $ancienNumRaw = $row[$ancienColKey] ?? null;
            $nouveauNumRaw = $row[$nouveauColKey] ?? null;

            $ancienNum = trim(strval($ancienNumRaw));
            $nouveauNum = trim(strval($nouveauNumRaw));

            if (!empty($ancienNum) && !empty($nouveauNum)) {
                if (isset($policeMapping[$ancienNum])) {
                    $this->warn("Duplicate ancien num police '{$ancienNum}'. Overwriting with new value '{$nouveauNum}'. Old value was '{$policeMapping[$ancienNum]}'. (Row $rowCount)");
                }
                $policeMapping[$ancienNum] = $nouveauNum;
                $processedCount++;
            } elseif (!empty($ancienNum)) {
                $this->warn("Row $rowCount: Ancien num police '{$ancienNum}' has an empty nouveau num police. Skipping this entry.");
                $skippedCount++;
            } else {
                // Both empty or ancienNum is empty, considered a skip
                $skippedCount++;
            }
        }

        if (empty($policeMapping)) {
            $this->warn("No valid data found to create the mapping array after processing $rowCount rows (processed $processedCount, skipped $skippedCount).");
        }
        
        // Ensure config directory exists
        if (!File::isDirectory(config_path())) {
            File::makeDirectory(config_path(), 0755, true, true);
        }

        $phpArrayString = "<?php\n\n// Generated by artisan generate:police-mapping\n// Source Excel: {$excelFileName}\n\nreturn " . var_export($policeMapping, true) . ";\n";

        try {
            File::put($outputFilePath, $phpArrayString);
            $this->info("Successfully generated PHP array at: " . $outputFilePath);
            $this->info($processedCount . " entries in the array. Total data rows scanned: " . $dataRows->count() . " (skipped $skippedCount rows).");
        } catch (\Exception $e) {
            $this->error("Error writing to output file '{$outputFilePath}': " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}