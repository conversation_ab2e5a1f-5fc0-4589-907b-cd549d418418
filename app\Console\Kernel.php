<?php

namespace App\Console;

use App\Jobs\calculClassement;
use App\Jobs\updateGelsAvoirs;
use App\Jobs\calculSyntheseProd;
use App\Jobs\FetchGmailMessages;
use App\Jobs\calculeStatHomePage;
use App\Jobs\calculEvalloSynthese;
use App\Jobs\checkTiersGelsAvoirs;
use App\Jobs\calculCommissionsCabinet;
use App\Jobs\ReaffectationAutomatique;
use App\Jobs\InitialisationDispatching;
use App\Jobs\calculCommissionsCabinetOld;
use App\Console\Commands\MigrateMotifData;
use App\Console\Commands\GeneratePoliceMappingArray;
use Illuminate\Console\Scheduling\Schedule;
use App\Jobs\Conference\SyncApizeeConferences;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        MigrateMotifData::class,
        GeneratePoliceMappingArray::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('queue:restart')->twiceDaily(7, 13);

        $schedule->job(new calculClassement)->twiceDaily(13, 19)->withoutOverlapping();
        $schedule->job(new calculSyntheseProd)->twiceDaily(13, 19)->withoutOverlapping();
        $schedule->job(new calculCommissionsCabinet)->twiceDaily(13, 19)->withoutOverlapping();
        $schedule->job(new FetchGmailMessages)->everyFourMinutes()->withoutOverlapping();
        
        $schedule->job(new ReaffectationAutomatique)
                ->weekdays()
                ->between('7:00', '19:00')
                ->everyTwoMinutes()
                ->withoutOverlapping();

        $schedule->job(new ReaffectationAutomatique)
                ->saturdays()
                ->between('8:00', '12:00')
                ->everyTwoMinutes()
                ->withoutOverlapping();

        $schedule->job(new calculeStatHomePage)->dailyAt('03:30')->withoutOverlapping();

        $schedule->job(new calculCommissionsCabinetOld)->twiceDaily(13, 19)->withoutOverlapping();
        
        $schedule->job(new InitialisationDispatching)->dailyAt('23:00')->withoutOverlapping();

        $schedule->job(new updateGelsAvoirs())->dailyAt('04:00')->withoutOverlapping();
        
        $schedule->job(new checkTiersGelsAvoirs())->monthlyOn(1, '04:15')->withoutOverlapping();

        $schedule->job(new calculEvalloSynthese())->dailyAt('04:30')->withoutOverlapping();

        $schedule->job(new SyncApizeeConferences)->twiceDaily(14, 20)->withoutOverlapping();

        // $schedule->job(new DownloadPastConferencesMedia)->dailyAt('02:00')->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
