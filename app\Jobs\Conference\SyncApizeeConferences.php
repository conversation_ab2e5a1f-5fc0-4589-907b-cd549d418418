<?php

namespace App\Jobs\Conference;

use App\ApizeeConferences;
use App\ApizeeLogins;
use App\ApizeeService;
use App\Courtier;
use App\Statut;
use App\Societe;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use GuzzleHttp\Client;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Services\ApizeeAPIClient;
use App\Traits\ApizeeAuthTrait;
use App\Http\Controllers\ApizeeController;
use App\Http\Controllers\FonctionController;

class SyncApizeeConferences implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ApizeeAuthTrait;

    protected $data;
    protected $courtierId;
    protected $since;
    protected $until;

    public function __construct($data = null)
    {
        $this->data = $data;
        $this->courtierId = $data['courtierId'] ?? 1;
        $this->since = $data['since'] ?? Carbon::now()->setTimezone('Europe/Paris')->subDays(2)->startOfDay();
        $this->until = $data['until'] ?? null;
        // $this->until = $data['until'] ?? Carbon::now()->setTimezone('Europe/Paris')->addDays(7)->endOfDay();
        $this->onQueue('conference');
    }

    public function handle()
    {
        try {
            $this->setupCourtier();
            Log::channel('conferences')->info("#==============================  Début de la synchronisation téléconférences ===================================#");
            Log::channel('conferences')->info("Intervalle de dates : " . $this->since->format('d/m/Y H:i:s') . " à " . ($this->until ? $this->until->format('d/m/Y H:i:s') : '*NOW/LATER*'));

            // Get CRM conferences that need syncing
            $crmConferences = $this->getActiveCRMConferences();
            if ($crmConferences->isEmpty()) {
                Log::channel('conferences')->info('Aucune téléconférence active à synchroniser');
                return;
            }

            // Get valid auth token
            // Choose Admin user for auth job
            $apizeeController = app()->make(ApizeeController::class);
            $AdminApizeeUserId = $apizeeController->getAdminApizeeUserId($this->courtierId);
            $apizeeLogin = $apizeeController->getApizeeAuthCred($AdminApizeeUserId);
            if (!$apizeeLogin) {
                Log::channel('conferences')->error('Aucun token téléconférence valide trouvé');
                return;
            }

            // Fetch all conferences from Apizee in bulk
            $apizeeConferences = $this->fetchAllApizeeConferences($apizeeLogin);
            // Log::channel('conferences')->info("Détails des fetched téléconférences platforme Apizee:", $apizeeConferences->toArray());
            
            // Process each CRM conference
            foreach ($crmConferences as $conference) {
                try {
                    $this->processSingleConference($conference, $apizeeConferences, $apizeeLogin);
                } catch (\Exception $e) {
                    Log::channel('conferences')->error("Error processing conference", [
                        'conference_id' => $conference->id,
                        'conference_name' => $conference->name,
                        'message' => $e->getMessage(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            Log::channel('conferences')->info("#==============================  Fin de la synchronisation téléconférences ===================================#");


        } catch (\Exception $e) {
            Log::channel('conferences')->error('Erreur dans le job de synchronisation téléconférences: ' . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(), 
                'line' => $e->getLine()
            ]);
        }
    }

    public function setupCourtier()
    {
        if ($this->data) {
            FonctionController::setConfigMySql2($this->courtierId);
        } else {
            $this->courtierId = Courtier::find(1)->id;    // PROD = 1  / LOCAL = 3
            FonctionController::setConfigMySql2($this->courtierId);
        }
    }

    public function getActiveCRMConferences()
    {
        $excludedStatuses = ['closed', 'archived', 'canceled'];
        $excludedStatusIds = Statut::whereIn('slug', $excludedStatuses)->pluck('id');

        return ApizeeConferences::whereNotNull('apizee_conference_id')
            ->whereNotIn('statut_id', $excludedStatusIds)
            ->where('start', '>=', $this->since)
            ->with(['statut', 'userCreator', 'affecteA'])
            ->get();
    }

    public function fetchAllApizeeConferences($auth)
    {
        try {
            $limit = 100; // 100 API maximum limit
            $conferences = collect();

            $apizeeController = app()->make(ApizeeController::class);
            
            // Get all Apizee services
            $serviceKeys = ApizeeService::pluck('key')
                ->unique()
                ->filter();

            // Fallback to default service key if no service found
            if ($serviceKeys->isEmpty()) {
                Log::channel('conferences')->warning('No valid téléconférence service keys found in any société');
                $defaultService = ApizeeService::where('is_default', true)->first();
                $serviceKeys = $defaultService ? [$defaultService->key] : null;
            }

            // Loop through each service key
            getApizeeConferencesApiCall:
            foreach ($serviceKeys as $serviceKey) {
                $offset = 0;
                
                do {
                    $queryParams = [
                        'since' => $this->since->format('Y-m-d\TH:i:s\Z'),
                        'limit' => $limit,
                        'offset' => $offset,
                        'service_key' => $serviceKey,
                        'moderator' => 'all' // Include all conferences
                    ];

                    // Add until parameter if set
                    if ($this->until) {
                        $queryParams['until'] = $this->until->format('Y-m-d\TH:i:s\Z');
                    }
                    
                    $apiClient = app()->make(ApizeeAPIClient::class);
                    $response = $apiClient->makeRequest('GET', 'conferences', [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $auth->access_token,
                            'Content-Type' => 'application/json'
                        ],
                        'query' => $queryParams,
                    ]);

                    $result = json_decode($response->getBody(), true);

                    if (!isset($result['data'])) {
                        throw new \Exception("Format de réponse API invalide");
                    }

                    $conferences = $conferences->concat($result['data']);
                    $offset = $result['next_offset'] ?? null;

                    Log::channel('conferences')->info(
                        "Récupération des téléconférences - Page traitée",
                        [
                            'offset' => $offset,
                            'count' => count($result['data']),
                            'total' => $result['total'] ?? 0,
                            'service_key' => $serviceKey
                        ]
                    );
                } while ($offset !== null);
            }

            return $conferences->keyBy('id');
            
        } catch (\Exception $e) {
            try {
                $this->handleApiException($e, $auth);
                $auth->refresh();
                goto getApizeeConferencesApiCall;
            } catch (\Exception $apiException) {
                throw $apiException;
            } 
        }
    }

    public function processSingleConference($conference, $apizeeConferences, $apizeeLogin)
    {
        // Log::channel('conferences')->info("Debut processSingleConference FUNC: " . $conference->name);

        // Find matching Apizee conference
        $apizeeData = $apizeeConferences->get($conference->apizee_conference_id);
        
        $conferenceDate = Carbon::createFromFormat('d/m/Y H:i', $conference->start)->setTimezone('Europe/Paris');
        $fetchWindowStart = $this->since;

        if (!$apizeeData) {
            Log::channel('conferences')->warning("La téléconférence ID: {$conference->id}, Nom: {$conference->name} n'a pas été trouvée dans les données Apizee");

            // Check if conference start date is within the fetch window
            if ($conferenceDate->gt($fetchWindowStart)) {
                // Double-check directly with API before marking as missing
                try {
                    $apiClient = app()->make(ApizeeAPIClient::class);
                    $response = $apiClient->makeRequest('GET', "conferences/{$conference->apizee_conference_id}", [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $apizeeLogin->access_token,
                            'Content-Type' => 'application/json'
                        ]
                    ]);

                    $directCheck = json_decode($response->getBody(), true);
                    if (!$directCheck) {
                        $this->handleMissingConference($conference);
                    }else{
                        Log::channel('conferences')->warning("La téléconférence ID: {$conference->id}, Nom: {$conference->name}, Service ID: {$conference->apizee_service_id} a été trouvée dans direct request check platforme Apizee");
                    }
                } catch (\Exception $e) {
                    if ($e instanceof \GuzzleHttp\Exception\ClientException && ($e->getResponse()->getStatusCode() == 404 || $e->getResponse()->getStatusCode() == 400) ) {
                        $this->handleMissingConference($conference);
                    } else {
                        Log::channel('conferences')->error(
                            "Error checking conference ID: {$conference->id}, Name: {$conference->name}: " . $e->getMessage(),
                            [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                                'line' => $e->getLine()
                            ]
                        );
                    }
                }
            }else{
                Log::channel('conferences')->warning("*NOT within the fetch window Conference ID: {$conference->id}, Name: {$conference->name}");
            }
            return;
        }

        // Clone for change tracking
        $conferenceClone = $conference->replicate();

        // Map user IDs and prepare request
        $userIds = $this->mapUsersIdsApizeeToCRM($apizeeData, $conferenceClone);
        $request = $this->prepareUpdateRequest($apizeeData, $conferenceClone);

        // Track changes
        $apizeeController = app()->make(ApizeeController::class);
        $traces = $apizeeController->trackConferenceChanges(
            $conferenceClone,
            $request,
            $apizeeData,
            $userIds['CRMAffectedId'],
            $userIds['CRMInvitedById']
        );

        if (!empty($traces)) {
            $this->updateConferenceAndNotify($conference, $request, $userIds, $apizeeData, $traces);
        }
    }

    public function handleMissingConference($conference)
    {
        $canceledStatus = Statut::where('slug', 'canceled')->first();
        if ($canceledStatus) {
            // Track status change
            $traces = [[
                'field' => 'Statut',
                'old' => $conference->statut->libelle,
                'new' => $canceledStatus->libelle
            ]];
            $apizeeController = app()->make(ApizeeController::class);
            $apizeeController->createNotificationAndTrace(
                $conference, $conference->cree_par_id, 'update-auto', $traces, new Request(), true
            );
            $conference->update(['statut_id' => $canceledStatus->id]);
            Log::channel('conferences')->warning(
                "La téléconférence {$conference->apizee_conference_id} n'a pas été trouvée dans plateforme. Elle a été marquée comme annulée."
            );
        }
    }

    public function prepareUpdateRequest($apizeeData, $conference)
    {
        // Log::channel('conferences')->info("Téléconférence Platform conference data for " . $conference->id . ": " . json_encode($apizeeData));
        $startTime = Carbon::createFromFormat('Y-m-d\TH:i:sP', $apizeeData['start_time'])
            ->setTimezone('Europe/Paris');

        return new Request([
            'name' => $apizeeData['name'],
            'start' => $startTime->format('d/m/Y H:i'),
            'duration' => $this->getDurationFromTimes($apizeeData),
            'reminder' => $conference->reminder,
            'description' => $conference->description,
            'statut_id' => $this->getStatusId($apizeeData['status'])
        ]);
    }

    public function updateConferenceAndNotify($conference, $request, $userIds, $apizeeData, $traces)
    {
        $startTime = Carbon::createFromFormat('Y-m-d\TH:i:sP', $apizeeData['start_time'])
            ->setTimezone('Europe/Paris')
            ->toDateTimeString();

        // Update conference
        $apizeeController = app()->make(ApizeeController::class);
        $apizeeController->updateLocalConference(
            $conference,
            $request,
            $userIds['CRMAffectedId'],
            $userIds['CRMInvitedById'],
            $startTime,
            $apizeeData
        );

        // Create notification
        $apizeeController->createNotificationAndTrace(
            $conference,
            $userIds['CRMInvitedById'],
            'update-auto',
            $traces,
            $request,
            true
        );

        Log::channel('conferences')->info("Synchronisation réussie de la téléconférence ID: '{$conference->id}', 
        conference nom: '{$conference->name}', Modifications traces: " . json_encode($traces));

    }

    // Existing helper methods remain unchanged
    public function mapUsersIdsApizeeToCRM($apizeeData, $oldConference)
    {
        try {
            $apizeeAffectedId = $apizeeData['moderator']['id'] ?? null;
            $apizeeInvitedById = $apizeeData['created_by']['id'] ?? null;

            // Get all active logins
            $apizeeLogins = ApizeeLogins::where('active', 1)
                ->where(function($query) {
                    $query->whereNotNull('apizee_user_id')
                        ->where('apizee_user_id', '!=', 0);
                })
                ->whereIn('apizee_user_id', array_filter([$apizeeAffectedId, $apizeeInvitedById]))
                ->pluck('user_id', 'apizee_user_id');

            // If no mapping found, fallback to existing IDs
            return [
                'CRMAffectedId' => $apizeeLogins[$apizeeAffectedId] ?? $oldConference->user_id,
                'CRMInvitedById' => $apizeeLogins[$apizeeInvitedById] ?? $oldConference->cree_par_id
            ];
        } catch (\Exception $e) {
            Log::channel('conferences')->error("Erreur lors de la correspondance des utilisateurs plateforme vers CRM: " . $e->getMessage(), [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'line' => $e->getLine()
                ]);
            // Fallback to existing IDs in case of error
            return [
                'CRMAffectedId' => $oldConference->user_id,
                'CRMInvitedById' => $oldConference->cree_par_id
            ];
        }
    }

    public function getStatusId($status)
    {
        return Statut::where('slug', $status)
            ->first()
            ->id ?? Statut::where('slug', 'scheduled')
            ->first()
            ->id;
    }

    public function getDurationFromTimes($apizeeData)
    {
        $start = Carbon::parse($apizeeData['start_time']);
        $end = Carbon::parse($apizeeData['end_time']);
        return $end->diffInMinutes($start);
    }
}