<?php

namespace App;

use App\Traits\HashidsTrait;
use App\Traits\FilterByHolidayTrait;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\Relations\Relation;


//Morphmap
Relation::morphMap([
    'Equipe' => 'App\Equipe',
    'Societe' => 'App\Societe',
    'Site' => 'App\Site',
    'Service' => 'App\Service'
]);

class Devis extends Model
{
    use HashidsTrait, FilterByHolidayTrait;

    protected $connection = 'mysql2';

    public function nomDatabase()
    {
        return Config::get('database.connections.mysql2.database');
    }

    public function fiche()
    {
        return $this->belongsTo('App\Fiche', 'num_fiche', 'num_fiche');
    }
    
    public function contrat()
    {
        return $this->hasOne('App\Contrat');
    }

    public function devisLR()
    {
        return $this->hasMany('App\DevisLR');
    }

    public function modePaiement()
    {
        return $this->belongsTo('App\Modepaiement', 'modepaiements_id');
    }

    public function typePaiement()
    {
        return $this->belongsTo('App\Typepaiement', 'typepaiements_id');
    }

    public function infosBancairesPrelevement()
    {
        return $this->belongsTo('App\Informationbancaire', 'infobancaires_pre_id');
    }

    public function infosBancairesRemboursement()
    {
        return $this->belongsTo('App\Informationbancaire', 'infobancaires_rem_id');
    }
    
    public function tiers()
    {
        return $this->belongsTo('App\Tiers');
    }

    public function typefiche()
    {
        return $this->belongsTo('App\Typefiche','typefiche_id');
    }

    public function cible()
    {
        return $this->belongsTo('App\Cible','cible_id');
    }

    public function statut()
    {
        return $this->belongsTo('App\Statut');
    }

    public function user()
    {
        return $this->belongsTo('App\User');
    }

    public function devisSante()
    {
        return $this->belongsTo('App\Devis', 'devis_origin');
    }

    public function reglements()
    {
        return $this->hasMany('App\Reglement');
    }

    public function getDevisCible()
    {
        $formule = json_decode($this->formule, true);
        $gamme = Gamme::where('id', $formule['idGamme'])->first();
        $foundCibleId = null;

        $defaultGammeCible = $gamme->gammeCible()->whereNotNull('default_cible_id')->first();

        if ($defaultGammeCible) { // Cible by Default
            $foundCibleId = $defaultGammeCible->default_cible_id;
        } else { //else check gammes_cibles 'Régles'
            $gammeCibles = GammeCible::where('gamme_id',  $gamme->id)->orderBy('cible_id')->where('active', 1)->get();
            if (count($gammeCibles)) {
                foreach ($gammeCibles as $gammeCible) { //check every 'régles' if one of them true : get cible_id 
                    if ($gammeCible->getCible($this, $formule)) {
                        $foundCibleId = $gammeCible->cible_id;
                        break;
                    }
                }
            } else {
                throw ValidationException::withMessages(['error' => "la gamme " . $gamme->libelle . " n'a pas de configuration des cibles."]);
            }
        }
 
        return $foundCibleId;
    }

    public function vehicule() {
        return (Vehicule::find($this->vehicule_id));  
    }
    
    public function vehicule2() {
        return $this->belongsTo('App\Vehicule', 'vehicule_id');   
    }

    public function animauxCompagnie() {
        return $this->belongsTo('App\AnimauxCompanie', 'animal_id');   
    }

    public function entitable() {
        return $this->morphTo();
    }
    /**
     * reusable scope for date filtering
     */
    public function scopeFilterByDateRange($query, $startdate, $enddate, $dateField)
    {
        if ($startdate && $enddate) {
            $query->whereBetween($dateField, [$startdate, $enddate]);
        } elseif ($startdate) {
            $query->where($dateField, '>=', $startdate);
        } elseif ($enddate) {
            $query->where($dateField, '<=', $enddate);
        }

        return $query;
    }

    public function saveFormuleValues($formule)
    {
        $formule = (array) $formule;
        $this->garantie_id = $formule['id'] ?? null;
        $this->tarif = isset($formule['tarif']) ? $formule['tarif'] + 0 : null;
        $this->taux_commission_ws = $formule['tauxCommission'] ?? null;
        $this->pack_tarif = isset($formule['packTarif']) ? $formule['packTarif'] + 0 : null;
        $this->tarif_initial = isset($formule['tarifInitial']) ? $formule['tarifInitial'] + 0 : null;
        $this->tarif_total = isset($formule['tarifTotal']) ? $formule['tarifTotal'] + 0 : null;
        $this->renforts_tarif = isset($formule['renfortsTarif']) ? $formule['renfortsTarif'] + 0 : null;
        $this->franchises_tarif = isset($formule['franchisesTarif']) ? $formule['franchisesTarif'] + 0 : null;
        $this->reduction = isset($formule['reduction']) ? $formule['reduction'] + 0 : null;

        $this->save();
    }
}
