<?php

namespace App\Traits;

use App\Holiday;
use App\Parametrage;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
trait FilterByHolidayTrait
{
    /**
     * Scope a query to only include records created on an active holiday.
     * param $date as string 'Y-m-d'
     */
    public function scopeFilterByHoliday($query, $date = null, $column = 'null')
    {
        

        // Check if holiday filter is active in parametrages table
        $isActive = Parametrage::where('slug', 'holiday_filter_active')->where('active', 1)->value('valeur');
        
        // Check user profils
        $user = Auth::user();

        if (!$user->isConseiller() && !$user->isAnimateur() && !$user->isQualificateur() && !$user->isPrdv()) {
            return $query;
        }elseif (!$isActive) {
            return $query;
        }else{
            $dateToCheck = $date ? Carbon::parse($date) : Carbon::today();
            $column = $column == 'null' ? 'created_at' : $column;
            $isHoliday = Holiday::where('date', $dateToCheck->toDateString())
                                ->where('active', 1)
                                ->exists();
    
            if ($isHoliday) {
                return $query->whereDate($this->getTable() . '.' . $column, $dateToCheck->toDateString());
            }
            
            return $query;
        }
    }
}