<!DOCTYPE html>
<html>
<head>
	<title>contrats</title>
</head>
<body>
<table class="table table-bordered">
			<thead>
				<tr>
					<td>
						Num police
					</td>
					<td>
						Num contrat
					</td>
					<td>
						CAC
					</td>
                    <td>
                        Commission cabinet
                    </td>
					<td>
						Statut
					</td>
					<td>
						Civilite assur
					</td>
					<td>
						Nom assur
					</td>
					<td>
						Prenom assur
					</td>
					<td>
						Profession
					</td>
                    @if($brancheID != 3 && $brancheID != '8' && $brancheID != '33' && $brancheID != '17')
                        <td>
                            Civilite conjoint
                        </td>
                        <td>
                            Nom conjoint
                        </td>
                        <td>
                            Prenom conjoint
                        </td>
                        <td>
                            Date naissance conjoint
                        </td>
                        <td>
                            Sexe enfant
                        </td>
                        <td>
                            Nom enfant
                        </td>
                        <td>
                            Prenom enfant
                        </td>
                        <td>
                            Date naissance enfant
                        </td>
                    @endif
					<td>
						Adresse assur
					</td>
					<td>
						Code postale
					</td>
					<td>
						Ville
					</td>
					<td>
						Telephone
					</td>
					<td>
						Telephone mobile
					</td>
					<td>
						Type fiche
					</td>
					<td>
						Compagnie
					</td>
					<td>
						Gamme
					</td>
					<td>
						Libelle formule
					</td>
					<td>
						Date d'effet
                    </td>
					<td>
						Date validation production
                    </td>
                    <td>
						Date validation contrat
                    </td>
                    <td>
						Date création contrat
                    </td>
					<td>
						Date naissance
                    </td>
                    
                    @if($brancheID != 3 && $brancheID != '8' && $brancheID != '33' && $brancheID != '17')
                        <td>
                            Regime
                        </td>
                    @endif
                    
					<td>
						Statut adhesion
					</td>
					<td>
						Cotisation
					</td>
					<td>
						Email assure
					</td>
					<td>
						Prestataire
					</td>
					<td>
						Groupe Publicitaire
					</td>
					<td>
						Societe
					</td>
					<td>
						Site
					</td>
					<td>
						Equipe
					</td>
					<td>
						Conseiller
					</td>
					<td>
						Validation devis
					</td>
					<td>
						Validation contrats
					</td>
					<td>
						Date validation devis
					</td>

					<td>
						Date annulation
					</td>

					<td>
						Date résiliation
					</td>
					<td>
						Motif
					</td>
                    
                    @if($brancheID == 3 || $brancheID == '8' || $brancheID == '33' || $brancheID == '17')
                        <td>
                            Id assurnet
                        </td>
                        <td>
                            Prorata
                        </td>
                        <td>
                            Frais Dossier
                        </td>
                        <td>
                            Montant Global
                        </td>
                        <td>
                            Reste a Payer
                        </td>
                        <td>
                            Nbre Reglement
                        </td>
                        <td>
                            PR_DerogMontantP
                        </td>
                        <td>
                            PR_DerogDateP
                        </td>
                        <td>
                            Observation Validation PROD
                        </td>
                        <td>
                            Etat Pre Enregistrement
                        </td>
                        <td>
                            PR_MotifFermer
                        </td>
                        <td>
                            Valid Contrat
                        </td>

                        <td>
                            Num Permis
                        </td>
                        <td>
                            Type Permis
                        </td>
                        <td>
                            Date Permis
                        </td>
                        <td>
                            Lieu Permis
                        </td>

                        <td>
                            Modele Vehicule
                        </td>
                        <td>
                            Immatriculation
                        </td>
                        <td>
                            Code sra
                        </td>
                        

                        <td>
                            Date Depos Doss Anim
                        </td>
                        <td>
                            Etat Depos Doss Anim
                        </td>
                        <td>
                            Obs Depos Doss Anim	
                        </td>
                        <td>
                            Date Depos Doss Gest
                        </td>
                        <td>
                            Etat Depos Doss Gest
                        </td>
                        <td>
                            Obs Depos Doss Gest	
                        </td>
                        <td>
                            Date Envoi Doc Clt
                        </td>
                        <td>
                            PR_HeureFermuture
                        </td>
                        <td>
                            Situation Reglement
                        </td>
                    @endif

				</tr>
			</thead>
			<tbody>
				<pre>
					
				</pre>
			        @foreach ($contrats as $contrat)
			         	<tr>
							<td >
								{{$contrat->num_police}}
						   </td>
							<td >
								{{$contrat->devis->num_devis}}
						   </td>
						   <td >
								{{$contrat->cad}}
						   </td>
                           <td >
                                {{$contrat->commission}}
                           </td>
							<td >
                                @if ($contrat->statut)
								    {{$contrat->statut->libelle}}
                                @else 
                                    N/A
                                @endif
						   </td>
							<td >
								{{$contrat->fiche->tiers->dpp->titre}}
						   </td>
							<td >
								{{$contrat->fiche->tiers->dpp->nom}}
						   </td>
							<td >
								{{$contrat->fiche->tiers->dpp->prenom}}
						   </td>
						   	@if($contrat->fiche->tiers->dpp->profession)
                            <td>
                                {{$contrat->fiche->tiers->dpp->profession->libelle}}
                            </td>
                            @else
                            	<td> </td>
                            @endif

                            @if($brancheID != 3 && $brancheID != '8' && $brancheID != '33' && $brancheID != '17')
                                <td >
                                    @if(is_object($contrat->fiche->listBeneficiaires['conjoint']))
                                        {{$contrat->fiche->listBeneficiaires['conjoint']->civilite}}
                                    @endif
                                </td>
                                    <td >
                                        @if(is_object($contrat->fiche->listBeneficiaires['conjoint']))
                                            {{$contrat->fiche->listBeneficiaires['conjoint']->nom}}
                                        @endif
                                </td>
                                    <td >
                                        @if(is_object($contrat->fiche->listBeneficiaires['conjoint']))
                                            {{$contrat->fiche->listBeneficiaires['conjoint']->prenom}}
                                        @endif
                                </td>
                                    <td >
                                        @if(is_object($contrat->fiche->listBeneficiaires['conjoint']))
                                            {{date('Y-m-d', strtotime($contrat->fiche->listBeneficiaires['conjoint']->date_naissance))}}
                                        @endif
                                </td>
                                @if(count($contrat->fiche->listBeneficiaires['enfants']) >0)
                                    <td>
                                        @foreach($contrat->fiche->listBeneficiaires['enfants'] as $enfant)	
                                            {{$enfant->sexe}} ,
                                        @endforeach
                                    </td>
                                    <td>
                                        @foreach($contrat->fiche->listBeneficiaires['enfants'] as $enfant)	
                                            {{$enfant->nom}} ,
                                        @endforeach
                                    </td>
                                    <td>
                                        @foreach($contrat->fiche->listBeneficiaires['enfants'] as $enfant)	
                                            {{$enfant->prenom}} ,
                                        @endforeach
                                    </td>
                                    <td>
                                        @foreach($contrat->fiche->listBeneficiaires['enfants'] as $enfant)	
                                            {{date('Y-m-d', strtotime($enfant->date_naissance))}} ,
                                        @endforeach
                                    </td>
                                @else
                                    <td >
                                    </td>
                                    <td >
                                    </td>
                                    <td >
                                    </td>
                                    <td >
                                    </td>
                                @endif
                            @endif
                            
                            <td >
                                {{$contrat->fiche->tiers->adresse1}} {{$contrat->fiche->tiers->adresse2}}
                            </td>
                            <td >
                                {{$contrat->fiche->tiers->code_postal}}
                            </td>
                            <td >
                                {{$contrat->fiche->tiers->ville}}
                            </td>
                            <td >
                                {{$contrat->fiche->numero_tel()}}
                            </td>
                            <td >
                                {{$contrat->fiche->tiers->portable}}
                            </td>
                            <td>
                                @if ($contrat->devis->typefiche)
                                    {{$contrat->devis->typefiche['libelle']}}
                                @else 
                                    --------------
                                @endif
							</td>
                            <td >
                                {{$contrat['formule']['compagnie'] ?? ''}}
                            </td>
                            <td >
                                {{$contrat['formule']['gamme'] ?? ''}}
                            </td>
                            <td >
                                {{$contrat['formule']['libelle'] ?? ''}}
                            </td>
                            <td >
                                {{date('Y-m-d', strtotime($contrat->devis->date_effet))}}
                            </td>
                            <td >
                                {{$contrat->devis->date_validation}}
                            </td>
                            <td >
                                {{$contrat->date_validation}}
                            </td>
                            <td >
                                {{$contrat->created_at}}
                            </td>
                            <td >
                                {{date('Y-m-d', strtotime($contrat->fiche->tiers->dpp->date_naissance))}}
                            </td>
                            
                            @if($brancheID != 3 && $brancheID != '8' && $brancheID != '33' && $brancheID != '17')

                                @if($contrat->fiche->tiers->dpp->regime)
                                    <td>
                                        {{$contrat->fiche->tiers->dpp->regime->libelle}}
                                    </td>
                                @else
                                    <td> </td>
                                @endif 
                            @endif

                            <td >
                                E
                            </td>
                            <td >
                                {{$contrat['formule']['tarifTotal'] ?? ''}}
                            </td>
                            <td >
                                {{$contrat->fiche->tiers->email}}
                            </td>
                            <td >
                                {{$contrat->affecte_a->prestataire_nom}}
                            </td>

                            <td >
                                {{$contrat->fiche->groupepub}}
                            </td>
                            <td >
                                {{$contrat->affecte_a->societe_nom}}
                            </td>
                            <td >
                                {{$contrat->affecte_a->site_nom}}
                            </td>
                            <td >
                                {{$contrat->affecte_a->equipe_nom}}
                            </td>
                            <td >
                                {{$contrat->affecte_a->nom}} {{$contrat->affecte_a->prenom}}
                            </td>
                            <td >
								@if($contrat->traces['validD'] && $contrat->traces['validD']['nom'] && $contrat->traces['validD']['prenom'])
                                    {{$contrat->traces['validD']['nom']}} {{$contrat->traces['validD']['prenom']}}
								@else
									---
                                @endif
                            </td>
                            <td >
								@if($contrat->traces['validC'] && $contrat->traces['validC']['nom'] && $contrat->traces['validC']['prenom'])
                                    {{$contrat->traces['validC']['nom']}} {{$contrat->traces['validC']['prenom']}}
								@else
									---
                                @endif
                            </td>
							<td >				
                                {{date('Y-m-d', strtotime($contrat->devis->date_validation))}}
                            </td>

                            
                            <td >
                                @if($contrat->date_annulation)
                                    {{Carbon\Carbon::createFromFormat('d/m/Y', $contrat->date_annulation)->format('Y-m-d')}}
								@else
									---
                                @endif			
                            </td>
                            <td >
                                @if($contrat->date_resiliation)
                                    {{Carbon\Carbon::createFromFormat('d/m/Y', $contrat->date_resiliation)->format('Y-m-d')}}
								@else
									---
                                @endif	
                            </td>
                            <td >				
                                @if($contrat->statutAction && $contrat->statutAction->motifsLibelle)
                                    {{$contrat->statutAction->motifsLibelle}}
								@else
									---
                                @endif
                            </td>

                            @if($brancheID == 3 || $brancheID == '8' || $brancheID == '33' || $brancheID == '17')
                                <td>
                                    @if($contrat->fiche->assurnet_id)
                                        {{$contrat->fiche->assurnet_id}}
                                    @endif
                                </td>
                                <td>
                                    @if($contrat->devis->prorata)
                                        {{$contrat->devis->prorata}}
                                    @endif
                                </td>
                                <td>
                                    @if($contrat->devis->frais_dossier)
                                        {{$contrat->devis->frais_dossier}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->montantGlobal)
                                        {{$contrat->montantGlobal}}
                                    @endif
                                </td>


                                <td>
                                    @if($contrat->devis->reste_a_payer)
                                        {{$contrat->devis->reste_a_payer}}
                                    @endif
                                </td>
                                
                                <td>
                                    @if($contrat->reglementsCount)
                                        {{$contrat->reglementsCount}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- PR_DerogMontantP -->
                                </td>
                                
                                <td>
                                    <!-- PR_DerogDateP -->
                                </td>
                                
                                <td>
                                    <!-- Observation Validation PROD -->
                                </td>
                                
                                <td>
                                    <!-- Etat Pre Enregistrement -->
                                </td>
                                
                                <td>
                                    <!-- PR_MotifFermer -->
                                    @if($contrat->statutLibelle)
                                        {{$contrat->statutLibelle}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Valid Contrat -->
                                </td>

                                <td>
                                    @if($contrat->dpp->num_permis)
                                        {{$contrat->dpp->num_permis}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->permis)
                                        {{$contrat->permis->type->libelle}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->permis)
                                        {{$contrat->permis->date}}
                                    @endif
                                </td>
                                
                                <td>
                                    @if($contrat->dpp->num_permis)
                                        {{$contrat->dpp->lieu_permis}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->vehicule)
                                        {{$contrat->vehicule->model}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->vehicule)
                                        {{$contrat->vehicule->immatriculation}}
                                    @endif
                                </td>

                                <td>
                                    @if($contrat->vehicule)
                                        {{$contrat->vehicule->code_sra}}
                                    @endif
                                </td>
                                

                                
                                <td>
                                    <!-- Date Depos Doss Anim -->
                                    @if($contrat->lastValidationDossierAnim)
                                        {{$contrat->lastValidationDossierAnim}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Etat Depos Doss Anim -->
                                    @if($contrat->etatValidationDossierAnim)
                                        {{$contrat->etatValidationDossierAnim}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Obs Depos Doss Anim -->
                                    @if($contrat->validationDossierAnimComplement)
                                        {{$contrat->validationDossierAnimComplement}}
                                    @endif
                                </td>

                                <td>
                                    <!-- Date Depos Doss Gest -->
                                    @if($contrat->lastValidationDossierGest)
                                        {{$contrat->lastValidationDossierGest}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Etat Depos Doss Gest -->
                                    @if($contrat->etatValidationDossierGest)
                                        {{$contrat->etatValidationDossierGest}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Obs Depos Doss Gest -->
                                    @if($contrat->validationDossierGestComplement)
                                        {{$contrat->validationDossierGestComplement}}
                                    @endif
                                </td>
                                
                                <td>
                                    <!-- Date Envoi Doc Clt -->
                                    {{date('Y-m-d', strtotime($contrat->devis->date_validation))}}
                                </td>
                                
                                <td>
                                    <!-- PR_HeureFermuture -->
                                </td>
                                
                                <td>
                                    <!-- Situation Reglement -->
                                    @if($contrat->situationReglement)
                                        {{$contrat->situationReglement}}
                                    @endif
                                </td>
                            @endif
					  </tr>
					@endforeach	
			</tbody>
		</table>
</body>
</html>