<?php
namespace App\Http\Controllers;

use App\Fam;
use App\Dpps;
use App\Lead;
use App\Site;
use App\User;
use DateTime;
use App\Devis;
use App\Fiche;
use App\Gamme;
use App\Motif;
use App\Tiers;
use Exception;
use App\Equipe;
use App\Profil;
use App\Regime;
use App\Statut;
use App\Branche;
use App\Contrat;
use App\Societe;
use App\Garantie;
use App\Vehicule;
use App\Compagnie;
use App\Groupepub;
use App\ModelMail;
use App\Reglement;
use App\Typefiche;
use App\Typepermi;
use Carbon\Carbon;
use App\ColumnsMaj;
use App\EquipeUser;
use App\MappingMaj;
use App\Permission;
use App\Provenance;
use App\Groupemotif;
use App\Proposition;
use App\Groupestatut;
use App\Modepaiement;
use App\Notification;
use App\Statutaction;
use App\Typepaiement;
use App\Groupebranche;
use App\TypePermisDpp;
use App\GarantieLibelle;
use App\Rules\DateValidCC;
use App\Jobs\DispatchFiche;
use App\SituationFamiliale;
use Illuminate\Support\Env;
use Illuminate\Support\Str;
use App\GroupepubProvenance;
use App\TraceContratCompany;
use Hamcrest\Arrays\IsArray;
use Illuminate\Http\Request;
use App\importfiles_reglement;
use Illuminate\Support\Fluent;
use App\Exports\ArrayDataExport;
use App\Events\EventTraceMatieres;
use Illuminate\Support\Facades\DB;
use App\importfiles_contratcompany;
use Illuminate\Support\Facades\Log;
use Vinkla\Hashids\Facades\Hashids;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use PhpParser\Node\Expr\Cast\Array_;
use App\Http\Resources\TiersResource;
// use Illuminate\Validation\Rule;
use App\Events\EventNotificationUsers;
use App\Traits\MajContratErrorHandler;
use App\Http\Resources\ContratResource;
use Illuminate\Support\Facades\Storage;
use App\Events\EventTracesContratCompany;
use App\Http\Controllers\TiersController;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use App\Http\Controllers\ContratController;
use App\Http\Controllers\FonctionController;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\PubliqueFicheController;
use Symfony\Component\HttpKernel\Exception\HttpException;

class UpdateContratCompanyController extends Controller
{
    protected $uploadMode = 'local';        // local or server
    protected $fileLimit = 500;
    protected $contratsCreationLimit = 50;

    use MajContratErrorHandler;

    public function loadSoc(){
        $user = Auth::user();
		FonctionController::setConfigMySql2($user->courtier_id);
        $animateurs = [];
		$conseillers = [];
		$equipes = [];
        $sites = [];
		$societes = [];

        $conseillersProfilIds = [
            Profil::CONSEILLER,
            Profil::COMMERCIAL_TERRAIN, 
            Profil::FIDELISATION, 
            Profil::FIDELISATION_AUTO, 
            Profil::PRDV,
            Profil::QUALIFICATEUR
        ];
        
        $societes = Societe::all(); 
        $sites = Site::all();
        $equipes = Equipe::where('active', 1)->get();
        $equipeIds = collect($equipes)->pluck('id'); 
        $usersEquipes = EquipeUser::whereIn('equipe_id', $equipeIds)->pluck('user_id');
        $conseillers = User::whereIn('id', $usersEquipes)->whereIn('profil_id', $conseillersProfilIds)->where('active', 1)->get();
        $animateurs = User::whereIn('id', $usersEquipes)->where('profil_id', Profil::ANIMATEUR)->where('active', 1)->get();
        foreach ($animateurs as $animateur) {
            $animateur['equipe_id'] = EquipeUser::where('user_id', $animateur->id)->where('active', 1)->where('deleted', null)->get()->pluck('equipe_id');
            $animateur['username'] = FonctionController::realUsername($animateur->username);
        }

        foreach ($conseillers as $conseiller) {
            $conseiller['equipe_id'] = EquipeUser::where('user_id', $conseiller->id)->where('deleted', null)->get()->pluck('equipe_id');
            $conseiller['nom_complet'] = $conseiller['nom'].' '.$conseiller['prenom'];
            $conseiller['username'] = FonctionController::realUsername($conseiller->username);
        }

        // Get all compagnies from crm
        $companies = Compagnie::get();
        $regimes = Regime::get();
        $sfs = SituationFamiliale::get();
        $requiredColumnsSante = ColumnsMaj::where('required', 1)->where('type', 'sante')->pluck('libelle')->toArray();
        $requiredColumnsAuto = ColumnsMaj::where('required', 1)->where('type', 'auto')->pluck('libelle')->toArray();
        $requiredColumnsReg = ColumnsMaj::where('required', 1)->where('type', 'reg')->pluck('libelle')->toArray();
        $requiredColumnsImp = ColumnsMaj::where('required', 1)->where('type', 'imp')->pluck('libelle')->toArray();
        $requiredColumnsAnn = ColumnsMaj::where('required', 1)->where('type', 'annulation')->pluck('libelle')->toArray();
        $requiredColumnsRV = ColumnsMaj::where('required', 1)->where('type', 'remiseVigueur')->pluck('libelle')->toArray();
        $grpMotifIdImpayé = Groupemotif::where('slug', 'impaye')->first()->id;
        $motifsImpaye = Motif::where('groupemotif_id', $grpMotifIdImpayé)->get();
        $grpMotifIdAnn = Groupemotif::where('slug', 'annulation')->first()->id;
        $motifsAnn = Motif::where('groupemotif_id', $grpMotifIdAnn)->get();
        $grpMotifIdResil = Groupemotif::where('slug', 'resiliation')->first()->id;
        $motifsResil = Motif::where('groupemotif_id', $grpMotifIdResil)->get();
        $grpStatRegl = Groupestatut::where('slug', 'reglement')->first()->id;
        $statsRegl = Statut::where('groupestatut_id', $grpStatRegl)->get();
        $Typepaiements = Typepaiement::get();
        $modepaiements = Modepaiement::get();
        $Typepermis = Typepermi::get();

        return $data = [
			'societes' => $societes,
			'sites' => $sites,
            'equipes' => $equipes,
            'animateurs' => $animateurs,
            'conseillers' => $conseillers,
            'companies' => $companies,
            'fileLimit' => $this->fileLimit,
            'regimes' => $regimes,
            'sfs' => $sfs,
            'requiredColumnsSante' => $requiredColumnsSante,
            'requiredColumnsAuto' => $requiredColumnsAuto,
            'requiredColumnsReg' => $requiredColumnsReg,
            'requiredColumnsImp' => $requiredColumnsImp,
            'requiredColumnsAnn' => $requiredColumnsAnn,
            'requiredColumnsRV' => $requiredColumnsRV,
            'motifsImpaye' => $motifsImpaye,
            'motifsAnn' => $motifsAnn,
            'motifsResil' => $motifsResil,
            'statsRegl' => $statsRegl,
            'Typepaiements' => $Typepaiements,
            'modepaiements' => $modepaiements,
            'Typepermis' => $Typepermis,
        ];
    }

    
    public function portefeuilleImportIndex(Request $request, $type = 'en_cours')
    {
        if($type == 'en_cours'){
            return view('portefeuilles.index'); 
        }else{
            return view('portefeuilles.annuler'); 
        }
    }

    public function importPortefeuille(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        $importFileId = importfiles_contratcompany::max('id') + 1;
        $file = $request->file;
        $filename = $request->filename;

        $exploded = explode(",", $file);
        $explodedFilename = explode(".", $filename);

        $branche = Branche::find($request->branche_id);

        // Traitement du csv ou xls seulement
        if (str_contains($explodedFilename[1], 'csv')) {
            $ext = 'Csv';
        } else if (str_contains($explodedFilename[1], 'xslx')) {
            $ext = 'Xlsx';
        } else {
            $ext = 'Xls';
        }

        $decode = base64_decode($exploded[1]);
        $filename = str_replace(' ', '', "imported" . substr(time(), 5) . $request->filename);

        $filename_raw = explode('.', $filename);
        $original_filename_raw = explode('.', $request->filename);

        $folder = 'contrat_company';
        if($this->uploadMode == "server"){
            // Upload original file to server
            $pathServeur = FonctionController::saveFile($file, $filename, $folder);

            // fix windows backslash for paths issue
            $path = str_replace('\\', '/', storage_path("app/tmp/{$filename}"));
            //file_put_contents($path, $decode);
            Storage::put("tmp/$filename", $decode);
        }else{
            // Upload original file to local
            // fix windows backslash for paths issue
            $path = str_replace('\\', '/', public_path("upload/$folder/{$filename}"));
            Storage::disk('public2')->put("/$folder/$filename", $decode);
        }

       // $array = Excel::selectSheetsByIndex(0)->load($path, function ($reader) { $reader->formatDates(true, 'd/m/Y'); }, null, true)->get();
        if ($ext == "csv") {
            /*  $array = Excel::load($path, function ($reader) {
              $reader->formatDates(true, 'd/m/Y');
            })->get(); */

            $array = Excel::toCollection(null, $path, null, 'Csv');
        }
        /*
        $array = Excel::toCollection(null, $path, null, 'Xlsx', [
            'formatDates' => true,
            'sheet_index' => 0,
        ])->first();
        */
        $array = $this->processExcelFile($path);
        // Delete empty rows
        $this->deleteEmptyRows($array);

        // Prevent importation of files having > 500 contrats
        if($array->count() > $this->fileLimit){
            return response()->json('Le fichier ne doit pas contenir plus de '.$this->fileLimit.' contrats', 422);
        }

        // Get only assoc array of contrats value without heading for conversion and manipulation 
        $arrayToArray = $array->toArray();
        $convertedArray =  $this->convertFileStructure($request, $arrayToArray, $filename_raw, $filename, $folder, $branche->slug);

        // Load contratsConverted File with proper path
        if($this->uploadMode == "server"){
            $convertedContratsFilePath = "$ftpUrl/".$convertedArray['contratConvertedFilePath'];
            $convertedContratsFile = file_get_contents($convertedContratsFilePath);
            $explode_filename_converted = explode('/', $convertedArray['contratConvertedFilePath']);
            //$convertedContratsFilePath = public_path("tmp".DIRECTORY_SEPARATOR.$explode_filename_converted[count($explode_filename_converted)-1]);
            $convertedContratsFilePath = str_replace('\\', '/', public_path("upload/tmp/".$explode_filename_converted[count($explode_filename_converted)-1]));
            file_put_contents($convertedContratsFilePath, $convertedContratsFile);

           // dd("converted Array:", $convertedArray, "converted contrat file path", $convertedContratsFilePath );
        }else{

           // $convertedContratsFilePath = str_replace('\\', '/', public_path($convertedArray['contratConvertedFilePath']));
            $convertedContratsFilePath = str_replace('\\', '/', public_path($convertedArray['contratConvertedFilePath']));
        }

       // $converted_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) { $reader->formatDates(true, 'd/m/Y'); }, null, true)->get();

        /* $converted_contrats = Excel::toCollection(null, $convertedContratsFilePath, null, 'Xlsx', [
            'formatDates' => true,
            'sheet_index' => 0,
        ])->first(); */
        $converted_contrats = $this->processExcelFile($convertedContratsFilePath);

        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";
        if($this->uploadMode == "server"){
            $convertedContratsFilePathStored = $this->storeExcelFile(
                new ArrayDataExport($converted_contrats),
                "{$explode_filename_converted[count($explode_filename_converted)-1]}.xlsx",
                $storagePath
            );
            $convertedContratsFilePathStored = FonctionController::saveFile($convertedContratsFilePathStored, ("{$explode_filename_converted[count($explode_filename_converted)-1]}"), $folder);
           // dd($convertedContratsFilePathStored);
        } else {
            $explodedFolderConverted = explode(DIRECTORY_SEPARATOR, $convertedArray['contratConvertedFilePath']);
            $extractedFilename = $explodedFolderConverted[count($explodedFolderConverted)-1];
            $extractedFolder =  $explodedFolderConverted[count($explodedFolderConverted)-2];
           /* $fileNameConverted = str_replace("upload".DIRECTORY_SEPARATOR. "/", '', "{$explode_filename_converted[count($explode_filename_converted)-1]}");*/

            $convertedContratsFilePathStored = $this->storeExcelFile(
                new ArrayDataExport($converted_contrats),
                $extractedFilename,
                $storagePath,
                $extractedFolder
            );
        }


        //dd($converted_contrats, $convertedArray);
        // Validate existance columns
        if($branche->slug == 'automobile'){
            $validationColumns = $this->validationColumnsAuto($converted_contrats);
        }else{
            $validationColumns = $this->validationColumns($converted_contrats);
        }
        if (!empty($validationColumns)) {
            return response()->json([
                'message' => 'Il y a des colonnes manquantes',
                'missColumns' => implode(" / ",$validationColumns),
            ], 422);
        }

        // =============== [Old] point All contrat file to converted file ==========
        //$notFound_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        //$exist_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        //$incorrect_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) { $reader->formatDates(true, 'd/m/Y'); }, null, true)->get();
        //  =============== [End] ===========

        // ===== [replaced] No need to to load Excel file multiple times, just initialize variables to point to $converted_contrats!
       // $notFound_contrats = clone $exist_contrats = clone $converted_contrats; // use clone to not assign by reference
        $incorrect_contrats = [];
        $notFound_contrats = [];
        $exist_contrats = [];
        // ========= [End]

        $countContratFound = 0;
        $countContratNotFound = 0;
        $countContratcreated = 0;
        $countContratExist= 0;
        $countContratAnnuler = 0;
        $countContratIncorrect = 0;

        // Work with convertedcontrat file
        foreach ($converted_contrats as $key => $value) {
            
          //  $value_array = array_values($value->all());
            // Check contrat incorrect informations in importation  => place it in contrats incorrect file
            if($branche->slug == 'automobile'){
                // Auto
                $validationErrors = $this->validationNewAuto($value);
                
            }else{
                $validationErrors = $this->validationNew($value);                
            }
            if(!empty($validationErrors)){
                $countContratIncorrect++;
                $incorrect_contrats[] = $value;
                continue;
            }
            
            //check if contrat already existe in CRM using Num police Contrat
            if(!empty($value['n0_contrat'])){
                $contrat = Contrat::where('num_police', $value['n0_contrat'])->first();
            }else{
                // not found
                $contrat = null;
            }
            
            if ($contrat) {
                $countContratExist++;
                $exist_contrats[] = $value;
            } else {
                $countContratNotFound++;
                $notFound_contrats[] = $value;
            }
            
        }
        //dd($converted_contrats);
        $storeFilesPaths = $this->storeFiles($filename_raw, $filename, $folder, $notFound_contrats, $exist_contrats, $incorrect_contrats, $converted_contrats);
        // Store result in importfile_contratcompany table
        if($this->uploadMode == "server"){
            // server
            $dbImportFile = importfiles_contratcompany::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => $pathServeur,
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => $storeFilesPaths['contrat_found_path'],
                'nom_fichier_notfound' => $storeFilesPaths['contrat_notFound_path'],
                'nom_fichier_created' => $storeFilesPaths['contrat_newcreated_path'],
                'nom_fichier_exist' => $storeFilesPaths['contrat_exist_path'],
                'nom_fichier_annuler' => null,
                'nom_fichier_incorrect' => $storeFilesPaths['incorrect_contrats_path'],
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_contrat_created' => $countContratcreated,
                'nbr_contrat_exist' => $countContratExist,
                'nbr_contrat_annuler' => $countContratAnnuler,
                'nbr_contrat_incorrect' => $countContratIncorrect,
                'grpbranche_id' => $request->grpbranche_id,
                'branche_id' => $request->branche_id,
                'company' => $request->company,
                'societe_id' => $request->societe,
                'site_id' => $request->site,
                'equipe_id' => $request->equipe,
                'conseiller_id' => $request->conseiller,
                'type_import' => $request->typeImport,
                'keepCommission' => 0,
                'active' => 1,
            ]);
        }else{
            // local
            $dbImportFile = importfiles_contratcompany::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => "upload/$folder/$filename",
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => "upload/$folder/found$filename",
                'nom_fichier_notfound' => "upload/$folder/notFound$filename",
                'nom_fichier_created' => "upload/$folder/created$filename",
                'nom_fichier_exist' => "upload/$folder/exist$filename",
                'nom_fichier_annuler' => null,
                'nom_fichier_incorrect' => "upload/$folder/incorrect$filename",
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_contrat_created' => $countContratcreated,
                'nbr_contrat_exist' => $countContratExist,
                'nbr_contrat_annuler' => $countContratAnnuler,   
                'nbr_contrat_incorrect' => $countContratIncorrect, 
                'grpbranche_id' => $request->grpbranche_id,
                'branche_id' => $request->branche_id,
                'company' => $request->company,
                'societe_id' => $request->societe,
                'site_id' => $request->site,
                'equipe_id' => $request->equipe,
                'conseiller_id' => $request->conseiller,
                'type_import' => $request->typeImport,
                'keepCommission' => 0,
                'active' => 1,
            ]);

        }

        // Remove local temp file to load only
        if($this->uploadMode == "server"){
            File::delete($path);
            File::delete($convertedContratsFilePath);
        }
        
        return response()->json('Contrats importés avec succès');

    }

    public function convertFileStructure($request, $arrayToArray, $filename_raw, $filename, $folder, $branche = 'sante') {
        // 1- Detect the company in the fillter
        // 2- get all the mapping for this company in DB (cegema structure is the default) 
        //  ** No need to block if no mapping for company  because detected in validation column and value
        // 3- vlidate the presence of columns neded and value
        //  *if any required column not precense stope the import
        //  *if value incorrect put row in contrat incorrect

        $companyId = $request->company;

        // Get company mapping columns
        $companyMappColl = MappingMaj::where('id_company', $companyId)->where('mapp_type', 'App\ColumnsMaj')->get();
        
        // if empty compagnie get fallback to all company if existe
        $companyMappColl_allComp = MappingMaj::where('id_company', 10000)->where('mapp_type', 'App\ColumnsMaj')->get();
        if($companyMappColl->isEmpty() && $companyMappColl_allComp->isNotEmpty()){
            $companyMappColl = $companyMappColl_allComp;
        }

        // Add column libelle to collection
        $columns = ColumnsMaj::get();
        foreach($companyMappColl as $mp){
            $mp->crm_map_libelle =  $columns->where('id',$mp->mapp_id)->first()['libelle'] ? $this->slugify($columns->where('id',$mp->mapp_id)->first()['libelle']) : null;
        }
        // Get the slug of companny Mapp to search with it (accuracy)
        $mappSetColumns = $companyMappColl->pluck('crm_map_libelle', 'map_company_slug')->toArray();
        $convertedArray = $this->recursive_change_key($arrayToArray, $mappSetColumns);

        // $companyMappAll = MappingMaj::where('id_company', $companyId)->where(function($query){
        //     $query->where('mapp_type', 'App\Regime')
        //     ->orwhere('mapp_type', 'App\SituationFamiliale')
        //     ->orwhere('mapp_type', 'App\Garantie')
        //     ->orwhere('mapp_type', 'App\Typepaiement')
        //     ->orwhere('mapp_type', 'App\Modepaiement')
        //     ->orwhere('mapp_type', 'App\Typepermi')
        //     ->orwhere('mapp_type', 'App\Statut');
        // })->get();

        // // if empty compagnie get fallback to all company if existe
        // $companyMappAll_allComp  = MappingMaj::where('id_company', 10000)->where(function($query){
        //                                             $query->where('mapp_type', 'App\Regime')
        //                                             ->orwhere('mapp_type', 'App\SituationFamiliale')
        //                                             ->orwhere('mapp_type', 'App\Garantie')
        //                                             ->orwhere('mapp_type', 'App\Typepaiement')
        //                                             ->orwhere('mapp_type', 'App\Modepaiement')
        //                                             ->orwhere('mapp_type', 'App\Typepermi')
        //                                             ->orwhere('mapp_type', 'App\Statut');
        //                                             })->get();

        // if($companyMappAll->isEmpty() && $companyMappAll_allComp->isNotEmpty()){
        //     $companyMappAll = $companyMappAll_allComp;
        // }
        
        // // separate mapping conversion
        // // Get the slug of companny Mapp to search with it (accuracy)
        // $companyMappSetRegime = $companyMappAll->where('mapp_type', 'App\Regime')->pluck('mapp_id', 'map_company_slug')->toArray();
        // $companyMappSetSF = $companyMappAll->where('mapp_type', 'App\SituationFamiliale')->pluck('mapp_id', 'map_company_slug')->toArray();
        // $companyMappSetGar = $companyMappAll->where('mapp_type', 'App\Garantie')->pluck('mapp_id', 'map_company_slug')->toArray();
        
        // $companyMappSetTypePa = $companyMappAll->where('mapp_type', 'App\Typepaiement')->pluck('mapp_id', 'map_company_slug')->toArray();
        // $companyMappSetModePa = $companyMappAll->where('mapp_type', 'App\Modepaiement')->pluck('mapp_id', 'map_company_slug')->toArray();
        // $companyMappSetTypePermis = $companyMappAll->where('mapp_type', 'App\Typepermi')->pluck('mapp_id', 'map_company_slug')->toArray();
        // $companyMappSetStatut = $companyMappAll->where('mapp_type', 'App\Statut')->pluck('mapp_id', 'map_company_slug')->toArray();

        $companyMappSetRegime = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Regime');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        // if empty compagnie get fallback to all company if existe
        if(empty($companyMappSetRegime)){
            $companyMappSetRegime = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Regime');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        $companyMappSetSF = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\SituationFamiliale');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetSF)){
            $companyMappSetSF = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\SituationFamiliale');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        $companyMappSetGar = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Garantie');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetGar)){
            $companyMappSetGar = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Garantie');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        
        // AUTO
        $companyMappSetTypePa = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Typepaiement');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetTypePa)){
            $companyMappSetTypePa = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Typepaiement');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        $companyMappSetModePa = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Modepaiement');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetModePa)){
            $companyMappSetModePa = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Modepaiement');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        $companyMappSetTypePermis = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Typepermi');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetTypePermis)){
            $companyMappSetTypePermis = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Typepermi');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }
        $companyMappSetStatut = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Statut');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetStatut)){
            $companyMappSetStatut = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Statut');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }

        // Motifs Impaye in annulation
        $companyMappSetMotif = MappingMaj::where('id_company', $companyId)->where(function($query){
            $query->where('mapp_type', 'App\Motif');
        })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        if(empty($companyMappSetMotif)){
            $companyMappSetMotif = MappingMaj::where('id_company', 10000)->where(function($query){
                $query->where('mapp_type', 'App\Motif');
            })->get()->pluck('mapp_id', 'map_company_slug')->toArray();
        }

        // Sante + Auto
        $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetRegime, 'regime');
        $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetSF, 'situation_de_famille');
        $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetGar, 'garantie');
        
        // Auto
        if($branche == 'automobile'){
            $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetTypePa, 'type_paiement');
            $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetModePa, 'mode_paiement');
            $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetModePa, 'mode_paie_reg');
            $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetTypePermis, 'type_permis');
            $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetStatut, 'statut_id');
        }
        
        // impaye annulation motif
        $convertedArray = $this->recursive_change_value($convertedArray, $companyMappSetMotif, 'motif_de_limpaye');

        $contratConvertedFilePath = $this->storeConvertedFile($filename_raw, $filename, $folder, $convertedArray);

        return [
            'convertedArray' => $convertedArray,
            'contratConvertedFilePath' => $contratConvertedFilePath,
        ];
        
    }

    public function updateContratCompany($contrat, $newRequest, $importFileId, $garanties_all, $gammes_all, $Compagnies_all, $branche, $save){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        $changeType = $this->calculeChangesType($contrat, $newRequest);
        if($changeType == 'red' || $changeType == 'orange'){
            $changes = $this->updateProcess($contrat, $newRequest, $importFileId,  $garanties_all, $gammes_all, $Compagnies_all, $changeType, $branche, $save);
        }else{
            if($branche->slug == 'automobile'){
                // calcule change in auto even if green type
                $changes = $this->updateProcess($contrat, $newRequest, $importFileId,  $garanties_all, $gammes_all, $Compagnies_all, $changeType, $branche, $save);
            }else{
                // No Update
                $changes = null;
                
                // // Update counter untouched file (not update any info in contrat)
                // $file = importfiles_contratcompany::find($importFileId);
                // $file->nbr_contrat_untouched = $file->nbr_contrat_untouched + 1;
                // $file->save();
            }

            /*
            // Calculate tier data chenges in green type
            $changed_data_tier = $this->updatetierdata($contrat, $newRequest, $save);
            // respect changes structure
            $changes = [
                'contrat' => [],
                'tier' => $changed_data_tier,
            ];
            */
        }

        return $changes;
    }


    public function updateProcess($contrat, $newRequest, $importFileId,  $garanties_all, $gammes_all, $Compagnies_all, $changeType, $branche, $save){
        // Initialize the changes
        $changed_data_contrat = [];
        $changed_data_tier = [];

        // check tier data and update it
        $changed_data_tier = $this->updatetierdata($contrat, $newRequest, $save);
        $changed_data_contrat = $this->updateTarif($contrat, $newRequest, [], $save);
        if($branche->slug == 'automobile'){
            $changed_data_auto = $this->autoUpdateData($contrat, $newRequest, $changed_data_contrat, $save);
            // dd($changed_data_auto);
        }else{
            $changed_data_auto = null;
        }
        if($changeType == 'red'){
            // chack update formule in save mode  || alow always in preview mode to trace changes
            if($contrat->updateFormule && $save || !$save){
                $changed_data_contrat = $this->updateFormule($contrat, $newRequest, $changed_data_contrat,  $garanties_all, $gammes_all, $Compagnies_all, $save);
            }elseif(!$contrat->updateFormule && $save){
                // Flag force formule change ignore 
                $updatedWithoutFormule = true;
            }
        }
        // $changed_data_contrat = $this->updateNumPolice($contrat, $newRequest, $changed_data_contrat, $save);     // No nedd cntrat already found

        if(isset($updatedWithoutFormule) && $updatedWithoutFormule == true){
            // Add flag in trace
            $changed_data = [
                'contrat' => $changed_data_contrat,
                'tier' => $changed_data_tier,
                'updatedWithoutFormule' => $updatedWithoutFormule,
                'auto' => $changed_data_auto,
            ];
        }else{
            $changed_data = [
                'contrat' => $changed_data_contrat,
                'tier' => $changed_data_tier,
                'auto' => $changed_data_auto,
            ];
        }

        if($save){
            $tabInfoTrace = [
                'slug'                => 'update-company-contrat', 
                'display_name'          => 'mettre à jour contrat', 
                'commentaires'         => json_encode($changed_data),
                'tracable_id'          =>  $contrat->id, 
                'tracable_type'        =>  "App\Contrat",  
                'importfile_id'        =>  $importFileId,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
        }else{
            // return save structure changed_data with no save trace in db
            return $changed_data;
        }
        
    }

    public function updatetierdata($contrat, $newRequest, $save){
        $dpps_numFiche = Contrat::join('devis', function ($join) use($contrat) {
                $join->on('contrats.devis_id', '=', 'devis.id')
                ->where('contrats.id', $contrat->id);
            })->join('tiers', 'devis.tiers_id', '=', 'tiers.id')
            ->join('fiches', 'fiches.num_fiche', '=', 'devis.num_fiche')
            ->join('dpps', 'dpps.tiers_id', '=', 'tiers.id')
            ->select('dpps.*', 'devis.num_fiche', 'devis.id as devis_id')
            ->first();


        $changed_data = [];
        $dpps_changed = false;
        $tier_changed = false;
        $fiche_changed = false;

        $dpps_data = Dpps::find($dpps_numFiche->id);
        $tier_data = Tiers::where('num_dpp', $dpps_numFiche->id)->first();

        $regimes = Regime::all();
        $sf = SituationFamiliale::all();
        
        // nom check
        if(!empty($newRequest->nom) && (strtolower($newRequest->nom) != strtolower($dpps_numFiche->nom)) ) {

            array_push($changed_data, [
                'old_Nom' => $dpps_data->nom,
                'new_Nom' => strtolower($newRequest->nom),
            ]);

            $dpps_data->nom = strtolower($newRequest->nom);
            $dpps_changed = true;

            // Upadte in tier raison_social as well
            $tier_data->raison_sociale = null;
            $tier_data->raison_sociale = strtolower($newRequest->nom).' '. strtolower($newRequest->prenom);
            $tier_changed = true;

        };
        // prenom check
        if(!empty($newRequest->prenom) && strtolower($newRequest->prenom) != strtolower($dpps_numFiche->prenom)){
            
            array_push($changed_data, [
                'old_Prenom' => $dpps_data->prenom,
                'new_Prenom' => strtolower($newRequest->prenom),
            ]);

            $dpps_data->prenom = strtolower($newRequest->prenom);
            $dpps_changed = true;

            // Upadte in tier raison_social as well
            $tier_data->raison_sociale = null;
            $tier_data->raison_sociale = strtolower($newRequest->nom).' '. strtolower($newRequest->prenom);
            $tier_changed = true;

        } 
        // email check
        if(!empty($newRequest->email)){
            if(strtolower(trim($newRequest->email)) == strtolower(trim($tier_data->email)) || strtolower(trim($newRequest->email)) == strtolower(trim($tier_data->email_pro)) || strtolower(trim($newRequest->email)) == strtolower(trim($tier_data->numemail))) {
                // Do nothing email deja exist in crm
            }else{
                array_push($changed_data, [
                    'old_Email' => strtolower(trim($tier_data->email)),
                    'new_Email' => strtolower(trim($newRequest->email)),
                ]);
    
                $tier_data->email = strtolower(trim($newRequest->email));
                $tier_changed = true;
            }
        }
        // regime check
        if(!empty($newRequest->regime) && $newRequest->regime != $dpps_data->regime_id) {
            array_push($changed_data, [
                'old_Regime' => $regimes->where('id',$dpps_data->regime_id)->first()['libelle'] ??  null,
                'new_Regime' => $regimes->where('id',$newRequest->regime)->first()['libelle'] ??  null,
            ]);

            $dpps_data->regime_id = $newRequest->regime;
            $dpps_changed = true;
        }

        // datenaissance check
        if(!empty($newRequest->date_naissance) && $newRequest->date_naissance != $dpps_data->date_naissance) {
            array_push($changed_data, [
                'old_Date de naissance' => $dpps_data->date_naissance,
                'new_Date de naissance' => $newRequest->date_naissance,
            ]);

            $dpps_data->date_naissance = $newRequest->date_naissance;
            $tiersController = new TiersController();
            $age = $tiersController->calculAge($newRequest->date_naissance);
            $dpps_data->age = $age['age'];
            $dpps_changed = true;
        }

        // situation familiale
        if( !empty($newRequest->situation_familiale) && $newRequest->situation_familiale != $dpps_data->situation_familiale) {
            array_push($changed_data, [
                'old_Situation de famille' => $sf->where('id',$dpps_data->situation_familiale)->first()['libelle'] ??  null,
                'new_Situation de famille' => $sf->where('id',$newRequest->situation_familiale)->first()['libelle'] ??  null,
            ]);

            $dpps_data->situation_familiale = $newRequest->situation_familiale;
            $dpps_changed = true;
        }

        // code postal
        if(!empty($newRequest->adresse_insee['code_postal']) && $newRequest->adresse_insee['code_postal'] != $tier_data->code_postal) {
            array_push($changed_data, [
                'old_Code postal' => $tier_data->code_postal,
                'new_Code postal' => $newRequest->adresse_insee['code_postal'],
            ]);

            $tier_data->code_postal = $newRequest->adresse_insee['code_postal'];
            $tier_changed = true;
        }
        // Ville
        if(!empty($newRequest->adresse_insee['ville']) && strtolower($newRequest->adresse_insee['ville']) != strtolower($tier_data->ville)) {
            array_push($changed_data, [
                'old_Ville' => strtolower($tier_data->ville),
                'new_Ville' => strtolower($newRequest->adresse_insee['ville']),
            ]);

            $tier_data->ville = strtolower($newRequest->adresse_insee['ville']);
            $tier_changed = true;
        }
        // Num Tel
        if(!empty($newRequest->num_tel)){
            if($newRequest->num_tel == $tier_data->num_tel || $newRequest->num_tel == $tier_data->num_post_tel || $newRequest->num_tel == $tier_data->fax_pro || $newRequest->num_tel == $tier_data->portable){
                // Do nothing num_tel already exust in crm
            }else{
                // Update num_tel
                array_push($changed_data, [
                    'old_Num téléphone' => strtolower($tier_data->num_tel),
                    'new_Num téléphone' => strtolower($newRequest->num_tel),
                ]);
    
                $tier_data->num_tel = $newRequest->num_tel;
                $tier_changed = true;
            }
        }
        // Portable
        if(!empty($newRequest->portable)){

            if($newRequest->portable == $tier_data->num_tel || $newRequest->portable == $tier_data->num_post_tel || $newRequest->portable == $tier_data->fax_pro || $newRequest->portable == $tier_data->portable){
                // Do nothing portable already exust in crm
            }else{
                // Update portable
                array_push($changed_data, [
                    'old_Portable' => strtolower($tier_data->portable),
                    'new_Portable' => strtolower($newRequest->portable),
                ]);
    
                $tier_data->portable = $newRequest->portable;
                $tier_changed = true;
            }
        }

        // check address 
        if(!empty($newRequest->adresse['adresse3'])){
            if( strtolower($newRequest->adresse['adresse3']) != strtolower($tier_data->adresse3) && strtolower($newRequest->adresse['adresse3']) != strtolower($tier_data->adresse2) && strtolower($newRequest->adresse['adresse3']) != strtolower($tier_data->adresse)){
                array_push($changed_data, [
                    'old_Adresse3' => strtolower($tier_data->adresse3),
                    'new_Adresse3' => strtolower($newRequest->adresse['adresse3']),
                ]);
    
                $tier_data->adresse3 = strtolower($newRequest->adresse['adresse3']);
                $tier_changed = true;
            }
        }

        if( !empty($newRequest->adresse['adresse2']) ) {
            if( strtolower($newRequest->adresse['adresse2']) != strtolower($tier_data->adresse3) && strtolower($newRequest->adresse['adresse2']) != strtolower($tier_data->adresse2) && strtolower($newRequest->adresse['adresse2']) != strtolower($tier_data->adresse)){
                array_push($changed_data, [
                    'old_Adresse2' => strtolower($tier_data->adresse2),
                    'new_Adresse2' => strtolower($newRequest->adresse['adresse2']),
                ]);
    
                $tier_data->adresse2 = strtolower($newRequest->adresse['adresse2']);
                $tier_changed = true;
            }
        }
        if( !empty($newRequest->adresse['adresse']) ) {
            if( strtolower($newRequest->adresse['adresse']) != strtolower($tier_data->adresse3) && strtolower($newRequest->adresse['adresse']) != strtolower($tier_data->adresse2) && strtolower($newRequest->adresse['adresse']) != strtolower($tier_data->adresse)){
                array_push($changed_data, [
                    'old_Adresse' => strtolower($tier_data->adresse),
                    'new_Adresse' => strtolower($newRequest->adresse['adresse']),
                ]);
                
                $tier_data->adresse = strtolower($newRequest->adresse['adresse']);
                if(empty($tier_data->adresse2)){
                    // Also update address2
                    $tier_data->adresse2 = strtolower($newRequest->adresse['adresse']);
                }
                $tier_changed = true;
            }
        }

        // Date effet
        if(!empty($newRequest->date_effet)){
            $devis = Devis::find($dpps_numFiche->devis_id);
            if($devis && $devis->date_effet != $newRequest->date_effet){
                array_push($changed_data, [
                    "old_Date d'effet" => $devis->date_effet,
                    "new_Date d'effet" => $newRequest->date_effet,
                ]);
                $devis->date_effet = $newRequest->date_effet;
                $devis->fiche->date_effet = $newRequest->date_effet;
                if($save){
                    $devis->push();
                }
            }
        }
        if($save){
            if($dpps_changed) $dpps_data->save();
            if($tier_changed) $tier_data->save();
        }

        // Date enrg        
        if(!empty($newRequest->date_denregistrement)){
            $devis = Devis::find($dpps_numFiche->devis_id);
            $contrat = Contrat::where('devis_id', $dpps_numFiche->devis_id)->first();
            if($devis && $contrat){
                if($newRequest->date_denregistrement != Carbon::parse($devis->date_validation)->format('Y-m-d')
                    && $newRequest->date_denregistrement != Carbon::parse($devis->date_revalidation)->format('Y-m-d')
                    && $newRequest->date_denregistrement != Carbon::parse($contrat->date_revalidation )->format('Y-m-d')
                    && $newRequest->date_denregistrement != Carbon::parse($contrat->date_validation)->format('Y-m-d') ){

                    array_push($changed_data, [
                        "old_Date d'enregistrement" => Carbon::parse($devis->date_validation)->format('Y-m-d'),
                        "new_Date d'enregistrement" => $newRequest->date_denregistrement,
                    ]);
                    $devis->date_validation = $newRequest->date_denregistrement;
                    $devis->date_revalidation = $newRequest->date_denregistrement;
                    $contrat->date_revalidation = $newRequest->date_denregistrement;
                    $contrat->date_validation = $newRequest->date_denregistrement;
                    if($save){
                        $devis->save();
                        $contrat->save();
                    }
                }
            }
        }

        return $changed_data;

    }

    public function updateNumPolice($contrat, $newRequest, $changed_data_contrat = [], $save){
        $old_num_police = $contrat->num_police;    
        if($contrat->num_police != $newRequest->ncontrat){
            // Update num police in crm
            $contrat->num_police = $newRequest->ncontrat;
            if($save){
                $contrat->save();
            }
            // Trace change
            array_push($changed_data_contrat, [
                'old_Num police' => $old_num_police,
                'new_Num police' => $newRequest->ncontrat,
            ]);
        }
        return $changed_data_contrat;  // [] if no changement at all 
    }

    public function updateTarif($contrat, $newRequest, $changed_data_contrat = [], $save){
        $devi_formule = json_decode($contrat->devis->formule, true);
        if($devi_formule['tarifTotal'] != $newRequest->cotisation_mensuelle_ttc){

            $old_tarifTotal = $devi_formule['tarifTotal'];
            //New update only cottisation [tarifTotal].
            $devi_formule['tarifTotal'] = $newRequest->cotisation_mensuelle_ttc;
            $devi_formule['tarif'] = $newRequest->cotisation_mensuelle_ttc;
            $devi_formule['packTarif'] = $newRequest->cotisation_mensuelle_ttc;
            $devi_formule['tarifInitial'] = $newRequest->cotisation_mensuelle_ttc;
    
            // Update model data with refresh after UpdateTarifDevis()
            if($save){
                $contrat->devis->formule = json_encode($devi_formule);
                $contrat->devis->saveFormuleValues($devi_formule);
                // $contrat->refresh();
            }
            
            // trace change contrat
            $changed_data_contrat = [
                [
                    'old_Tarif total' => $old_tarifTotal ?? 0,
                    'new_Tarif total' => $devi_formule['tarifTotal'] ?? 0,
                ],
            ];
        }

        return $changed_data_contrat;
    }

    public function autoUpdateData($contrat, $newRequest, $changed_data_contrat = [], $save){
        $devis = $contrat->devis;
        $devi_formule = json_decode($devis->formule, true);
        $tier = $devis->tiers;
        $dpp = $tier->dpps;
        $vehicule = Vehicule::where('id', $devis->vehicule_id)->where('tiers_id', $tier->id)->where('active', 1)->first();
        
        // Auto Mapping
        $typePaie_all = Typepaiement::get();
        $typePermis_all = Typepermi::get();
        $modePaie_all = Modepaiement::get();

        $devis_changed = false;
        $dpp_changed = false;
        $vehicule_changed = false;
        
        // Initialize
        $changed_data_auto = [];

        if($devis->frais_dossier != $newRequest->frais_dossier){
            $old_frais_dossier = $devis->frais_dossier;
            // update frais_dossier in devis model
            $devis->frais_dossier = $newRequest->frais_dossier;
            $devis_changed = true;
            array_push($changed_data_auto, [
                'old_Frais dossier' => $old_frais_dossier ?? 0,
                'new_Frais dossier' => $newRequest->frais_dossier ?? 0,
            ]);
        }

        if($devis->prorata != $newRequest->prorata){
            $old_prorata = $devis->prorata;
            $devis->prorata = $newRequest->prorata;
            $devis_changed = true;
            array_push($changed_data_auto, [
                'old_Prorata' => $old_prorata,
                'new_Prorata' => $newRequest->prorata,
            ]);
        }

        if($devis->reste_a_payer != $newRequest->reste_a_payer){
            $old_reste_a_payer = $devis->reste_a_payer;
            $devis->reste_a_payer = $newRequest->reste_a_payer;
            $devis_changed = true;
            array_push($changed_data_auto, [
                'old_Reste a payer' => $old_reste_a_payer,
                'new_Reste a payer' => $newRequest->reste_a_payer,
            ]);
        }

        if($devis->typepaiements_id != $newRequest->type_paiement){
            $old_typepaiements = $devis->typepaiements_id;

            $devis->typepaiements_id = $newRequest->type_paiement;
            $devi_formule['mode']['id'] = $newRequest->type_paiement;

            $devis_changed = true;
            array_push($changed_data_auto, [
                'old_Type paiement' => $typePaie_all->where('id',$old_typepaiements)->first()['libelle'] ??  null,
                'new_Type paiement' => $typePaie_all->where('id',$newRequest->type_paiement)->first()['libelle'] ??  null,
            ]);
        }

        if($devis->modepaiements_id != $newRequest->mode_paiement){
            $old_modepaiements = $devis->modepaiements_id;
            $devis->modepaiements_id = $newRequest->mode_paiement;
            $devis_changed = true;
            array_push($changed_data_auto, [
                'old_Type paiement' => $modePaie_all->where('id',$old_modepaiements)->first()['libelle'] ??  null,
                'new_Type paiement' => $modePaie_all->where('id',$newRequest->mode_paiement)->first()['libelle'] ??  null,
            ]);
        }

        if($dpp->num_permis != $newRequest->num_permis){
            $old_num_permis = $dpp->num_permis;
            $dpp->num_permis = $newRequest->num_permis;
            $dpp_changed = true;
            array_push($changed_data_auto, [
                'old_Num permis' => $old_num_permis,
                'new_Num permis' => $newRequest->num_permis,
            ]);
        }

        if($dpp->lieu_permis != $newRequest->lieu_permis){
            $old_lieu_permis = $dpp->lieu_permis;
            $dpp->lieu_permis = $newRequest->lieu_permis;
            $dpp_changed = true;
            array_push($changed_data_auto, [
                'old_Lieu permis' => $old_lieu_permis,
                'new_Lieu permis' => $newRequest->lieu_permis,
            ]);
        }

        if($dpp->type_permis != $newRequest->type_permis){
            $old_type_permis = $dpp->type_permis;
            $dpp->type_permis = $newRequest->type_permis;
            $dpp_changed = true;
            array_push($changed_data_auto, [
                'old_Type permis' => $typePermis_all->where('id',$old_type_permis)->first()['libelle'] ??  null,
                'new_Type permis' => $typePermis_all->where('id',$newRequest->type_permis)->first()['libelle'] ??  null,
            ]);
        }

        $carbon_dpp_date_permis = Carbon::parse($dpp->date_permis);
        $carbon_newRequest_date_permis = Carbon::parse($newRequest->date_permis);
        if($carbon_dpp_date_permis->ne($carbon_newRequest_date_permis)){
            $old_date_permis = $carbon_dpp_date_permis->format('d-m-Y');
            $dpp->date_permis = $carbon_newRequest_date_permis;
            $dpp_changed = true;
            array_push($changed_data_auto, [
                'old_Date permis' => $old_date_permis,
                'new_Date permis' => $carbon_newRequest_date_permis->format('d-m-Y'),
            ]);
        }


        if($vehicule && strtolower($vehicule->model) != strtolower($newRequest->model)){
            $old_model = $vehicule->model;
            $vehicule->model = $newRequest->model;
            $vehicule->apple_comm = $newRequest->model;
            $vehicule_changed = true;
            array_push($changed_data_auto, [
                'old_Model' => $old_model,
                'new_Model' => $newRequest->model,
            ]);
        }

        if($vehicule && strtolower($vehicule->immatriculation) != strtolower($newRequest->immatriculation)){
            $old_immatriculation = $vehicule->immatriculation;
            $vehicule->immatriculation = $newRequest->immatriculation;
            $vehicule_changed = true;
            array_push($changed_data_auto, [
                'old_Immatriculation' => $old_immatriculation,
                'new_Immatriculation' => $newRequest->immatriculation,
            ]);
        }

        if($vehicule && strtolower($vehicule->code_sra) != strtolower($newRequest->code_sra)){
            $old_code_sra = $vehicule->code_sra;
            $vehicule->code_sra = $newRequest->code_sra;
            $vehicule_changed = true;
            array_push($changed_data_auto, [
                'old_Code sra' => $old_code_sra,
                'new_Code sra' => $newRequest->code_sra,
            ]);
        }
        
        if($save){
            if($devis_changed) $devis->save();
            if($dpp_changed) $dpp->save();
            if($vehicule_changed) $vehicule->save();
        }

        return $changed_data_auto;
    }

    public function updateFormule($contrat, $newRequest, $changed_data_contrat = [],  $garanties_all, $gammes_all, $Compagnies_all, $save){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        $devis = $contrat->devis;
        $devi_formule = json_decode($devis->formule, true);
        $devis_change = false;

        $old_formule = $devi_formule['garantie'];    
        $old_formule_id = $devi_formule['id'];    
        $old_gamme = $devi_formule['gamme'];    
        $old_gamme_id = $devi_formule['gid'];       //idGamme
        $old_company = $devi_formule['compagnie']; 
        $old_company_code = $devi_formule['cieCode']; 
        
        // New
        // Collection based find
        $garantie = $garanties_all->where('id', $newRequest->garantie)->first();
        if(!$garantie) $garantie = $garanties_all->where('id', 1)->first();       //Default gar 1
        $gamme = $gammes_all->where('id', $garantie->gamme_id)->first();
        $compagnie = $Compagnies_all->where('id', $gamme->compagnie_id)->first();


        $new_formule = $garantie->libelle;    
        $new_formule_id = $garantie->id;    
        $new_gamme = $gamme->libelle;    
        $new_gamme_id = $gamme->id;    
        $new_company = $compagnie->libelle;
        $new_company_code = $compagnie->code;

        // Check Formule // Gar
        if($old_formule_id != $new_formule_id){
            $devi_formule['id'] = $garantie->id;
            $devi_formule['codeGarantie'] = $garantie->code;
            $devi_formule['garantie'] = $new_formule;
            $devi_formule['libelle'] = $new_formule;
            // intialize ronforts and venteCouple
            $devi_formule['selectedRenforts'] = [];
            $devi_formule['selectedFranchises'] = [];
            $devi_formule['venteCoupleeFormules'] = [];
            // Add field to indicate formule update 
            $devi_formule['update-portefeuille-compagnie'] =true;

            if($save){
                $devis->formule = json_encode($devi_formule);
                $devis_change = true;
            }
            // Trace change
            array_push($changed_data_contrat, [
                'old_Formule' => $old_formule,
                'new_Formule' => $new_formule,
            ]);
        }
        // Check gamme
        if($old_gamme_id != $new_gamme_id){
            $devi_formule['gid'] = $gamme->id;
            $devi_formule['idGamme'] = $gamme->id;
            $devi_formule['codeGamme'] = $gamme->code;
            $devi_formule['gamme'] = $new_gamme;
            if($save){
                $devis->formule = json_encode($devi_formule);
                $devis_change = true;
            }
            // Trace change
            array_push($changed_data_contrat, [
                'old_Gamme' => $old_gamme,
                'new_Gamme' => $new_gamme,
            ]);
        }

        // Check company
        if($old_company_code != $new_company_code){
            $devi_formule['cieCode'] = $compagnie->code;
            $devi_formule['cie_code'] = $compagnie->code;
            $devi_formule['compagnie'] = $new_company;
            $devi_formule['logo'] = $compagnie->logo;
            if($save){
                $devis->formule = json_encode($devi_formule);
                $devis_change = true;
            }
            // Trace change
            array_push($changed_data_contrat, [
                'old_Compagnie' => $old_company,
                'new_Compagnie' => $new_company,
            ]);
        }

        if($devis_change){
            $devis->save();
        }

        if ($devis_change & $save) {
            $devis->saveFormuleValues($devi_formule);
        }

        return $changed_data_contrat;  // [] if no changement at all 
    }


    public function loadContratCompany(Request $request, $type = 'en_cours'){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
       // FonctionController::setConfigMySqlTarificateur(); //Database hosts array is empty

        $dateDebut = $request->dateDebut? Carbon::createFromFormat('Y-m-d', $request->dateDebut)->format('Y-m-d 00:00:00') : null;
        $dateFin = $request->dateFin? Carbon::createFromFormat('Y-m-d', $request->dateFin)->format('Y-m-d 23:59:59') : null;
        
        $files =  importfiles_contratcompany::latest()
        ->where(function($query) use($type, $request){
            $query->when($type == 'annulation', function($query) use($type, $request){
                if($request->typeImport){
                    if($request->typeImport == 1){
                        $query->where('type_import', $type)
                            ->orWhere('type_import', 'impaye')
                            ->orWhere('type_import', 'remiseVigueur');
                    }elseif($request->typeImport == 2){     // Annulation
                        $query->where('type_import', $type);
                    }elseif($request->typeImport == 3){     // Impaye
                        $query->where('type_import', 'impaye');
                    }elseif($request->typeImport == 4){     // remiseVigueur
                        $query->where('type_import', 'remiseVigueur');
                    }
                }else{      // Annulation + Impayé + remiseVigueur
                    $query->where('type_import', $type)
                            ->orWhere('type_import', 'impaye')
                            ->orWhere('type_import', 'remiseVigueur');
                }
                
            });
            $query->when($type == 'en_cours', function($query) use($type){
                $query->where('type_import', $type);
            });
        })
        ->where(function($query) use($request, $dateDebut, $dateFin, $type){
            // $query->where('type_import', $type);
            if($dateDebut) $query->where('created_at', '>=', $dateDebut);
            if($dateFin) $query->where('created_at', '<=', $dateFin);
            if($request->fileName) $query->where('nom_original', 'like', '%'.$request->fileName.'%');
            if($request->company) $query->where('company', $request->company);
            if($request->grpBrancheId) $query->where('grpbranche_id', $request->grpBrancheId);
            if($request->brancheId) $query->where('branche_id', $request->brancheId);
        })
        ->paginate(10);
        
        $grpBranches = Groupebranche::select('libelle', 'id')->get();
        $branches = Branche::select('id', 'libelle', 'couleur', 'icon')->get();
        $companies = Compagnie::select('id', 'libelle')->get();

        // $sites = Site::pluck('nom', 'complement_nom', 'id');
        $equipes = Equipe::select('id', 'nom')->get();
        $conseillers = User::where('courtier_id', $courtier_id)->select('id', 'nom', 'prenom')->get();

        foreach($files as $file){
            $file['grpbranche'] = $grpBranches->where('id',$file->grpbranche_id)->first()['libelle'] ??  null;
            $file['branche'] = $branches->where('id',$file->branche_id)->first() ??  null;
            $file['company'] = $companies->where('id',$file->company)->first()['libelle'] ??  null;
            if($file['grpbranche'] == 'Assurance personnelle'){
                $file['grpbranche_couleur'] = '#E5CCFF';
                $file['grpbranche_icon'] = 'fa fa-heartbeat';
            }else{
                $file['grpbranche_couleur'] = '#94EF94';
                $file['grpbranche_icon'] = 'fa fa-car';
            }
            // Contrats modified count in each file
            $file->ContratsModCount = TraceContratCompany::where('importfile_id', $file->id)
                                ->where('tracable_type', 'App\Contrat')
                                ->where('slug', 'update-company-contrat')     //Only modified contrats
                                ->groupBy('tracable_id')
                                ->get()->count();

            // $file->site = $sites->where('id', $file->site_id)->first() ?? null;
            $file->equipe = $equipes->where('id', $file->equipe_id)->first() ?? null;
            $file->conseiller = $conseillers->where('id', $file->conseiller_id)->first() ?? null;
            // if($file->conseiller){
            //     $file->username = FonctionController::realUsername($file->conseiller->username);
            // }else{
            //     $file->username = 'X';
            // }
        }
        return response()->json([
            'files' => $files,
            'uploadMode' => $this->uploadMode,
            'app_url' => env('APP_URL'),
        ]);
    }

    public function contratHistoryIndex(Request $request, $type = 'en_cours')
    {
        return view('portefeuilles.history'); 
    }

    public function contratHistory(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);

        $mappingSlugs = [
            'update-company-contrat' => 'Mettre à jour contrat PRT',
            'insertion-contrat-prt' => 'création contrat PRT',
            'creer-reglement-prt' => 'création réglement PRT',
            'update-reglement-prt' => 'Mettre à jour règlement PRT',
            'resiliation-contrat-prt' => 'Resiliation contrat PRT',
            'annulation-contrat-prt' => 'Annulation contrat PRT',
            'impaye-contrat-prt' => 'Impayé contrat PRT',
            'remise-vigueur-contrat-prt' => 'Remise en vigueur contrat PRT',
        ];
        
        $typeSearch = $request->typeSearch;
        $typeTrace = $request->typeTrace;
        if(!$typeSearch){
            return response()->json('Vous devez selectionner un type de rechrche', 455); 
        }

        // Initialize
        $idContrat = null;
        $idsFile = null;
        $contratReglementIds = null;
        $dateDebut = null;
        $dateFin = null;

        if($typeSearch == 1){
            $numPoliceContrat = $request->numPolice;
            if(!$numPoliceContrat){
                return response()->json('Vous devez entrer un numéro de police', 455); 
            }
            $contrat = Contrat::where('num_police', $numPoliceContrat)->first();
            if(!$contrat){
                return response()->json('Le contrat n\'a pas été trouvé dans le CRM', 455); 
            }
            $idContrat = $contrat->id;
        }elseif($typeSearch == 2){
            $fileName = $request->fileName;
            if(!$fileName){
                return response()->json('Vous devez entrer un nom de fichier', 455); 
            }
            $files = importfiles_contratcompany::where('nom_original', 'like', '%'.$fileName.'%')->get();
            if($files->isEmpty()){
                return response()->json('Le fichier n\'a pas été trouvé dans le CRM', 455); 
            }
            $idsFile = $files->pluck('id');
        }elseif($typeSearch == 3){
            $numPoliceContrat = $request->numPolice;
            if(!$numPoliceContrat){
                return response()->json('Vous devez entrer un numéro de police', 455); 
            }
            $contrat = Contrat::where('num_police', $numPoliceContrat)->first();
            $devisId = $contrat->devis_id;
            $contratReglementIds = Reglement::where('devis_id', $devisId)->get()->pluck('id');
        }elseif($typeSearch == 4){
            $dateDebut = $request->dateDebut? Carbon::createFromFormat('Y-m-d', $request->dateDebut)->format('Y-m-d 00:00:00') : null;
            $dateFin = $request->dateFin? Carbon::createFromFormat('Y-m-d', $request->dateFin)->format('Y-m-d 23:59:59') : null;
    
            if(!$dateDebut){
                return response()->json('Vous devez entrer une date', 455); 
            }
        }

        $tracesContrat =  TraceContratCompany::latest()
        ->when($typeSearch == 1, function($query) use($request, $idContrat){
            $query->where(function($query) use($request, $idContrat){
                $query->where('tracable_type', 'App\Contrat')
                        ->where('tracable_id', $idContrat);
            });
        })
        ->when($typeSearch == 2, function($query) use($request, $idsFile){
            $query->where(function($query) use($request, $idsFile){
                $query->whereIn('importfile_id', $idsFile);
            });
        })
        ->when($typeSearch == 3, function($query) use($request, $contratReglementIds){
            $query->where(function($query) use($request, $contratReglementIds){
                $query->where('tracable_type', 'App\Reglement')
                        ->whereIn('tracable_id', $contratReglementIds);
            });
        })
        ->when($typeSearch == 4, function($query) use($request, $dateDebut, $dateFin){
            $query->where(function($query) use($request, $dateDebut, $dateFin){
                if($dateDebut) $query->where('created_at', '>=', $dateDebut);
                if($dateFin) $query->where('created_at', '<=', $dateFin);
    
            });
        })
        ->when($typeTrace != 1000, function($query) use($request){
            if($request->typeTrace == 1){
                $query->where('slug', 'update-company-contrat');
            }elseif($request->typeTrace == 2){
                $query->where('slug', 'insertion-contrat-prt');
            }elseif($request->typeTrace == 3){
                $query->where('slug', 'creer-reglement-prt');
            }elseif($request->typeTrace == 4){
                $query->where('slug', 'update-reglement-prt');
            }elseif($request->typeTrace == 5){
                $query->where('slug', 'resiliation-contrat-prt');
            }elseif($request->typeTrace == 6){
                $query->where('slug', 'annulation-contrat-prt');
            }elseif($request->typeTrace == 7){
                $query->where('slug', 'impaye-contrat-prt');
            }elseif($request->typeTrace == 8){
                $query->where('slug', 'remise-vigueur-contrat-prt');
            }
        })
        ->when($typeSearch == 3, function($query) use($request){
            $query->with('file_reglements');
        })
        ->when($typeSearch != 3, function($query) use($request){
            $query->with('file');
        })
        ->with('user')
        ->paginate(10);

        foreach($tracesContrat as $trace){
            if(!is_array($trace->commentaires)){
                $trace->commentaires = json_decode($trace->commentaires, true);
            }

            if($trace->slug == 'update-company-contrat'){
                $trace->slugDisplayName = $mappingSlugs['update-company-contrat'];
            }elseif($trace->slug == 'insertion-contrat-prt'){
                $trace->slugDisplayName = $mappingSlugs['insertion-contrat-prt'];
            }elseif($trace->slug == 'creer-reglement-prt'){
                $trace->slugDisplayName = $mappingSlugs['creer-reglement-prt'];
            }elseif($trace->slug == 'update-reglement-prt'){
                $trace->slugDisplayName = $mappingSlugs['update-reglement-prt'];
            }elseif($trace->slug == 'resiliation-contrat-prt'){
                $trace->slugDisplayName = $mappingSlugs['resiliation-contrat-prt'];
            }elseif($trace->slug == 'annulation-contrat-prt'){
                $trace->slugDisplayName = $mappingSlugs['annulation-contrat-prt'];
            }elseif($trace->slug == 'impaye-contrat-prt'){
                $trace->slugDisplayName = $mappingSlugs['impaye-contrat-prt'];
            }

            if($typeSearch == 1){        // 1 Contrat only
                $trace->tier_hashid = Hashids::encode($contrat->devis->tiers_id);
                $trace->contrat_hashid = Hashids::encode($contrat->id);
            }
            else{
                if($trace->tracable_type == 'App\Contrat'){

                    $contrat = Contrat::find($trace->tracable_id);
                    if($contrat){
                        $trace->tier_hashid = Hashids::encode($contrat->devis->tiers_id);
                        $trace->contrat_hashid = Hashids::encode($contrat->id);
                    }else{
                        $trace->tier_hashid =  null;
                        $trace->contrat_hashid = null;
                    }
        
                }
            }
        }

        return response()->json([
            'tracesContrat' => $tracesContrat,
            'app_url' => env('APP_URL'),
            'uploadMode' => $this->uploadMode,
        ]);
    }

    public function loadContratsFromFiles(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800); 

        $file = importfiles_contratcompany::find($request->fileId);
        $branche = Branche::find($file->branche_id);

        if($this->uploadMode == "server"){
            
            $foundContratsFilePath = "$ftpUrl/$file->nom_fichier_found";
            $notFoundContratsFilePath = "$ftpUrl/$file->nom_fichier_notfound";
            $createdContratsFilePath = "$ftpUrl/$file->nom_fichier_created";
            $existContratsFilePath = "$ftpUrl/$file->nom_fichier_exist";
            $incorrectContratsFilePath = "$ftpUrl/$file->nom_fichier_incorrect"; 


            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $createdContratsFile = file_get_contents($createdContratsFilePath);
            $existContratsFile = file_get_contents($existContratsFilePath);

            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_notfound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_created = explode('/', $file->nom_fichier_created);
            $explode_filename_exist = explode('/', $file->nom_fichier_exist);
            
            /*  == before 
            // TBC
            $foundContratsFilePath = public_path("tmp/".$explode_filename_found[count($explode_filename_found)-1]);
            $notFoundContratsFilePath = public_path("tmp/".$explode_filename_notfound[count($explode_filename_notfound)-1]);
            $createdContratsFilePath = public_path("tmp/".$explode_filename_created[count($explode_filename_created)-1]);
            $existContratsFilePath = public_path("tmp/".$explode_filename_exist[count($explode_filename_exist)-1]);
            */
              
            // after 
            $foundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notfound[count($explode_filename_notfound)-1]));
            $createdContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_created[count($explode_filename_created)-1]));
            $existContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_exist[count($explode_filename_exist)-1]));
            
            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($createdContratsFilePath, $createdContratsFile);
            file_put_contents($existContratsFilePath, $existContratsFile);
            // check if file contrats incorrect exist
            if($file->nom_fichier_incorrect){
                $incorrectContratsFile = file_get_contents($incorrectContratsFilePath);
                $explode_filename_incorrect = explode('/', $file->nom_fichier_incorrect);
                $incorrectContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_incorrect[count($explode_filename_incorrect)-1]));
                file_put_contents($incorrectContratsFilePath, $incorrectContratsFile);
            }
    
        }else{
            $foundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_found));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $createdContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_created));
            $existContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_exist));
            $incorrectContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_incorrect));
        }

        // Get all regimes
        $regimes = Regime::all();
        $status = Statut::all();
        // $sf = SituationFamiliale::all();
        $grpBranches = Groupebranche::all();
        $branches = Branche::all();
        $garanties_all = Garantie::all();
        $gammes_all = Gamme::get();
        $Compagnies_all = Compagnie::get();
        // Auto
        $typePaie_all = Typepaiement::get();
        $typePermis_all = Typepermi::get();
        $modePaie_all = Modepaiement::get();


        $grpbrancheNom = $grpBranches->where('id',$file->grpbranche_id)->first() ? $grpBranches->where('id',$file->grpbranche_id)->first()->libelle : null;
        $brancheFile = $branches->where('id',$file->branche_id)->first() ? $branches->where('id',$file->branche_id)->first() : null;

        // ========  [Start] The following commented code bloc is removed from Laravel Excel
        /* $array_found = Excel::selectSheetsByIndex(0)->load($foundContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        $array_notFound = Excel::selectSheetsByIndex(0)->load($notFoundContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        $array_created = Excel::selectSheetsByIndex(0)->load($createdContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get(); 
        $array_exist = Excel::selectSheetsByIndex(0)->load($existContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();*/
        // ========= [End] 
        
        // Suggested replacement
        $array_found =  $this->processExcelFile($foundContratsFilePath);
        $array_notFound = $this->processExcelFile($notFoundContratsFilePath);
        $array_created = $this->processExcelFile($createdContratsFilePath);
        $array_exist = $this->processExcelFile($existContratsFilePath);

        // Not add any additional information to excel file
        if($file->nom_fichier_incorrect){
            // Check old contrats not have this feature
          /*  $array_incorrect = Excel::selectSheetsByIndex(0)->load($incorrectContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();*/

          $array_incorrect = $this->processExcelFile($incorrectContratsFilePath);
        }else{
            $array_incorrect = [];
        }
        // get all trace contrat_found with ids
        $found_contrats_num_polices = $array_found->pluck('n0_contrat')->all();
        // $notfound_contrats_num_polices = $array_notFound->pluck('n0_contrat')->all();
        $created_contrats_num_polices = $array_created->pluck('n0_contrat')->all();
        $exist_contrats_num_polices = $array_exist->pluck('n0_contrat')->all();
        $all_contrats_polices = array_merge($found_contrats_num_polices, $created_contrats_num_polices, $exist_contrats_num_polices);

        // => All contrats 4 files
        $AllContartsObj = Contrat::whereIn('num_police',$all_contrats_polices)
                                ->join('devis', function($join) use($all_contrats_polices){
                                    $join->on('devis.id', '=', 'contrats.devis_id')
                                    ->whereIn('contrats.num_police',$all_contrats_polices);
                                })
                                ->join('fiches', 'fiches.num_fiche', '=', 'devis.num_fiche')
                                ->join('tiers', 'fiches.tiers_id', '=', 'tiers.id')
                                ->select('contrats.*', 'tiers.id as tier_id', 'devis.num_devis', 'devis.formule')
                                ->get();
        $all_contrats_keys = Contrat::whereIn('num_police',$found_contrats_num_polices)->get()->map->only('id', 'num_police');
        $all_contrats_ids = $all_contrats_keys->pluck('id')->all();
        $contrats_traces = TraceContratCompany::where('importfile_id', $request->fileId)
                                ->whereIn('tracable_id',$all_contrats_ids)->where('tracable_type', 'App\Contrat')
                                ->where('slug', 'update-company-contrat')     //Only modified contrats
                                // ->groupBy('tracable_id')
                                ->latest()->get();

        // add numm police to collection to match later
        foreach($contrats_traces as $tr){
            $tr['num_police'] = $all_contrats_keys->where('id', $tr['tracable_id'])->first()['num_police'] ?? null;
        }

        

        // Change date format to fr
        // Add trace to found-contrats only
        $modified_array_found = [];
        if (!empty($array_found)) {
            foreach($array_found as $value){
                $value['date_deffet'] =  $value['date_deffet'] ?? null;
                $value['date_de_naissance'] =   $value['date_de_naissance'] ?? null;
                //$value['regime_libelle'] =  $regimes->where('id',$value->regime)->first()['libelle'] ??  null;
                $value['regime_libelle'] =  isset($value['regime']) ? $regimes->where('id', $value['regime'])->first()['libelle'] ?? null : null;
                if($brancheFile->slug == 'automobile'){
                    $value['date_permis'] =  $value['date_permis'] ?? null;

                    /*
                    $value['typePaie_libelle'] =  $typePaie_all->where('id',$value->type_paiement)->first()['libelle'] ??  null;
                    $value['modePaie_libelle'] =  $modePaie_all->where('id',$value->mode_paiement)->first()['libelle'] ??  null;
                    $value['typePermis_libelle'] =  $typePermis_all->where('id',$value->type_permis)->first()['libelle'] ??  null; */

                    // Fixed inconsistency  ( [] vs -> notation) which causes bugs
                    $value['typePaie_libelle'] =  $typePaie_all->where('id',$value['type_paiement'])->first()['libelle'] ??  null;
                    $value['modePaie_libelle'] =  $modePaie_all->where('id',$value['mode_paiement'])->first()['libelle'] ??  null;
                    $value['typePermis_libelle'] =  $typePermis_all->where('id',$value['type_permis'])->first()['libelle'] ??  null;
                }
    
                $conFound = $AllContartsObj->where('num_police', $value['n0_contrat'])->first();
                if($conFound){
                    $value['statut_libelle'] = $status->where('id',$conFound->statut_id)->first()['libelle'] ??  null;
                    $value['tier_hashid'] = Hashids::encode($conFound->tier_id);
                    $value['contrat_hashid'] = Hashids::encode($conFound->id);
                    $value['num_devis'] = $conFound->num_devis;
                    $value['formule'] =  json_decode($conFound['formule']);
                    // New
                    $value['branche'] = $branches->where('id',$conFound->devis->fiche->branche_id)->first() ??  null;
                }else{
                    $value['statut_libelle'] = null;
                    $value['tier_hashid'] = null;
                    $value['contrat_hashid'] = null;
                    $value['num_devis'] = null;
                    $value['formule'] = null;
                    // New
                    $value['branche'] = null;
                    $value['changeType'] = 'green';
                }
    
                // Find traces for 1 contrat (can be many)
                $value['traces'] = $contrats_traces->where('num_police', $value['n0_contrat'])->isNotEmpty() ? $contrats_traces->where('num_police', $value['n0_contrat']) : null;
                if($value['traces']){
                    $allTraceCombine = [];
                    foreach($value['traces'] as $trace){
                        // Override commentaires field json value as array
                        if(!is_array($trace->commentaires)){
                            $trace->commentaires = json_decode($trace->commentaires, true);
                        }
                        array_push($allTraceCombine, $trace->commentaires);                    
                    }
                    // Calcule change Type
                    $value['changeType'] = $this->calculeChangesTypeDB($allTraceCombine);
                }else{
                    $value['changeType'] = 'green';
                }
                $modified_array_found[] = $value;
            }
        }
        $array_found = $modified_array_found;

        $modified_array_notFound = [];
        foreach($array_notFound as $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
            //$value['regime_libelle'] =  $regimes->where('id',$value['regime'])->first()['libelle'] ??  null;
            $value['regime_libelle'] =  isset($value['regime']) ? $regimes->where('id', $value['regime'])->first()['libelle'] ?? null : null;
            if($brancheFile->slug == 'automobile'){
                $value['date_permis'] =  $value['date_permis'] ?? null;
                $value['typePaie_libelle'] =  $typePaie_all->where('id',$value['type_paiement'])->first()['libelle'] ??  null;
                $value['modePaie_libelle'] =  $modePaie_all->where('id',$value['mode_paiement'])->first()['libelle'] ??  null;
                $value['typePermis_libelle'] =  $typePermis_all->where('id',$value['type_permis'])->first()['libelle'] ??  null;
            }
            // Gamme not found
            $garantie = $garanties_all->where('id', $value['garantie'])->first();
            if(!$garantie) $garantie = $garanties_all->where('id', 1)->first();       //Default gar 1
            $gamme = $gammes_all->where('id', $garantie->gamme_id)->first();
            $compagnie = $Compagnies_all->where('id', $gamme->compagnie_id)->first();
            $value['garantie_libelle'] =  $garantie->libelle;
            $value['gamme_libelle'] =  $gamme->libelle;
            $value['compagnie_logo'] =  $compagnie->logo;
            $value['compagnie_libelle'] =  $compagnie->libelle;

            $value['branche'] = $branches->where('id',$file->branche_id)->first() ??  null;

            $modified_array_notFound[] = $value;
        }
        $array_notFound = $modified_array_notFound;

        $modified_array_created = [];
        foreach($array_created as $key => $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
          //  $value['regime_libelle'] =  $regimes->where('id',$value['regime'])->first()['libelle'] ??  null;
            $value['regime_libelle'] =  isset($value['regime']) ? $regimes->where('id', $value['regime'])->first()['libelle'] ?? null : null;
            if($brancheFile->slug == 'automobile'){
                $value['date_permis'] =  $value['date_permis'] ?? null;
                $value['typePaie_libelle'] =  $typePaie_all->where('id',$value['type_paiement'])->first()['libelle'] ??  null;
                $value['modePaie_libelle'] =  $modePaie_all->where('id',$value['mode_paiement'])->first()['libelle'] ??  null;
                $value['typePermis_libelle'] =  $typePermis_all->where('id',$value['type_permis'])->first()['libelle'] ??  null;
            }
            $conCreated = $AllContartsObj->where('num_police', $value['n0_contrat'] ?? $value['n0_contrat'] ?? null)->first();
            if($conCreated){
                $value['tier_hashid'] = Hashids::encode($conCreated->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conCreated->id);
                $value['num_devis'] = $conCreated->num_devis;
                $value['statut_libelle'] = $status->where('id',$conCreated->statut_id)->first()['libelle'] ??  null;
                $value['formule'] =  json_decode($conCreated['formule']);
                // New
                $value['branche'] = $branches->where('id',$conCreated->devis->fiche->branche_id)->first() ??  null;
            }else{
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['statut_libelle'] = null; 
                $value['formule'] = null;
                // New
                $value['branche'] = null;
            }
            
            $modified_array_created[] = $value;
        }
        $array_created = $modified_array_created;

        $modified_array_exist = [];
        foreach($array_exist as $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
          //  $value['regime_libelle'] =  $regimes->where('id',$value['regime'])->first()['libelle'] ??  null;
            $value['regime_libelle'] =  isset($value['regime']) ? $regimes->where('id', $value['regime'])->first()['libelle'] ?? null : null;
            if($brancheFile->slug == 'automobile'){
                $value['date_permis'] =  $value['date_permis'] ?? null;
                $value['typePaie_libelle'] =  $typePaie_all->where('id',$value['type_paiement'])->first()['libelle'] ??  null;
                $value['modePaie_libelle'] =  $modePaie_all->where('id',$value['mode_paiement'])->first()['libelle'] ??  null;
                $value['typePermis_libelle'] =  $typePermis_all->where('id',$value['type_permis'])->first()['libelle'] ??  null;
            }

            $conExist = $AllContartsObj->where('num_police', $value['n0_contrat'] ?? $value['n0_contrat'] ?? null)->first();
            if($conExist){
                $value['tier_hashid'] = Hashids::encode($conExist->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conExist->id);
                $value['num_devis'] = $conExist->num_devis;
                $value['statut_libelle'] = $status->where('id',$conExist->statut_id)->first()['libelle'] ??  null;
                $value['formule'] =  json_decode($conExist['formule']);
                // New
                $value['branche'] = $branches->where('id',$conExist->devis->fiche->branche_id)->first() ??  null;

            }else{
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['statut_libelle'] = null; 
                $value['formule'] = null;
                // New
                $value['branche'] =  null;
            }

            // Attach preview changes 
            $PFC = new PubliqueFicheController();

            $value = new Fluent($value);
            $newRequest = $this->createRequest($value, $file->id, 'updateContrat', $branche->slug);

            // $contratExist = Contrat::where('num_police', $value->n0_contrat)->first();
            $contratExist = $conExist;
            if($contratExist){
                $value['changeType'] = $this->calculeChangesType($contratExist, $newRequest);
                $contratchange = $this->updateContratCompany($contratExist, $newRequest, $file->id, $garanties_all, $gammes_all, $Compagnies_all, $branche, false);
                $contratchange['id'] = $contratExist->id;
                $contratchange['num_police'] = $contratExist->num_police;
                $value['traces'] = [
                    'commentaires' => $contratchange,
                ];
            }else{
                $value['changeType'] = 'green';
                $value['traces'] = [
                    'commentaires' => null,
                ];
            }

            $modified_array_exist[] = $value;
        }
        $array_exist = $modified_array_exist;

        // Add info to array incorrect if exist
        $modified_array_incorrect = [];
        if($array_incorrect){
            foreach($array_incorrect as $value){
                if($brancheFile->slug == 'automobile'){
                    // Auto
                    $validation = $this->validationNewAuto($value);
                }else{
                    $validation = $this->validationNew($value);
                }
                /*
                $value['regime_libelle'] =  $regimes->where('id',$value->regime)->first()['libelle'] ??  null;
                // Gamme not found
                $garantie = $garanties_all->where('id', $value->garantie)->first();
                if(!$garantie) $garantie = null;         // possible data column incorrect 
                if($garantie){
                    $gamme = $gammes_all->where('id', $garantie->gamme_id)->first();
                    $compagnie = $Compagnies_all->where('id', $gamme->compagnie_id)->first();
                }else{
                    $gamme = null;
                    $compagnie = null;
                }
                if($garantie && $gamme && $compagnie){
                    $value['garantie_libelle'] =  $garantie->libelle;
                    $value['gamme_libelle'] =  $gamme->libelle;
                    $value['compagnie_logo'] =  $compagnie->logo;
                    $value['compagnie_libelle'] =  $compagnie->libelle;
                }else{
                    $value['garantie_libelle'] =  null;
                    $value['gamme_libelle'] =  null;
                    $value['compagnie_logo'] =  null;
                    $value['compagnie_libelle'] =  null;
                }
                */
    
                $value['branche'] = $branches->where('id',$file->branche_id)->first() ??  null;
                $value['validationErrors'] = $validation;
                $modified_array_incorrect[] = $value;
            }


        }
        $array_incorrect = $modified_array_incorrect;
        
        // delete temp local files
        if($this->uploadMode == "server"){
            File::delete($foundContratsFilePath);
            File::delete($notFoundContratsFilePath);
            File::delete($createdContratsFilePath);
            File::delete($existContratsFilePath);
            if($file->nom_fichier_incorrect){
                File::delete($incorrectContratsFilePath);
            }
        }

        return response()->json([
            'contratsFound' => $array_found,
            'contratsNotFound' => $array_notFound,
            'contratsCreated' => $array_created,
            'contratsExist' => $array_exist,
            'contratsIncorrect' => $array_incorrect,
            'grpbrancheNom' => $grpbrancheNom,
            'branche' => $brancheFile,
        ]);
    }

    

    // This function update contrat in db and move it from contrat trouvé to contrat modifié
    public function updateContratMultiple(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        $folder = 'contrat_company';
        $file = $request->file;
        $file = importfiles_contratcompany::find($file['id']);
        $branche = Branche::find($file->branche_id);

        if($this->uploadMode == "server"){
            $importContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_importe'];
            $foundContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_found'];
            $existContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_exist'];
            $notFoundContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_notfound'];

            $importContratsFile = file_get_contents($importContratsFilePath);
            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $existContratsFile = file_get_contents($existContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);

            $explode_filename_import = explode('/', $file->nom_fichier_importe);
            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_exist = explode('/', $file->nom_fichier_exist);
            $explode_filename_notfound = explode('/', $file->nom_fichier_notfound);

            $importContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_import[count($explode_filename_import)-1]));
            $foundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $existContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_exist[count($explode_filename_exist)-1]));
            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notfound[count($explode_filename_notfound)-1]));

            file_put_contents($importContratsFilePath, $importContratsFile);
            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($existContratsFilePath, $existContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);

        }else{
            $importContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_importe']));
            $foundContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_found']));
            $existContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_exist']));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_notfound']));
        }

        $garanties_all = Garantie::all();
        $gammes_all = Gamme::get();
        $Compagnies_all = Compagnie::get();
        /*
        // removed methods
        $array_found = Excel::selectSheetsByIndex(0)->load($foundContratsFilePath, function ($reader) {
            // $reader->ignoreEmpty();    //THIS WILL DELETE EMPTY CELLS WITH HEADING => incorrect information
            $reader->formatDates(true, 'd/m/Y');
        }, null, true)->get();

        $array_exist = Excel::selectSheetsByIndex(0)->load($existContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        */

        $array_found = $this->processExcelFile($foundContratsFilePath);
        $array_exist = $this->processExcelFile($existContratsFilePath);

        //$array_found = new Fluent($array_found);
        //$array_exist = new Fluent($array_exist);

        $contratsFile = $request->contrat;
        // Prevent updating too many contrats
        if(count($contratsFile) > $this->fileLimit){
            return response()->json([
                'message' => 'Vous ne devez pas sélectionner plus de ' . $this->fileLimit . ' contrats',
                'type' => 'warning',
                'status' => 422
            ], 422);
        }

        $hasError = false;
        $errorResponses = [];

        try{
            foreach($contratsFile as $con){
                DB::connection('mysql2')->beginTransaction();
                try{
                    $contratFile = json_decode(json_encode($con));
                    $newRequest = $this->createRequest($contratFile, $file->id, 'updateContrat', $branche->slug);
    
                    $contratCrm = Contrat::where('num_police', $contratFile->n0_contrat)->first();
                    $existContrat_key = $array_exist->search(function($i) use($contratFile) {
                        return $i['n0_contrat'] == $contratFile->n0_contrat;
                    });
                    if (!$contratCrm) {
                        throw ValidationException::withMessages([
                            'contrat' => ['Le contrat n\'existe pas dans le CRM']
                        ]);
                    }
                    
                    if($contratCrm){
                        // Add update with formule check param in contrat file
                        $contratCrm->updateFormule = $request->updateFormule;
                        // Update contrat and save changes in db
                        $contratchange = $this->updateContratCompany($contratCrm, $newRequest, $file->id,  $garanties_all, $gammes_all, $Compagnies_all, $branche, true);
                    }
                    // update count found(modif) and count exist in importfile_contrat
                    $file->nbr_contrat_found = $file->nbr_contrat_found + 1;
                    $file->nbr_contrat_exist = $file->nbr_contrat_exist - 1;
                    $file->save();

                    // Remove contrat from exist to modify
                    $contratArray = $array_exist[$existContrat_key];
                    // Remove contrat from exist array and put it in found array => modify array
                    unset($array_exist[$existContrat_key]);
                    // Remove error columns before adding to array_found
                    $cleanedContrat = $this->cleanErrorColumns($contratArray);
                    $array_found->push($cleanedContrat);
    
                    Log::channel('injectionContrat')->info("Le contrat {$contratFile->n0_contrat} a été mis à jour avec succès");
                    DB::connection('mysql2')->commit();
    
                } catch (\Throwable $e) {
                    DB::connection('mysql2')->rollback();
                    $hasError = true;

                    // Refresh the file model after rollback
                    $file->refresh();
    
                    // Add error to the corresponding array based on file type
                    $array_exist = $this->handleErrorContrat($contratFile, $array_exist, $file, $e, 'updateContrat');
    
                    Log::channel('injectionContrat')->error("Erreur UpdateContratMultiple contrat {$contratFile->n0_contrat}: " . $e->getMessage(), [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'line' => $e->getLine(),
                    ]);
    
                    $errorInfo = $this->handleException($e, $contratFile->n0_contrat);
                    $errorResponses[] = $errorInfo;
                    continue;
                }
            }
        }catch (\Throwable $e) {
            // Catch any unexpected errors outside the main loop
            // Log::channel('injectionContrat')->error("Erreur inattendue UpdateContratMultiple: " . $e->getMessage());
        } finally {
            // Store final state files
            $this->storeFilesUpdatedContrats(
                $file, 
                $array_found, 
                $array_exist, 
                $importContratsFilePath,
                $foundContratsFilePath, 
                $existContratsFilePath, 
                $notFoundContratsFilePath, 
                $folder
            );
        }
        
        // Return appropriate response based on single/multiple updates
        if (count($contratsFile) <= 1 && count($errorResponses) > 0) {
            return response()->json($errorResponses[0], $errorResponses[0]['status']);
        } else {
            $msg = $hasError 
                ? 'Certains contrats n\'ont pas pu être mis à jour. Veuillez vérifier les détails.'
                : 'Les contrats ont été mis à jour avec succès.';
                
            return response()->json([
                'message' => $msg,
                'type' => ($hasError ? 'warning' : 'success')
            ]);
        }

    }



    public function contratsSearchperTier(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);

        $contrat = json_decode($request->contrat);
        $branche = json_decode($request->branche);
            
        $PFC = new PubliqueFicheController();
        
        $requestTierCheck = new \Illuminate\http\Request();
        $requestTierCheck->replace([
            "nom" => !empty($contrat->nom) ? $contrat->nom : null,
            "prenom" => !empty($contrat->prenom) ? $contrat->prenom : null,
            "date_naissance" => !empty($contrat->date_de_naissance) ? Carbon::createFromFormat('d/m/Y', $contrat->date_de_naissance)->format('Y-m-d') : null,
            "email" => !empty($contrat->email) ? $contrat->email : null,
            "tel" => !empty($contrat->portable) ? $PFC->numeroTel($contrat->portable) : null,
        ]);

        $tiersController = new TiersController();
        if($branche->slug == 'automobile'){
            $searchContrats = $tiersController->checkTiersExist($requestTierCheck)
                        ->join('fiches', 'fiches.tiers_id', '=', 'tiers.id')
                        ->join('devis', 'devis.num_fiche', '=', 'fiches.num_fiche')
                        ->join('contrats', 'devis.id', '=', 'contrats.devis_id')
                        ->join('vehicules', 'devis.vehicule_id', '=', 'vehicules.id')
                        ->select('contrats.*', 
                                'dpps.nom', 'dpps.prenom', 'dpps.date_naissance', 'dpps.regime_id',
                                'dpps.num_permis', 'dpps.lieu_permis', 'dpps.date_permis', 'dpps.type_permis',
                                'tiers.id as tier_id', 'tiers.portable', 'tiers.num_post_tel', 'tiers.num_tel', 'tiers.fax_pro', 'tiers.email', 'tiers.numemail', 'tiers.email_pro',
                                'fiches.branche_id', 'fiches.date_effet',
                                'devis.num_devis', 'devis.formule',
                                'devis.typepaiements_id', 'devis.modepaiements_id', 'devis.prorata', 'devis.frais_dossier', 'devis.reste_a_payer',
                                'vehicules.model', 'vehicules.immatriculation', 'vehicules.code_sra'
                                )
                        ->groupBy('contrats.id')
                        ->paginate(10);
        }else{
            // Sante
            $searchContrats = $tiersController->checkTiersExist($requestTierCheck)
                        ->join('fiches', 'fiches.tiers_id', '=', 'tiers.id')
                        ->join('devis', 'devis.num_fiche', '=', 'fiches.num_fiche')
                        ->join('contrats', 'devis.id', '=', 'contrats.devis_id')
                        ->select('contrats.*', 
                                'dpps.nom', 'dpps.prenom', 'dpps.date_naissance', 'dpps.regime_id',
                                'dpps.num_permis', 'dpps.lieu_permis', 'dpps.date_permis', 'dpps.type_permis',
                                'tiers.id as tier_id', 'tiers.portable', 'tiers.num_post_tel', 'tiers.num_tel', 'tiers.fax_pro', 'tiers.email', 'tiers.numemail', 'tiers.email_pro',
                                'fiches.branche_id', 'fiches.date_effet',
                                'devis.num_devis', 'devis.formule',
                                'devis.typepaiements_id', 'devis.modepaiements_id', 'devis.prorata', 'devis.frais_dossier', 'devis.reste_a_payer'
                                )
                        ->groupBy('contrats.id')
                        ->paginate(10);
        }
        

        $regimes = Regime::all();
        $branches = Branche::all();
        $status = Statut::all();

        // Auto
        $typePaie_all = Typepaiement::get();
        $modePaie_all = Modepaiement::get();

        $searchContrats->each(function($con) use($regimes, $branches, $status, $typePaie_all, $modePaie_all){
            // $con->branche_libelle = $branches->where('id',$con->branche_id)->first()['libelle'] ??  null;
            $con->regime_libelle = $regimes->where('id',$con->regime_id)->first()['libelle'] ??  null;
            $con->statut_libelle = $status->where('id',$con->statut_id)->first()['libelle'] ??  null;
            $con->branche = $branches->where('id',$con->branche_id)->first() ??  null;
            if($con->date_naissance) $con->date_naissance = Carbon::parse($con->date_naissance)->format('d/m/Y') ;
            if($con->date_effet) $con->date_effet = Carbon::parse($con->date_effet)->format('d/m/Y');
            
            // Auto
            $con->typePaie_libell =  $typePaie_all->where('id',$con->type_paiement)->first()['libelle'] ??  null;
            $con->modePaie_libell =  $modePaie_all->where('id',$con->mode_paiement)->first()['libelle'] ??  null;
            
            $con->tier_hashid = Hashids::encode($con->tier_id);
            $con->contrat_hashid = Hashids::encode($con->id);
            // decode formule devis
            $con->formule = json_decode($con->formule);
        });

        return ['contratsTier' => $searchContrats];

    }

    public function linkContrats(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];
        $folder = 'contrat_company';
        
        $contratCrm = json_decode(json_encode($request->contratCrm), false);
        $contratFile = json_decode(json_encode($request->contratFile), false);
        $file = json_decode(json_encode($request->file), false);
        
        //Remove frome notfound file to found file (exist), after update contrat num_police from portfeuille to crm 
        $file = importfiles_contratcompany::find($file->id);
        if($this->uploadMode ==  "server"){
            $foundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_found;
            $notFoundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_notfound;
            $existContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_exist;
            $importContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_importe;

            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $existContratsFile = file_get_contents($existContratsFilePath);
            $importContratsFile = file_get_contents($importContratsFilePath);

            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_notFound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_exist = explode('/', $file->nom_fichier_exist);
            $explode_filename_import = explode('/', $file->nom_fichier_importe);

            $foundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notFound[count($explode_filename_notFound)-1]));
            $existContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_exist[count($explode_filename_exist)-1]));
            $importContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_import[count($explode_filename_import)-1]));

            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($existContratsFilePath, $existContratsFile);
            file_put_contents($importContratsFilePath, $importContratsFile);

        }else{
            $foundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_found));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $existContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_exist));
            $importContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_importe));
        }


        $array_found = $this->processExcelFile($foundContratsFilePath);
        $array_exist = $this->processExcelFile($existContratsFilePath);
        $array_notFound = $this->processExcelFile($notFoundContratsFilePath);

        // Remove contrat from exist to modify
        $notFoundContrat_key = $array_notFound->search(function($i) use($contratFile) {
            return $i['n0_contrat'] == $contratFile->n0_contrat;
        });
        $contratArray = $array_notFound[$notFoundContrat_key];

        //Remove contrat from notfound array and put it in found array (exist array)
        unset($array_notFound[$notFoundContrat_key]);
        $array_exist->push($contratArray);

        // Ecrase exiting file with new data
        $explode_filename_exist = explode('/', $file->nom_fichier_exist);
        $raw_filename_exist = explode('.', $explode_filename_exist[count($explode_filename_exist)-1]);
        $raw_filename_exist = $raw_filename_exist[0];
        $explode_filename_notfound = explode('/', $file->nom_fichier_notfound);
        $raw_filename_notfound = explode('.', $explode_filename_notfound[count($explode_filename_notfound)-1]);
        $raw_filename_notfound = $raw_filename_notfound[0];

        
        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        $contrat_exist_path = $this->storeExcelFile(
            new ArrayDataExport($array_exist),
            "{$raw_filename_exist}.xlsx",
            $storagePath
        );
        $contrat_notFound_path = $this->storeExcelFile(
            new ArrayDataExport($array_notFound),
            "{$raw_filename_notfound}.xlsx",
            $storagePath
        );

        if($this->uploadMode == "server"){
            $contrat_exist_path = FonctionController::saveFile($contrat_exist_path, ($explode_filename_exist[count($explode_filename_exist)-1]), $folder);
            $contrat_notFound_path = FonctionController::saveFile($contrat_notFound_path, ($explode_filename_notfound[count($explode_filename_notfound)-1]), $folder);

        }

        $contratCrmObject = Contrat::find($contratCrm->id);
        $newRequest = new \Illuminate\http\Request();
        $newRequest->replace([
            "ncontrat" => $contratArray['n0_contrat'] ?? null,
        ]);

        // Update only num_police so contrat exist can retrieve contrats by num police (company num not exist in crm)
        $num_police_change = $this->updateNumPolice($contratCrmObject, $newRequest, [], true);
        if(!empty($num_police_change)){
            $changed_data = [
                'contrat' => $num_police_change,
                'tier' => [],
            ];
            $tabInfoTrace = [
                'slug'                 => 'update-company-contrat', 
                'display_name'         => 'mettre à jour contrat', 
                'commentaires'         => json_encode($changed_data),
                'tracable_id'          =>  $contratCrm->id, 
                'tracable_type'        =>  "App\Contrat",  
                // 'num_police'           =>  $contrat->num_police,  
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
        }


        // update count found and count not found in importfile_contrat
        $file->nbr_contrat_exist = $file->nbr_contrat_exist + 1;
        $file->nbr_contrat_notfound = $file->nbr_contrat_notfound - 1;
        $file->save();

        if($this->uploadMode == "server"){
            // delete temp local files
            File::delete($foundContratsFilePath);
            File::delete($notFoundContratsFilePath);
            File::delete($existContratsFilePath);
            File::delete($importContratsFilePath);
        }

        return response()->json('Liaison faite avec succès');

    }

    // ======================================**************************************************=========================>
    //                                      Create Not existed contrats functions
    // ======================================**************************************************=========================>


    public function createNotExistedContratsMultiple(Request $request){
        // 1- create tier
        // 2- create dpps
        // 3- create fams
        // Check if exist deja contrats and user miss it
        // 4- create fiche
        // 5- create devi
        // 6- create contrat

        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800); 

        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];


        $PFC = new PubliqueFicheController();
        $tiersController = new TiersController();
        $folder = 'contrat_company';

        $duplicateForce = $request->duplicate;
        $file = $request->file;
        $file = importfiles_contratcompany::find($file['id']);

        // Status fiche devi contrat
        $statut_id_fiche = Statut::where('slug', 'ferme-passage-contrat')->first()->id;
        $statut_id_devi = Statut::where('slug', 'devis-ferme-passage-contrat')->first()->id;
        $statut_id_contrat = Statut::where('slug', 'contrat-client')->first()->id;
        //Fiche Info
        $branche = Branche::find($file->branche_id);
        if($branche){
            $branche_id = $branche->id;
        }else{
            $branche_id = Branche::find('slug', 'sante')->first()->id;      // Default branche sante
        }
        // $societe_id = Auth::user()->societes->first() ? Auth::user()->societes->first()->id : 1;  // Edit Later; 
        $societe_id = $file->societe_id;
        if($branche->groupepub_injection_id){
            $groupe_id = Groupepub::find($branche->groupepub_injection_id)->id;
        }else{
            $groupe_id = Groupepub::where('slug', 'prt-compagnie')->first()->id;      // default portefuille compagnie sante 
        }
           
        $prov_id = Provenance::where('slug', 'prt_compagnie')->first()->id;      
        $grp_prv_id = GroupepubProvenance::where('groupepub_id', $groupe_id)->where('provenance_id', $prov_id)->first()->id; //grouppub prov  => PRT_compagnie || PRT_compagnie
        

        if($this->uploadMode == "server"){
            $foundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_found;
            $notFoundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_notfound;
            $createdContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_created;
            $importContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_importe;

            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $createdContratsFile = file_get_contents($createdContratsFilePath);
            $importContratsFile = file_get_contents($importContratsFilePath);

            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_notFound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_created = explode('/', $file->nom_fichier_created);
            $explode_filename_import = explode('/', $file->nom_fichier_importe);

            $foundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notFound[count($explode_filename_notFound)-1]));
            $createdContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_created[count($explode_filename_created)-1]));
            $importContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_import[count($explode_filename_import)-1]));

            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($createdContratsFilePath, $createdContratsFile);
            file_put_contents($importContratsFilePath, $importContratsFile);
            
        }else{
            $foundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_found));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $createdContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_created));
            $importContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_importe));
        }
        
        $array_notFound = $this->processExcelFile($notFoundContratsFilePath);
        $array_created = $this->processExcelFile($createdContratsFilePath);
        
        $contratsNotExist = $request->contrat;
        // Prevent creation too much contrats
        if(count($contratsNotExist) > $this->contratsCreationLimit){
            return response()->json([
                'message' => 'Vous ne devez pas sélectionner plus de '.$this->contratsCreationLimit.' contrats',
                'type' => 'warning',
                'status' => 422
            ], 422);
        }

        $hasError = false;
        $errorResponses = [];
        try{
            foreach($contratsNotExist as $con){
                try{
                    DB::connection('mysql2')->beginTransaction();
                    $contratNotExist = json_decode(json_encode($con));
                    $contratNotExist_key = $array_notFound->search(function($i) use($contratNotExist) {
                        return $i['n0_contrat'] == $contratNotExist->n0_contrat;
                    });
                    $newRequest = $this->createRequest($contratNotExist, $file->id, 'creationContrat', $branche->slug);
                    $dateEnregistrement = $newRequest->date_denregistrement;
                    //check tiers exist
                    $query = $tiersController->checkTiersExist($newRequest);
                    $tiersExist = $query->select('tiers.*')->first();
                    //if not exist create new tiers
                    if ($tiersExist) {
                        $tiers = $tiersExist;
                    } else {
                        $tiers = $tiersController->createTiers($newRequest);
                        $tiers->num_tel = $newRequest->num_tel;
                        $tiers->portable  = $newRequest->portable;
                        $tiers->save();
                    }
                    
                    if ($tiers->id) {
                        //check if tiers has Dpp, if not create new Dpp
                        $dpp = $tiers->num_dpp ? Dpps::where('id', $tiers->num_dpp)->first() : null;
                        if (!$dpp) {
                            $dpp = $tiersController->createDpp($newRequest, $tiers);
                            $tiers->num_dpp = $dpp->id;
                            $tiers->save();
                        }
                        //check if tiers has Fam, if not create new fam
                        $fam = Fam::where('tiers_id', $tiers->id)->where('parent_id', 1)->first();
                        if (!$fam && $dpp->id) {
                            $fam = $tiersController->createFam($dpp, $tiers);
                        }
                    }

                    $tiersController->traces($tiers);
                    // $tiersController->notifier($tiers);

                    /*** skip lead check existe  ***/
                    /*
                    //create lead if not exist
                    $lead = $this->checkLeadExist($newRequest, $tiers, $branche_id);
                    if(!$lead){
                        $lead = $this->createLeadPortefeuille($newRequest, $tiers, $branche_id, $societe_id, $groupe_id, $grp_prv_id);
                    }
                    */
                    $lead = $this->createLeadPortefeuille($newRequest, $tiers, $branche_id, $societe_id, $groupe_id, $grp_prv_id);



                    if ($duplicateForce) {
                        // Skip checks and go directly to fiche creation
                        $newRequest->importfile_id = true;
                        $newRequest->PRT_compagnie = true;
                        // Add soc/site/equipe/cc ids to request for fiche dispatch
                        $this->dispatchconseillerId($newRequest, $file);
                        // Create fiche not exist in the crm
                        $fiche = $PFC->createFiche($newRequest, $branche_id, $tiers, $grp_prv_id, $lead->id, $courtier_id, $societe_id, $groupe_id);
                        // $fiche = Fiche::where('tiers_id', $tiers->id)->latest()->first();        // OLD REPLACED WITH $fiche = $PFC->createFiche
                        
                        // update fiche status and num_fiche after creation
                        $fiche->statut_id = $statut_id_fiche; //Fermé passage contrat
                        $fiche->lu = 1; // lu par défaut
                        $fiche->nbr_vu = 1;
                        $fiche->num_fiche = $PFC->mask(2, $fiche->branche_id).$PFC->mask(7, $fiche->id);
                        $fiche->save();
                    
                        $statutAction = new StatutAction;
                        $statutAction->user_id = Auth::user()->id;
                        $statutAction->entitable_id = $fiche->id;
                        $statutAction->entitable_type = "Fiche";
                        $statutAction->statut_id = $statut_id_fiche;
                        $statutAction->complement = "Création fiche automatique PRT par ".Auth::user()->prenom.' '.Auth::user()->nom;
                        $statutAction->save();
                    } else {
                        // Original check logic when not forcing duplicate
                        $ficheExist = $this->checkFicheExistPortefeuille($newRequest, $branche_id, $tiers, $grp_prv_id, $lead->id, $groupe_id, $statut_id_fiche);
                        if(!empty($ficheExist['contratExiste'])){
                            throw new HttpException(460, 'Contrat existe déjà [Num contrat : '.$ficheExist['contratExiste']->num_devis. ']');
                        } elseif (!empty($ficheExist['fichePRTCompagnieNoContart'])) {
                            /**  Skip this filter => if contrat not exist and fiche existe => create new fiche for the new contrat **/
                            /** Only take fiche with no contrat with grp_pub prt_compagnie + ferme_passage_contrat **/
                            $fiche = $ficheExist['fichePRTCompagnieNoContart'];
                        }
                        else{
                            // Same fiche creation logic as above
                            $newRequest->importfile_id = true;
                            $newRequest->PRT_compagnie = true;
                            // Add soc/site/equipe/cc ids to request fot fiche dispatch
                            $this->dispatchconseillerId($newRequest, $file);
                            // Create fiche not exist in the crm
                            $fiche = $PFC->createFiche($newRequest, $branche_id, $tiers, $grp_prv_id, $lead->id, $courtier_id, $societe_id, $groupe_id);
                            
                            // update fiche status and num_fiche after creation
                            $fiche->statut_id = $statut_id_fiche;  //Fermé passage contrat
                            $fiche->lu = 1;
                            $fiche->nbr_vu = 1;
                            $fiche->num_fiche = $PFC->mask(2, $fiche->branche_id).$PFC->mask(7, $fiche->id);
                            $fiche->save();
                    
                            $statutAction = new StatutAction;
                            $statutAction->user_id = Auth::user()->id;
                            $statutAction->entitable_id = $fiche->id;
                            $statutAction->entitable_type = "Fiche";
                            $statutAction->statut_id = $statut_id_fiche;
                            $statutAction->complement = "Création fiche automatique PRT par ".Auth::user()->prenom.' '.Auth::user()->nom;
                            $statutAction->save();
                        }
                    }


                    $garantie = Garantie::find($newRequest->garantie);
                    if(!$garantie) $garantie = Garantie::find(1);  // Default first garantie
                    $tarif = $newRequest->cotisation_mensuelle_ttc; 
                    $formule = $this->setFormule($garantie, $tarif, $branche->slug, $newRequest);
                    
                    $devis = $this->checkDevisExistPortefeuille($fiche, $statut_id_devi);
                    if(!$devis){
                        $cons = Auth::user(); 
                        //create Proposition
                        $proposition = new Proposition();
                        $proposition->user_id = $cons;
                        $proposition->garanties = json_encode([$formule]); // nedd check
                        $proposition->num_fiche = $fiche->num_fiche;
                        $proposition->tiers_id = $tiers->id;
                        $proposition->active = 1;
                        $proposition->save();

                        //create Devis
                        $dateEnregistrement = $newRequest->date_denregistrement;
                        $devis = $this->createDevisPortefeuille($newRequest, $formule, $tarif, $proposition, $cons, $fiche, $tiers, $dateEnregistrement, null, $statut_id_devi, $branche);

                    }

                    //create Contrat
                    $contrat = $this->createContratPortefeuille($newRequest, $devis, $fiche, $statut_id_contrat, $dateEnregistrement);

                    // Additional auto
                    if($branche->slug == 'automobile'){
                        $this->autoCreateContrat($dpp,$tiers, $fiche, $devis, $newRequest, $tarif, $branche);
                    }

                    // Remove from contrats not found and put it in newcreated contrats
                    $contratArray = $array_notFound[$contratNotExist_key];
                    //Remove contrat from notfound array and put it in created array
                    if($contratNotExist_key !== false) {
                        unset($array_notFound[$contratNotExist_key]);
                        // Remove error columns before adding to array_found
                        $cleanedContrat = $this->cleanErrorColumns($contratArray);
                        $array_created->push($cleanedContrat);
                    }
                    // update count found and count created in importfile_contrat
                    $file->nbr_contrat_notfound = $file->nbr_contrat_notfound - 1;
                    $file->nbr_contrat_created = $file->nbr_contrat_created + 1;
                    $file->save();

                    Log::channel('injectionContrat')->info("successfully created contract {$contratNotExist->n0_contrat}: ");
                    DB::connection('mysql2')->commit();

                } catch (\Throwable $e) {
                    DB::connection('mysql2')->rollback();
                    $hasError = true;

                    // Refresh the file model after rollback
                    $file->refresh();

                    // Add error to the corresponding array based on file type
                    $array_notFound = $this->handleErrorContrat($contratNotExist, $array_notFound, $file, $e, false);

                    Log::channel('injectionContrat')->error("Erreur CreatedNotExistedContrats contrat {$contratNotExist->n0_contrat}: " . $e->getMessage(), [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'line' => $e->getLine(),
                    ]);

                    $errorInfo = $this->handleException($e, $contratNotExist->n0_contrat);
                    $errorResponses[] = $errorInfo;
                    continue;
                }

            }
        
        }catch (\Throwable $e) {
            // 
        }finally{
            // Store final state files
            $this->storeFilesCreatedContrats($file, $array_notFound, $array_created, $foundContratsFilePath, 
            $notFoundContratsFilePath, $createdContratsFilePath, $importContratsFilePath, $folder );
        }
        

        if(count($contratsNotExist) <= 1 && count($errorResponses) > 0){
            return response()->json($errorResponses[0], $errorResponses[0]['status']);
        }else{

            $msg = $hasError 
                ? 'Certains contrats n\'ont pas pu être créés. Veuillez vérifier les détails.'
                : 'Les contrats ont été créés avec succès';
                
            return response()->json([
                'message' => $msg,
                'type' => ($hasError ? 'warning' : 'success')
            ]);
        }
                

    }



    // =============================================================>
    
    public function checkLeadExist($request, $tiers, $branche_id){
        $nom = $tiers->dpps->nom;
        $prenom = $tiers->dpps->prenom;
        // $nom = addslashes($tiers->dpps->nom);
        // $prenom = addslashes($tiers->dpps->prenom);
        $lead = Lead::where('branche_id', $branche_id)
                ->whereRaw("JSON_CONTAINS(data, JSON_OBJECT('date_effet', '$request->date_effet'))")
                ->whereRaw('JSON_CONTAINS(data, JSON_OBJECT("nom", "'.$nom.'"))')
                ->whereRaw('JSON_CONTAINS(data, JSON_OBJECT("prenom", "'.$prenom.'"))')
                ->first();
        return $lead;
    }


    public function createLeadPortefeuille($request, $tiers, $branche_id, $societe_id, $groupe_id, $grp_prv_id){
        $data = [];
                
        //Assuré
        $data['nom'] = $tiers->dpps->nom;
        $data['prenom'] = $tiers->dpps->prenom;
        $data['date_effet'] = $request->date_effet;
        $data['tel_mobile'] = $tiers->portable;
        $data['tel_bureau'] = $tiers->num_post_tel;
        $data['tel_domicile'] = $tiers->num_tel;
        $data['fax'] = $tiers->fax_pro;
        $data['email'] = $tiers->email;
        $data['code_postal'] = $tiers->code_postal;
        $data['ville'] = $tiers->ville;
        $data['adresse'] = empty($tiers->adresse1) ? $tiers->adresse2 : $tiers->adresse1 . ' ' . $tiers->adresse2;
        $data['date_naissance'] = $tiers->dpps->date_naissance;
        $data['regime'] = $tiers->dpps->regime_id;
        $data['profession'] = $tiers->dpps->profession_id;
        $data['sexe'] = $tiers->dpps->sexe;

        // Construction de clé
        $courtierId = Societe::find($societe_id)->courtier_id;
        $cle = Hashids::encode($groupe_id, $grp_prv_id, $courtierId, $societe_id);
        $data['cle'] = $cle;

        $newLead = new Lead;
        $newLead->branche_id = $branche_id;
        $newLead->data = json_encode($data);
        $newLead->saisie_manuelle = 1;
        $newLead->active = 1;
        $newLead->save();
        
        return $newLead;
    }

    public function dispatchconseillerId(Request $newRequest, $file){
        $newRequest->conseillerId = $file->conseiller_id;
        $newRequest->equipeId = $file->equipe_id;
        $newRequest->siteId = $file->site_id;
        $newRequest->societeId = $file->societe_id;
    
    }

    public function checkFicheExistPortefeuille($newRequest, $branche_id, $tiers, $grp_prv_id, $lead_id, $groupe_id, $statut_id_fiche){        

        /** Old check **/
        // $ficheNoContart = Fiche::whereTiersId($tiers->id)
        //         ->whereBrancheId($branche_id)
        //         ->whereGroupepubProvenanceId($grp_prv_id)
        //         ->whereDoesntHave('contratt')
        //         ->latest()
        //         ->first();

        // $fichewithContart = Fiche::whereTiersId($tiers->id)
        //         ->whereBrancheId($branche_id)
        //         ->whereGroupepubProvenanceId($grp_prv_id)
        //         ->whereHas('contratt')
        //         ->latest()
        //         ->first();

        // return[
        //     'ficheNoContart'   => $ficheNoContart,
        //     'fichewithContart'   => $fichewithContart,
        // ];

        //  ===> New check
        $garantie = Garantie::find($newRequest->garantie);
        if(!$garantie) $garantie = Garantie::find(1);  // Default first garantie
        $gamme = Gamme::find($garantie->gamme_id);
        $cieCode = $gamme->compagnie->code;
        
        $branche = Branche::find($branche_id);

        
        /** Query 1 **/
        // $fiche_sub = Fiche::where('fiches.tiers_id', $tiers->id)
        //                 ->where('fiches.branche_id', $branche_id);
        // $devis_sub = Devis::where('devis.formule->cieCode', $cieCode)
        //                 ->where('devis.tiers_id', $tiers->id);
        // $contrats_sub = Contrat::whereIn('contrats.statut_id', [20, 21, 75, 107])      // Contart client
        //                     ->whereHas('devis', function ($q) use ($tiers) {
        //                         $q->where('devis.tiers_id', $tiers->id);
        //                     });
        // $vehicule_sub = Vehicule::where('immatriculation', $newRequest->immatriculation)
        //                     ->where('active', 1);

        // $contratExiste = DB::connection('mysql2')
        //                 ->table(DB::connection('mysql2')->raw("({$fiche_sub->toSql()}) as fiches")) 
        //                 ->mergeBindings($fiche_sub->getQuery())
        //                 ->select('fiches.id as fiche_id', 'fiches.num_fiche', 'devis.num_devis', 'contrats.id as contrat_id')
        //                 ->join(DB::connection('mysql2')->raw("({$devis_sub->toSql()}) as devis"), 'devis.num_fiche', '=', 'fiches.num_fiche') 
        //                 ->mergeBindings($devis_sub->getQuery())	
        //                 ->join(DB::connection('mysql2')->raw("({$contrats_sub->toSql()}) as contrats"), 'devis.id', '=', 'contrats.devis_id') 
        //                 ->mergeBindings($contrats_sub->getQuery())
        //                 ->when($branche->slug == 'automobile', function($query) use($newRequest, $vehicule_sub){
        //                     $query->join(DB::connection('mysql2')->raw("({$vehicule_sub->toSql()}) as vehicules"), 'devis.vehicule_id', '=', 'vehicules.id') 
        //                     ->mergeBindings($vehicule_sub->getQuery());
        //                 })
        //                 ->first();
        /****/

        /** Query 2 **/
        $contratExiste = Devis::where('devis.formule->cieCode', $cieCode)->where('devis.tiers_id', $tiers->id)
                ->whereHas('contrat', function ($q)  {
                    $q->whereIn('contrats.statut_id', [20, 21, 75, 107]); 
                })
                ->whereHas('fiche', function ($q) use($branche_id) {
                    $q->whereBrancheId($branche_id); 
                })
                // Auto contrat doublon when same vehicule
                ->when($branche->slug == 'automobile', function($query) use($newRequest){
                    $query->whereHas('vehicule2', function($q) use($newRequest) {
                        $q->where('immatriculation', $newRequest->immatriculation)->where('active', 1);
                    });
                })
                // ->whereHas('contratt')
                ->first();

        // $ficheNoContart = Fiche::whereTiersId($tiers->id)
        //             ->whereBrancheId($branche_id)
        //             ->whereGroupepubProvenanceId($grp_prv_id)
        //             ->where('statut_id', $statut_id_fiche)      //Fermé passage contrat
        //             ->whereDoesntHave('contratt')               //N-W
        //             ->first();

        $ficheNoContart = null;

        return [
            'contratExiste' => $contratExiste,
            'fichePRTCompagnieNoContart' => $ficheNoContart,
        ];

    }
    public function checkDevisExistPortefeuille($fiche, $statut_id_devi){
        $devi = devis::where('num_fiche', $fiche->num_fiche)->where('statut_id', $statut_id_devi)->first();
        return $devi;
    }

    public function setFormule($garantie, $tarif, $branche= 'sante', $newRequest){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        $gamme = Gamme::find($garantie->gamme_id);
        $newGarantie = new \stdClass();

        $newGarantie->id = $garantie->id;
        $newGarantie->gid = $gamme->id;
        $newGarantie->logo = $gamme->compagnie->logo;
        // $newGarantie->tarif = number_format($tarif, 2);
        // $newGarantie->packTarif = number_format($tarif, 2);   //packTarif = Number(this.contrat.formule.tarifTotal);
        // $newGarantie->tarifTotal = number_format($tarif, 2);  //tarifTotal = Number(Number(this.contrat.formule.tarifInitial) + renfortsTarif).toFixed(2);
        $newGarantie->tarif = $tarif;
        $newGarantie->packTarif = $tarif;   //packTarif = Number(this.contrat.formule.tarifTotal);
        $newGarantie->tarifTotal = $tarif;  //tarifTotal = Number(Number(this.contrat.formule.tarifInitial) + renfortsTarif).toFixed(2);
        // $newGarantie->renfortsTarif = number_format($renfortsTarif, 2);
                
        $newGarantie->gamme = $gamme->libelle;
        $newGarantie->order = $garantie->order;

        $newGarantie->libelle = $garantie->libelle;
        $newGarantie->cieCode = $gamme->compagnie->code;
        $newGarantie->idGamme = $gamme->id;
        $newGarantie->garantie = $garantie->libelle;
        $newGarantie->cie_code = $gamme->compagnie->code;
        $newGarantie->minPoint = $garantie->min_point;
        $newGarantie->maxPoint = $garantie->max_point;
        $newGarantie->codeGamme = $gamme->code;
        $newGarantie->compagnie = $gamme->compagnie->libelle;
        $newGarantie->typeFormule = $garantie->typeFormule->libelle;
        $newGarantie->codeGarantie = $garantie->code;
        
        $newGarantie->documents = collect($gamme->documents)->mapWithKeys(function ($doc, $key) {
            return [$doc['code'] => $doc];
        })->all();
        // $newGarantie->documents = null;

        if($branche == 'automobile'){
            $newGarantie->formule['mode']['id'] = $newRequest->type_paiement;
        }

        return $newGarantie;
    }

    public function createDevisPortefeuille($newRequest, $formule, $tarif, $proposition, $user, $fiche, $tiers, $dateEnregistrement, $typeFiche, $statut_id_devi, $branche)
    {
        $devisController = new DevisController();
        $nbrDevis = count(Devis::whereNumFiche($fiche['num_fiche'])->get());

        //DEVIS
        $userDevis = User::find($newRequest->conseillerId);
        $entite = $userDevis->entite();

        $devis = new Devis();
        $devis->entitable_id = $entite['entitable_id'];
        $devis->entitable_type = $entite['entitable_type'];
        $devis->num_fiche = $fiche['num_fiche'];
        $devis->num_devis = $devis->num_fiche.str_pad(($nbrDevis+1), 2, "0", STR_PAD_LEFT);
        $devis->user_id = $newRequest->conseillerId;
        $devis->devis_origin = 0;
        // 
        $devis->date_validation = $dateEnregistrement ? $dateEnregistrement : '1111-11-11'; 
        $devis->date_revalidation = $dateEnregistrement ? $dateEnregistrement : '1111-11-11'; 
        $devis->date_effet = $newRequest->date_effet;
        // $devis->lien_pdf = $lienPdf;
        $devis->tiers_id = $tiers['id'];
        $devis->statut_id = $statut_id_devi; //passage contrat
        $devis->proposition_id = $proposition->id;
        $devis->active = 1;
        $devis->etat = 1; //passage contrat //CHANGE TO 0 LATER
        $formule->devis_id = Devis::max('id') + 1;;
        
        // $formuleArray = (array) $formule;
        $devis->formule = json_encode($formule, TRUE);
        
        //get typefiche
        if (!$typeFiche) {
            //get typefiche from Group pub
            $grpprov = GroupepubProvenance::where('id', $devis->fiche->groupepub_provenance_id )->first();
            $grpp = Groupepub::where('id', $grpprov->groupepub_id)->first();
            $devis->typefiche_id = $grpp->typefiche_id;
           
            
        } else {
            $typefiche = Typefiche::where('libelle', $typeFiche)->first();
            $devis->typefiche_id = $typefiche ? $typefiche->id : null;
        }
        
        $devisPortefeuille = true;
        $devis->montant = $devisController->claculCad($devis, $devisPortefeuille); 

        // => Comment until comm project pushed to prod 
        //get devis cible
        // try {
        //     $cible_id = $devis->getDevisCible();
        // } catch (Exception $e) {
        //     // $message = $e->validator->errors()->first();
        //     $cible_id = 1;   // Default cible_id when not found in config
        // }
        // $devis->cible_id = $cible_id;

        $devis->saveFormuleValues($formule);
        //TRACE 
        $tabInfoTraceMatiere = [  
            'slug' => "création-devi-prt",
            'active' => 1, 
            'user_id' => $devis->user_id,
            'fiche_id' => $fiche->id,
            'statut_id' => $devis->statut_id,
            'courtier_id' => Auth::user()->courtier_id, 
            // 'commentaire' => "Création devis ".$garantie['gamme']." - ".$garantie['garantie'],
            'commentaire' => "création devi automatique injection compagnie par ".Auth::user()->prenom.' '.Auth::user()->nom,
            'userable_id' => $fiche->userable_id,
            'entitable_id' => $devis->id,
            'userable_type' => $fiche->userable_type,
            'entitable_type' => 'Devis',
            'dispatchable_id' => $fiche->dispatchable_id,
            'dispatchable_type' => $fiche->dispatchable_type,
        ]; 

        event(new EventTraceMatieres($tabInfoTraceMatiere)); 

        $statutAction = new StatutAction;
        $statutAction->user_id = Auth::user()->id;
        $statutAction->entitable_id = $devis->id;
        $statutAction->entitable_type = "Devis";
        $statutAction->statut_id = $devis->statut_id;
        $statutAction->complement = "Création devis automatique PRT par ".Auth::user()->prenom.' '.Auth::user()->nom;
        $statutAction->save();
        
        return $devis;
    }

    public function createContratPortefeuille($request, $devis, $fiche, $statut_id_contrat, $dateEnregistrement){
        $devisController = new DevisController();
        $contrat = new Contrat;
        $contrat->num_police = $request->ncontrat;
        $contrat->date_revalidation =  $dateEnregistrement ? $dateEnregistrement : '1111-11-11';
        $contrat->date_validation =  $dateEnregistrement ? $dateEnregistrement : '1111-11-11';
        $contrat->devis_id = $devis->id;
        $contrat->statut_id = $statut_id_contrat; //contrat client        
        $contrat->cad = $devis->montant;
        $contrat->chiffre_reel = $devisController->calculChiffreReel($devis);
        // $contrat->commission = number_format($request->sinistreprime_annee_1 / 12, 2); //CHECK LATER // Edit later
        $contrat->commission = 0; //CHECK LATER // Edit later
        $contrat->save();
        //Trace matiere
        $tabInfoTraceMatiere = [  
            'courtier_id'        => Auth::user()->courtier_id, 
            'fiche_id'           => $fiche->id,
            'user_id'            => Auth::user()->id,
            'userable_id'        => $fiche->userable_id,
            'userable_type'      => $fiche->userable_type,
            'entitable_id'       => $contrat->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $fiche->dispatchable_id ?? 1,  //EDIT LATER
            'dispatchable_type'  => $fiche->dispatchable_type ?? 'User',
            'statut_id'          => $contrat->statut_id,
            'slug'               => "création-contrat-prt",
            'commentaire'        => "création contrat automatique injection compagnie par ".Auth::user()->prenom.' '.Auth::user()->nom,
            'complement'         => null,
            'active'             => 1, 
        ]; 
        event(new EventTraceMatieres($tabInfoTraceMatiere));

        $statutAction = new StatutAction;
        $statutAction->user_id = Auth::user()->id;
        $statutAction->entitable_id = $contrat->id;
        $statutAction->entitable_type = "Contrat";
        $statutAction->statut_id = $contrat->statut_id;
        $statutAction->complement = "Création contrat automatique PRT par ".Auth::user()->prenom.' '.Auth::user()->nom;
        $statutAction->save();

        // Trace contrats-company
        $tabInfoTrace = [
            'slug'                 => 'insertion-contrat-prt', 
            'display_name'         => 'Insertion contrat portefeuille contrats', 
            'commentaires'         => null,
            'tracable_id'          =>  $contrat->id, 
            'tracable_type'        =>  "App\Contrat",  
            'importfile_id'        =>  $request->importFileId,  
            ];
        event(new EventTracesContratCompany($tabInfoTrace));
    }


    public function matcheGarantiePortfeuille($newRequest){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        $checkexist = GarantieLibelle::where('libelle', $newRequest->garantie)->exists();
        if(!$checkexist){
            GarantieLibelle::create([
                'libelle' => $newRequest->garantie,
                'garantie_id' => 1, //Edit later
            ]);
        }
    }


    // ======================================**************************************************=========================>
    //                                      Create Not existed contrats functions
    // ======================================**************************************************=========================>


    // =========================================    UpdateTrifDevisNoSave    =====================================================> 
    // =========================================    UpdateTrifDevisNoSave    =====================================================> 

    // ================================    Annuler Interface and functions    =======================================> 

    public function importPortefeuilleAnnuler(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];


        $importFileId = importfiles_contratcompany::max('id') + 1;
        $file = $request->file;
        $filename = $request->filename;
        $keepInitialCommission = $request->keepInitialCommission;

        $exploded = explode(",", $file);
        $explodedFilename = explode(".", $filename);

        // Type import
        if($request->typeImport == 2){
            $typeImport = 'impaye';
        }elseif($request->typeImport == 3){
            $typeImport = 'remiseVigueur';
        }else{
            $typeImport = 'annulation';
        }

        // Check if the selected branch has groupepub_injection_impaye_id in injection impayé
        $branche = Branche::find($request->branche_id);
        if ( $typeImport == 'impaye' && (!$branche || !$branche->groupepub_injection_impaye_id)) {
            return response()->json('Cette branche n\'a pas de groupe publicitaire pour les impayés configuré.', 422);
        }

        // Traitement du csv ou xls seulement
        if (str_contains($explodedFilename[1], 'csv')) {
            $ext = 'Csv';
        } else if (str_contains($explodedFilename[1], 'xslx')) {
            $ext = 'Xlsx';
        } else {
            $ext = 'Xls';
        }
        $decode = base64_decode($exploded[1]);
        $filename = str_replace(' ', '', "imported" . substr(time(), 5) . $request->filename);

        $filename_raw = explode('.', $filename);
        $original_filename_raw = explode('.', $request->filename);

        $folder = 'contrat_company';
        if($this->uploadMode == "server"){
            $pathServeur= FonctionController::saveFile($file, $filename, $folder); 
            $path = str_replace('\\', '/', storage_path("app/tmp/{$filename}"));
            file_put_contents($path, $decode);       
        }else{
          /*  $path =  str_replace('\\', '/', storage_path("upload/$folder/$filename"));
            Storage::disk('public2')->put("/$folder/$filename",$decode);   */   
            $path = str_replace('\\', '/', public_path("upload/$folder/{$filename}"));
            Storage::disk('public2')->put("/$folder/$filename", $decode);  
        }
        
        if ($ext == "csv") {
            $array = Excel::toCollection(null, $path, null, 'Csv');
        }

        
        $array = $this->processExcelFile($path);
        // Delete empty rows
        $this->deleteEmptyRows($array);

        // Prevent importation of files having > 500 contrats
        if($array->count() > $this->fileLimit){
            return response()->json('Le fichier ne doit pas contenir plus de '.$this->fileLimit.' contrats', 422);
        }

        // Get only assoc array of contrats value without heading for conversion and manipulation 
        $arrayToArray = $array->toArray();
        // annulation no need for auto convert traitmemnt (only need num contrat)
        $convertedArray =  $this->convertFileStructure($request, $arrayToArray, $filename_raw, $filename, $folder, 'sante');
        // Load contratsConverted File with proper path
        if($this->uploadMode == "server"){
            $convertedContratsFilePath = "$ftpUrl/".$convertedArray['contratConvertedFilePath'];
            $convertedContratsFile = file_get_contents($convertedContratsFilePath);
            $explode_filename_converted = explode('/', $convertedArray['contratConvertedFilePath']);
            $convertedContratsFilePath = str_replace('\\', '/', public_path("upload/tmp/".$explode_filename_converted[count($explode_filename_converted)-1]));
            file_put_contents($convertedContratsFilePath, $convertedContratsFile);
        }else{
            $convertedContratsFilePath = str_replace('\\', '/', public_path($convertedArray['contratConvertedFilePath']));
        }
        $converted_contrats = $this->processExcelFile($convertedContratsFilePath);

         // Define storage path based on upload mode (TBC)
         $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";
         if($this->uploadMode == "server"){
             $convertedContratsFilePathStored = $this->storeExcelFile(
                 new ArrayDataExport($converted_contrats),
                 "{$explode_filename_converted[count($explode_filename_converted)-1]}.xlsx",
                 $storagePath
             );
 
             $convertedContratsFilePathStored = FonctionController::saveFile($convertedContratsFilePathStored, ("{$explode_filename_converted[count($explode_filename_converted)-1]}"), $folder);
        } else {
            $explodedFolderConverted = explode(DIRECTORY_SEPARATOR, $convertedArray['contratConvertedFilePath']);
            $extractedFilename = $explodedFolderConverted[count($explodedFolderConverted)-1];
            $extractedFolder =  $explodedFolderConverted[count($explodedFolderConverted)-2];

            $convertedContratsFilePathStored = $this->storeExcelFile(
                new ArrayDataExport($converted_contrats),
                $extractedFilename,
                $storagePath,
                $extractedFolder
            );
        }

         // END TBC
        // Validate existance columns
        $validationColumns = $this->validationColumnsAnnulation($converted_contrats, $typeImport);
        if (!empty($validationColumns)) {
            return response()->json([
                'message' => 'Il y a des colonnes manquantes',
                'missColumns' => implode(" / ",$validationColumns),
            ], 422);
        }
        
        // Create deep copies of $converted_contrats
        $notFound_contrats = [];
        $exist_contrats = [];
        $incorrect_contrats = [];


        $countContratFound = 0;
        $countContratcreated = 0;
        $countContratNotFound = 0;
        $countContratExist= 0;
        $countContratAnnuler= 0;
        $countContratIncorrect = 0;

        // Work with convertedcontrat file
        foreach ($converted_contrats as $key => $value) {
           // $value_array = array_values($value->all());
            
            // Check contrat incorrect informations in importation  => place it in contrats incorrect file
            $validationErrors = $this->validationNewAnnulation($value, $typeImport);
            if(!empty($validationErrors)){
                $countContratIncorrect++;
                $incorrect_contrats[] = $value;
                continue;
            }
            //check if contrat already existe in CRM using Num police Contrat
            if(!empty($value['n0_contrat'])){
                $contrat = Contrat::where('num_police', $value['n0_contrat'])->first();
            }else{
                // unfound
                $contrat = null;
            }
            
            if ($contrat) {
                $countContratExist++;
                $exist_contrats[] = $value;
            } else {
                $countContratNotFound++;
                $notFound_contrats[] = $value;
            }
            
        }
        $storeFilesPaths = $this->storeFilesAnnulation($filename_raw, $filename, $folder, $notFound_contrats, $exist_contrats, $incorrect_contrats, $converted_contrats, $typeImport);

        // Store result in importfile_contratcompany table
        if($this->uploadMode == "server"){
            // server
            $dbImportFile = importfiles_contratcompany::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => $pathServeur,
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => null,
                'nom_fichier_created' => null,
                'nom_fichier_notfound' => $storeFilesPaths['contrat_notFound_path'],
                'nom_fichier_exist' => $storeFilesPaths['contrat_exist_path'],
                'nom_fichier_annuler' => $storeFilesPaths['annuler_contrats_path'],
                'nom_fichier_incorrect' => $storeFilesPaths['incorrect_contrats_path'],
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_contrat_created' => $countContratcreated,
                'nbr_contrat_exist' => $countContratExist,
                'nbr_contrat_annuler' => $countContratAnnuler,
                'nbr_contrat_incorrect' => $countContratIncorrect,
                'grpbranche_id' => $request->grpbranche_id,
                'branche_id' => $request->branche_id,
                'company' => $request->company,
                'type_import' => $typeImport,
                'keepCommission' => $keepInitialCommission,
                'active' => 1,
            ]);
        }else{
            // Local
            $dbImportFile = importfiles_contratcompany::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => "upload/$folder/$filename",
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => null,
                'nom_fichier_created' => null,
                'nom_fichier_notfound' => "upload/$folder/notFound$filename",
                'nom_fichier_exist' => "upload/$folder/exist$filename",
                'nom_fichier_annuler' => "upload/$folder/$typeImport$filename",     // Name based on typeImport => annulation or impaye
                'nom_fichier_incorrect' => "upload/$folder/incorrect$filename",
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_contrat_created' => $countContratcreated,
                'nbr_contrat_exist' => $countContratExist,
                'nbr_contrat_annuler' => $countContratAnnuler,
                'nbr_contrat_incorrect' => $countContratIncorrect, 
                'grpbranche_id' => $request->grpbranche_id,
                'branche_id' => $request->branche_id,
                'company' => $request->company,
                'societe_id' => $request->societe,
                'site_id' => $request->site,
                'equipe_id' => $request->equipe,
                'conseiller_id' => $request->conseiller,
                'type_import' => $typeImport,
                'keepCommission' => $keepInitialCommission,
                'active' => 1,
            ]);
        }

        // Remove local temp file to load only
        if($this->uploadMode == "server"){
            File::delete($path);
            File::delete($convertedContratsFilePath);
        }
        
        return response()->json('Les contrats importés avec succès');

    }

    public function loadContratsFromFilesAnnuler(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];
        

        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800); 

        $file = importfiles_contratcompany::find($request->fileId);
        $typeImport = $file->type_import;
        if($this->uploadMode == "server"){
            $notFoundContratsFilePath = "$ftpUrl/$file->nom_fichier_notfound";
            $existContratsFilePath = "$ftpUrl/$file->nom_fichier_exist";
            $annulerContratsFilePath = "$ftpUrl/$file->nom_fichier_annuler";
            $incorrectContratsFilePath = "$ftpUrl/$file->nom_fichier_incorrect";
           
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $existContratsFile = file_get_contents($existContratsFilePath);
            $annulerContratsFile = file_get_contents($annulerContratsFilePath);

            $explode_filename_notFound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_exist = explode('/', $file->nom_fichier_exist);
            $explode_filename_annuler = explode('/', $file->nom_fichier_annuler);

            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notFound[count($explode_filename_notFound)-1]));
            $existContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_exist[count($explode_filename_exist)-1]));
            $annulerContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_annuler[count($explode_filename_annuler)-1]));

            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($existContratsFilePath, $existContratsFile);
            file_put_contents($annulerContratsFilePath, $annulerContratsFile);
            
            // check if file contrats incorrect exist
            if($file->nom_fichier_incorrect){
                $incorrectContratsFile = file_get_contents($incorrectContratsFilePath);
                $explode_filename_incorrect = explode('/', $file->nom_fichier_incorrect);
                $incorrectContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_incorrect[count($explode_filename_incorrect)-1]));
                file_put_contents($incorrectContratsFilePath, $incorrectContratsFile);
            }

        }else{
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $existContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_exist));
            $annulerContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_annuler));
            $incorrectContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_incorrect));
        }

        $grpbrancheNom = Groupebranche::find($file->grpbranche_id)->libelle;

        $array_notFound = $this->processExcelFile($notFoundContratsFilePath);
        $array_exist = $this->processExcelFile($existContratsFilePath);
        $array_annuler = $this->processExcelFile($annulerContratsFilePath);

        // Not add any additional information to excel file
        if($file->nom_fichier_incorrect){
            // Check old contrats not have this feature
            $array_incorrect = $this->processExcelFile($incorrectContratsFilePath);
        }else{
            $array_incorrect = [];
        }


        // get all trace contrat_found with ids
        $exist_contrats_num_polices = $array_exist->pluck('n0_contrat')->all();
        $annuler_contrats_num_polices = $array_annuler->pluck('n0_contrat')->all();
        $all_contrats_polices = array_merge($exist_contrats_num_polices, $annuler_contrats_num_polices);

        $AllContartsObj = Contrat::whereIn('num_police',$all_contrats_polices)
                                // ->join('devis', 'devis.id', '=', 'contrats.devis_id')
                                ->join('devis', function($join) use($all_contrats_polices){
                                    $join->on('devis.id', '=', 'contrats.devis_id')
                                    ->whereIn('contrats.num_police',$all_contrats_polices);
                                })
                                ->join('fiches', 'fiches.num_fiche', '=', 'devis.num_fiche')
                                ->join('tiers', 'fiches.tiers_id', '=', 'tiers.id')
                                ->select('contrats.*', 'tiers.id as tier_id', 'tiers.raison_sociale', 'devis.num_devis', 'devis.formule')
                                ->get();

        
        // Get all regimes
        // $regimes = Regime::all();
        // $grpBranches = Groupebranche::all();
        
        $status = Statut::all();
        $branches = Branche::all();
        
        $motifs = Motif::get();

        $modified_array_notFound = [];
        foreach($array_notFound as $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_dannulation'] =  $value['date_dannulation'] ?? null;
            $value['date_dimpaye'] =  $value['date_dimpaye'] ?? null;
            $value['commentaire'] =  $value['commentaire'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
            // $value['regime_libelle'] =  $regimes->where('id',$value->regime)->first()['libelle'] ??  null;
            if (isset($value['motif_de_limpaye']))  {
                $value['motif_libelle'] =  $motifs->where('id',$value['motif_de_limpaye'])->first()['libelle'] ??  null;
            }
            $modified_array_notFound[] = $value;
        }
        $array_notFound = $modified_array_notFound;

        $modified_array_exist = [];
        foreach($array_exist as $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_dannulation'] =  $value['date_dannulation'] ?? null;
            $value['date_dimpaye'] =  $value['date_dimpaye'] ?? null;
            $value['commentaire'] =  $value['commentaire'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
            // $value['regime_libelle'] =  $regimes->where('id',$value->regime)->first()['libelle'] ??  null;
            if (isset($value['motif_de_limpaye'])) {
            
                $value['motif_libelle'] =  $motifs->where('id',$value['motif_de_limpaye'])->first()['libelle'] ??  null;
            }
            $conExist = $AllContartsObj->where('num_police', $value['n0_contrat'])->first();
            if($conExist){
                $value['tier_hashid'] = Hashids::encode($conExist->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conExist->id);
                $value['num_devis'] = $conExist->num_devis;
                $value['statut_id'] = $conExist->statut_id;
                $value['statut_libelle'] = $status->where('id',$conExist->statut_id)->first()['libelle'] ??  null;
                $value['formule'] =  json_decode($conExist['formule']);
                $value['branche'] = $branches->where('id',$conExist->devis->fiche->branche_id)->first() ??  null;
                // if nom not existe in portf file
                if(!isset($value['nom']) && !isset($value['prenom'])){
                    $value['nom'] = $conExist->raison_sociale;
                }
            }else{
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['statut_id'] = null;
                $value['statut_libelle'] = null;
                $value['formule'] =  null;
                $value['branche'] = null;
            }
            $modified_array_exist[] = $value;
        }
        $array_exist = $modified_array_exist;

        $modified_array_annuler = [];
        foreach($array_annuler as $value){
            $value['date_deffet'] =  $value['date_deffet'] ?? null;
            $value['date_dannulation'] =  $value['date_dannulation'] ?? null;
            $value['date_dimpaye'] =  $value['date_dimpaye'] ?? null;
            $value['commentaire'] =  $value['commentaire'] ?? null;
            $value['date_de_naissance'] =  $value['date_de_naissance'] ?? null;
            // $value['regime_libelle'] =  $regimes->where('id',$value->regime)->first()['libelle'] ??  null;
            if (isset($value['motif_de_limpaye'])) {
                $value['motif_libelle'] =  $motifs->where('id',$value['motif_de_limpaye'])->first()['libelle'] ??  null;
            }

            $conAnnuler = $AllContartsObj->where('num_police', $value['n0_contrat'])->first();
            if($conAnnuler){
                $value['tier_hashid'] = Hashids::encode($conAnnuler->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conAnnuler->id);
                $value['num_devis'] = $conAnnuler->num_devis;
                $value['statut_id'] = $conAnnuler->statut_id;
                $value['statut_libelle'] = $status->where('id',$conAnnuler->statut_id)->first()['libelle'] ??  null;
                $value['formule'] =  json_decode($conAnnuler['formule']);
                $value['branche'] = $branches->where('id',$conAnnuler->devis->fiche->branche_id)->first() ??  null;
                // if nom not existe in portf file
                if(!isset($value['nom']) && !isset($value['prenom'])){
                    $value['nom'] = $conAnnuler->raison_sociale;
                }
            }else{
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['statut_id'] = null;
                $value['statut_libelle'] = null;
                $value['formule'] =  null;
                $value['branche'] = null;
                
            }
            $modified_array_annuler[] = $value;
        }
        $array_annuler = $modified_array_annuler;

        // Add info to array incorrect if exist
        if($array_incorrect){
            $modified_array_incorrect = [];
            foreach($array_incorrect as $value){
                $validation = $this->validationNewAnnulation($value, $typeImport);
                $value['branche'] = $branches->where('id',$file->branche_id)->first() ??  null;
                $value['validationErrors'] = $validation;
                $modified_array_incorrect[] = $value;
            }
            $array_incorrect = $modified_array_incorrect;
        }

        if($this->uploadMode == "server"){
            File::delete($notFoundContratsFilePath);
            File::delete($existContratsFilePath);
            File::delete($annulerContratsFilePath);
            if($file->nom_fichier_incorrect){
                File::delete($incorrectContratsFilePath);
            }
        }

        return response()->json([
            'contratsNotFound' => $array_notFound,
            'contratsExist' => $array_exist,
            'contratsAnnuler' => $array_annuler,
            'contratsIncorrect' => $array_incorrect,
            'grpbrancheNom' => $grpbrancheNom,
        ]);
        

    }

    public function AnnulerContratMultiple(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        $folder = 'contrat_company';
        $file = $request->file;
        $file = importfiles_contratcompany::find($file['id']);
        if($this->uploadMode == "server"){
            $importContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_importe'];
            // $foundContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_found'];
            $notFoundContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_notfound'];
            $existContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_exist'];
            $annulerContratsFilePath = $ftpUrl.'/'.$file['nom_fichier_annuler'];

            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $existContratsFile = file_get_contents($existContratsFilePath);
            $annulerContratsFile = file_get_contents($annulerContratsFilePath);

            $explode_filename_notFound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_exist = explode('/', $file->nom_fichier_exist);
            $explode_filename_annuler = explode('/', $file->nom_fichier_annuler);

            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notFound[count($explode_filename_notFound)-1]));
            $existContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_exist[count($explode_filename_exist)-1]));
            $annulerContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_annuler[count($explode_filename_annuler)-1]));

            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($existContratsFilePath, $existContratsFile);
            file_put_contents($annulerContratsFilePath, $annulerContratsFile);
            
        }else{
            $importContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_importe']));
            // $foundContratsFilePath = public_path($file['nom_fichier_found']);
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_notfound']));
            $existContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_exist']));
            $annulerContratsFilePath = str_replace('\\', '/', public_path($file['nom_fichier_annuler']));
        }

        $array_exist = $this->processExcelFile($existContratsFilePath);
        $array_annuler = $this->processExcelFile($annulerContratsFilePath);

        $contratsFile = $request->contrat;
        $anunulation_prt = Motif::where('slug', 'annulation-portefeuille-compagnie')->first();
        $resiliation_prt = Motif::where('slug', 'resiliation-portefeuille-compagnie')->first();

        // Prevent annuler too much contrats
        if(count($contratsFile) > $this->fileLimit){
            return response()->json([
                'message' => 'Vous ne devez pas sélectionner plus de '.$this->fileLimit.' contrats',
                'type' => 'warning',
                'status' => 422
            ], 422);
        }
        $keepCommission = $file->keepCommission;
        $hasError = false;
        $errorResponses = [];
        
        try{
            foreach($contratsFile as $con){
                DB::connection('mysql2')->beginTransaction();
                try{
                    $contratFile = json_decode(json_encode($con));
                    $contratCrm = Contrat::where('num_police', $contratFile->n0_contrat)->first();
                    $existContrat_key = $array_exist->search(function($i) use($contratFile) {
                        return $i['n0_contrat'] == $contratFile->n0_contrat;
                    });
    
                    if(!$contratCrm){
                        throw ValidationException::withMessages([
                            'contrat' => ['Le contrat n\'existe pas dans le CRM']
                        ]);
                    }
                    
                    if($file->type_import == 'impaye'){
                        $this->impayerFunction($contratFile, $contratCrm, $file); 
                    }elseif($file->type_import == 'remiseVigueur'){
                        $this->remiseVigFunction($contratFile, $contratCrm, $file, $keepCommission); 
                    }else{
                        $this->annulerFunction($contratFile, $anunulation_prt, $resiliation_prt, $contratCrm, $file);
                    }

                    // update count annuler and count exist in importfile_contrat
                    $file->nbr_contrat_exist = $file->nbr_contrat_exist - 1;
                    $file->nbr_contrat_annuler = $file->nbr_contrat_annuler + 1;
                    $file->save();
                    
                    $contratArray = $array_exist[$existContrat_key];
                    //Remove contrat from exist array and put annuler array
                    unset($array_exist[$existContrat_key]);
                    // Remove error columns before adding to annuler array
                    $cleanedContrat = $this->cleanErrorColumns($contratArray);

                    $array_annuler->push($cleanedContrat);
                    

                    if($file->type_import == 'impaye') {
                        Log::channel('injectionContrat')->info("Le contrat {$contratFile->n0_contrat} a été passé en impayé avec succès");
                    } elseif($file->type_import == 'remiseVigueur') {
                        Log::channel('injectionContrat')->info("Le contrat {$contratFile->n0_contrat} a été remis en vigueur avec succès");
                    } else {
                        Log::channel('injectionContrat')->info("Le contrat {$contratFile->n0_contrat} a été annulé avec succès");
                    }
                    DB::connection('mysql2')->commit();
                } catch (\Throwable $e) {
                        DB::connection('mysql2')->rollback();
                        $hasError = true;

                        // Refresh the file model after rollback
                        $file->refresh();
    
                        // Add error to the corresponding array based on file type
                        $array_exist = $this->handleErrorContrat($contratFile, $array_exist, $file, $e, false);

                        Log::channel('injectionContrat')->error("Erreur {$file->type_import} contrat {$contratFile->n0_contrat}: " . $e->getMessage(), [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'line' => $e->getLine(),
                        ]);

                        $errorInfo = $this->handleException($e, $contratFile->n0_contrat);
                        $errorResponses[] = $errorInfo;
                        continue;
                        
                }
    
            }        
        }catch (\Throwable $e) {
            // 
        }finally{
            // Store final state files
            $this->storeFilesAnnulerContrats($file, $array_annuler, $array_exist, $notFoundContratsFilePath,
            $existContratsFilePath, $annulerContratsFilePath, $folder);
        }


        if(count($contratsFile) <= 1 && count($errorResponses) > 0) {
            return response()->json($errorResponses[0], $errorResponses[0]['status']);
        }else{
            // Return final aggregated result:
            if ($file->type_import == 'impaye') {
                $msg = $hasError
                    ? 'Certains contrats n\'ont pas pu être passés en impayés. Veuillez vérifier les détails.'
                    : 'Les contrats sélectionnés sont passés en impayés avec succès.';
            } elseif ($file->type_import == 'remiseVigueur') {
                $msg = $hasError
                    ? 'Certains contrats n\'ont pas pu être remis en vigueur. Veuillez vérifier les détails.'
                    : 'Les contrats ont été remis en vigueur avec succès.';
            } else {
                $msg = $hasError
                    ? 'Certains contrats n\'ont pas pu être annulés. Veuillez vérifier les détails.'
                    : 'Les contrats annulés avec succès.';
            }
            return response()->json([
                'message' => $msg,
                'type'    => ($hasError ? 'warning' : 'success'),
            ]);
        }

    }

    public function handleErrorContrat($contratFile, $array, $file, $e, $type = false)
    {   
        // Create the error info using centralized function
        $errorInfo = $this->handleException($e, $contratFile->n0_contrat);

        // Handle special cases UpdateContrat / reglements
        if ($type == 'updateContrat' || $type == 'reglements') {
            $typeImport = $type;
        } else {
            $typeImport = $file->type_import;
        }

        return $array->map(function ($item) use ($contratFile, $errorInfo, $file, $typeImport) {
            return $this->updateExcelErrorRow($item, $contratFile, $errorInfo, $typeImport);
        });
    }


    public function annulerFunction($contratFile, $anunulation_prt, $resiliation_prt, $contratCrm, $file)
    {
        // Detect annulation or résiliation operation

        // $dateEffetCarbon =  $contratFile->date_deffet ? Carbon::createFromFormat('d-m-Y', $contratFile->date_deffet) : Carbon::now();
        // $dateAnnulationCarbon = $contratFile->date_dannulation ? Carbon::createFromFormat('Y-m-d H:i:s', $contratFile->date_dannulation) : Carbon::now() ;
        $dateEffetCarbon =  !empty($contratFile->date_deffet) ? Carbon::createFromFormat('d/m/Y', $contratFile->date_deffet) : Carbon::now();
        $dateAnnulationCarbon = !empty($contratFile->date_dannulation) ? Carbon::createFromFormat('d/m/Y', $contratFile->date_dannulation) : Carbon::now();
        
        // Find motif crm from file motif_de_limpaye
        if (!$contratFile->motif_de_limpaye) {
            $grpMotifAnnId = Groupemotif::where('slug', 'annulation')->first()->id;
            $motifIdAnnAutre = Motif::where('slug', 'autre')->where('groupemotif_id', $grpMotifAnnId)->first()->id;
            $contratFile->motif_de_limpaye = $motifIdAnnAutre;
        }
        $motif_ann_libelle = Motif::find($contratFile->motif_de_limpaye)->libelle;

        // Format date annulation
        if (!empty($contratFile->date_dannulation)) {
            $dateAnnulationHist = Carbon::createFromFormat('d/m/Y', $contratFile->date_dannulation)->format('d/m/Y');
        }
        if (!empty($contratFile->motif_de_limpaye) && !empty($contratFile->date_dannulation)) {
            $commentaire_org = "$dateAnnulationHist => Motif: $motif_ann_libelle";
        } elseif (!empty($contratFile->date_dannulation)) {
            $commentaire_org = $dateAnnulationHist;
        } elseif (!empty($contratFile->motif_de_limpaye)) {
            $commentaire_org = "Motif: $motif_ann_libelle";
        } else {
            $commentaire_org = null; 
        }

        if ($dateAnnulationCarbon->lessThan($dateEffetCarbon) || $dateAnnulationCarbon->isSameDay($dateEffetCarbon)) {
            $newRequest = new \Illuminate\http\Request();
            $motifId = $contratFile->motif_de_limpaye ? $contratFile->motif_de_limpaye : $anunulation_prt->id;
            $slug =  $anunulation_prt->slug;
            $newRequest->replace([
                "action" => 1,
                "type" => 'annulation-portefeuille-compagnie',
                "dateAnnulation" => !empty($contratFile->date_dannulation) ? Carbon::createFromFormat('d/m/Y', $contratFile->date_dannulation)->format('Y-m-d') : Carbon::now(),
                "contrat_hash" => Hashids::encode($contratCrm->id),
                "motifId" => [$motifId],
                "slug" => $slug,        //annulation-portefeuille-compagnie
                // 'commentaire_org' => "annulation contrat automatique portefeuille compagnie par ".Auth::user()->prenom.' '.Auth::user()->nom,   //commentaire
                // "commentaire" => !empty($contratFile->motif_de_limpaye) ? $contratFile->motif_de_limpaye : null,   //complement
                'commentaire_org' => "Date d'annulation: $commentaire_org",   //commentaire
                "commentaire" => null,   //complement
                "maj_contrat" => true,
            ]);

            if ($contratCrm->statut_id == 23) {
                // Contrat deja annuler mark as untached
                // Update counter untouched file (not annuler)
                $file->nbr_contrat_untouched = $file->nbr_contrat_untouched + 1;
            }

            // Annuler contrat
            $contratController = new ContratController();
            $contratController->AnnulerContrat($newRequest);

            // trace annulation PRT 
            $user = Auth::user();
            $tabInfoTrace = [
                'slug'                => 'annulation-contrat-prt', 
                'display_name'          => 'Annulation contrat PRT', 
                'commentaires'         => "Contrat n_police $contratCrm->num_police a annulé par $user->nom $user->prenom ",
                'tracable_id'          =>  $contratCrm->id, 
                'tracable_type'        =>  "App\Contrat",
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
        } else {

            $newRequest = new \Illuminate\http\Request();
            $motifId = $contratFile->motif_de_limpaye ? $contratFile->motif_de_limpaye : $resiliation_prt->id;
            $slug =  $resiliation_prt->slug;
            $newRequest->replace([
                "action" => 'resiliation-portefeuille-compagnie',
                "dateResiliation" => !empty($contratFile->date_dannulation) ? Carbon::createFromFormat('d/m/Y', $contratFile->date_dannulation)->format('Y-m-d') : Carbon::now(),
                "contrat_hash" => Hashids::encode($contratCrm->id),
                "motifId" => [$motifId],
                "slug" => $slug,        //resiliation-portefeuille-compagnie
                // 'commentaire_org' => "resiliation contrat automatique portefeuille compagnie par ".Auth::user()->prenom.' '.Auth::user()->nom,  //commentaire
                // "complement" => !empty($contratFile->motif_de_limpaye) ? $contratFile->motif_de_limpaye : null,   //complement
                'commentaire_org' => "Date de résiliation: $commentaire_org",  //commentaire
                "complement" => null,   //complement
                "maj_contrat" => true,
            ]);

            if ($contratCrm->statut_id == 22) {
                // Contrat deja resilier mark as untached
                // Update counter untouched file (not resilier)
                $file->nbr_contrat_untouched = $file->nbr_contrat_untouched + 1;
            }

            // Resilier contrat
            $contratController = new ContratController();
            $contratController->ResilierContrat($newRequest);

            // trace resiliation PRT 
            $user = Auth::user();
            $tabInfoTrace = [
                'slug'                => 'resiliation-contrat-prt', 
                'display_name'          => 'Resiliation contrat PRT', 
                'commentaires'         => "Contrat n_police $contratCrm->num_police a été résilié par $user->nom $user->prenom ",
                'tracable_id'          =>  $contratCrm->id, 
                'tracable_type'        =>  "App\Contrat",
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
        }
    }

    public function impayerFunction($contratFile, $contratCrm, $file){
        $impayéStatutId = 107;
        $contratClientStatutIds = [20, 21, 75];

        // IF contrat CRM deja impayé Or not contrat client => Skip
        if ($contratCrm->statut_id != $impayéStatutId && in_array($contratCrm->statut_id, $contratClientStatutIds)) {
            $AppelStatutId = Statut::where('slug', 'appel')->first()->id;

            $user = Auth::user();
            $courtier_id = $user->courtier_id;
            $societe_id = Auth::user()->societes->first() ? Auth::user()->societes->first()->id : 1;  // Edit Later; 
            // Get branche from contrat's devis's fiche
            $branche = $contratCrm->devis->fiche->branche;

            // Stop if contrat branche has no groupepub_injection_impaye_id configured
            if (!$branche || !$branche->groupepub_injection_impaye_id) {
                throw ValidationException::withMessages([
                    'branche' => 'Le groupe publicitaire n\'est pas configuré pour la branche de ce contrat.'
                ]);
            }
            
            $groupe_id = $branche->groupepub_injection_impaye_id;

            $grp_prv_id = GroupepubProvenance::where('groupepub_id', $groupe_id)->first()->id;  //grouppub impaye-prt-compagnie
            $impaye_prt = Permission::where('name', 'maj-prt-contrat-impaye')->first();
            if (!$contratFile->motif_de_limpaye) {
                $grpMotifIdImpayé = Groupemotif::where('slug', 'impaye')->first()->id;
                $motifIdImpayeAutre = Motif::where('slug', 'autres')->where('groupemotif_id', $grpMotifIdImpayé)->first()->id;
                $contratFile->motif_de_limpaye = $motifIdImpayeAutre;
            }
            $motif_impaye_libelle = Motif::find($contratFile->motif_de_limpaye)->libelle;

            $devisContratImpaye = $contratCrm->devis;
            $ficheContratImpaye = $devisContratImpaye->fiche;

            // change statut to impayé and fill date_impaye
                $contratCrm->statut_id = $impayéStatutId;
                if (!empty($contratFile->date_dimpaye)) {
                    $dateImpCar = Carbon::createFromFormat('d/m/Y', $contratFile->date_dimpaye)->format('Y/m/d');
                    $contratCrm->date_impaye = $dateImpCar;
                }
                $contratCrm->save();

            // Duplicate fiche with new statut and groupe_pub
            // $newFicheContratImpaye = $ficheContratImpaye->replicate();
            // $newFicheContratImpaye->statut_id = $AppelStatutId;
            // $newFicheContratImpaye->groupepub_provenance_id = $grp_prv_id;
            // $newFicheContratImpaye->save();

            // Create new fiche
            $slug = uniqid();
            $newFicheContratImpaye = new Fiche();
            $newFicheContratImpaye->statut_id = $AppelStatutId;
            $newFicheContratImpaye->groupepub_provenance_id = $grp_prv_id;
            // Add contrat impayé id to new fiche appel
            $newFicheContratImpaye->contrat_impaye_id = $contratCrm->id;
            
            $newFicheContratImpaye->date_effet = $ficheContratImpaye->date_effet; 
            $newFicheContratImpaye->date_insertion = \Carbon\Carbon::now(); 
            $newFicheContratImpaye->slug = $slug;
            $newFicheContratImpaye->motif_id = 0;
            $newFicheContratImpaye->branche_id = $ficheContratImpaye->branche_id;
            $newFicheContratImpaye->tiers_id = $ficheContratImpaye->tiers_id;
            $newFicheContratImpaye->souscripteur = $ficheContratImpaye->souscripteur;
            $newFicheContratImpaye->importfile_id = 0;
            $newFicheContratImpaye->userable_id = 0;
            $newFicheContratImpaye->userable_type = '';
            $newFicheContratImpaye->dispatchable_id = 0;
            $newFicheContratImpaye->dispatchable_type = '';
            $newFicheContratImpaye->lead_id = 0;
            $newFicheContratImpaye->vehicule_id = 0;
            $newFicheContratImpaye->doublon = 0;
            $newFicheContratImpaye->origine_id = 0;
            $newFicheContratImpaye->ventecouplee = NULL;
            $newFicheContratImpaye->active = 1;
            $newFicheContratImpaye->cree_par = Auth::user()->id;
            $newFicheContratImpaye->user_id = 0;
            $newFicheContratImpaye->type_num_tel = $ficheContratImpaye->type_num_tel;
            $newFicheContratImpaye->type_email = $ficheContratImpaye->type_email;
            $newFicheContratImpaye->created_at =  \Carbon\Carbon::now();
            $newFicheContratImpaye->updated_at = \Carbon\Carbon::now();
            $newFicheContratImpaye->save();

            $newFicheContratImpaye->num_fiche = str_pad($newFicheContratImpaye->branche_id, 2, "0", STR_PAD_LEFT).str_pad($newFicheContratImpaye->id, 7, "0", STR_PAD_LEFT);
            $newFicheContratImpaye->save();

            // Format date impaye
            if (!empty($contratFile->date_dimpaye)) {
                $dateImpHist = Carbon::createFromFormat('d/m/Y', $contratFile->date_dimpaye)->format('d/m/Y');
            }
            if (!empty($contratFile->motif_de_limpaye) && !empty($contratFile->date_dimpaye)) {
                $commentaire_org = "Date d'impayé => $dateImpHist / motif => $motif_impaye_libelle";
            } elseif (!empty($contratFile->date_dimpaye)) {
                $commentaire_org = "Date d'impayé => $dateImpHist";
            } elseif (!empty($contratFile->motif_de_limpaye)) {
                $commentaire_org = "Motif => $motif_impaye_libelle";
            } else {
                $commentaire_org = null; 
            }

            // trace changement statut contrat to impaye
            $tabInfoTraceMatiere = [  
                'courtier_id'        => $courtier_id, 
                'fiche_id'           => $ficheContratImpaye->id,
                'user_id'            => $user->id,
                'userable_id'        => $ficheContratImpaye->userable_id,
                'userable_type'      => $ficheContratImpaye->userable_type,
                'entitable_id'       => $contratCrm->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $ficheContratImpaye->dispatchable_id,
                'dispatchable_type'  => $ficheContratImpaye->dispatchable_type,
                'statut_id'          => $contratCrm->statut_id,
                'slug'               => $impaye_prt->name,
                'commentaire'        => $commentaire_org,
                'complement'         => null,
                'active'             => 1, 
            ];

            event(new EventTraceMatieres($tabInfoTraceMatiere));

            // Add motif in statutAction table
            $statutAction = new Statutaction();
            $statutAction->entitable_id     = $contratCrm->id;
            $statutAction->entitable_type   = 'Contrat';
            $statutAction->user_id          = $user->id;
            $statutAction->statut_id        = $contratCrm->statut_id;
            $statutAction->motif_id         = json_encode([$contratFile->motif_de_limpaye]); // keep for backward compatibility
            $statutAction->complement       = "Date d'impayé => $dateImpHist";
            $statutAction->save();

            $statutAction->motifs()->syncWithoutDetaching([$contratFile->motif_de_limpaye]); // save motif in pivot table as well


            // trace impayé PRT 
            $user = Auth::user();
            $tabInfoTrace = [
                'slug'                => 'impaye-contrat-prt', 
                'display_name'          => 'Impayé contrat PRT', 
                'commentaires'         => "Contrat n_police $contratCrm->num_police a passé en impayé par $user->nom $user->prenom ",
                'tracable_id'          =>  $contratCrm->id, 
                'tracable_type'        =>  "App\Contrat",
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
            
            // Dispatch fiche
            $jobDispatchData = [

                'fiche' => $newFicheContratImpaye,
                'societe_id' => null,
                'branche_id' => $newFicheContratImpaye->branche_id,
                'courtier_id' => $courtier_id,
                'code_postal' => null,
                'groupe_id' => $groupe_id,
                'action' => 'generation-fiche-contrat-impaye-prt',
                'commentaire' => "Generation fiche à base de contrat impayé N° police $contratCrm->num_police"
            ];

            dispatch_sync(new DispatchFiche($jobDispatchData));

            // If diff <= 6 || date d'effet bigger then date d'impayé mois send email and notif
            $dateEffetCarbon =  Carbon::parse($devisContratImpaye->date_effet);
            $dateImpayeCarbon = !empty($contratFile->date_dimpaye) ? Carbon::createFromFormat('d/m/Y', $contratFile->date_dimpaye) : null;
            
            if ($dateEffetCarbon && $dateImpayeCarbon) {
                $diffDays = $dateEffetCarbon->diffInDays($dateImpayeCarbon);
                if (
                    ($dateEffetCarbon->greaterThan($dateImpayeCarbon) || $diffDays <= 180) 
                    && $branche->slug != 'automobile'
                ) {                    
                    // Notif and email
                    //Notification
                    $tiers = Tiers::find($devisContratImpaye->tiers_id);
                    $tiers = new TiersResource($tiers); 
                    $link = '/tiers/' . collect($tiers)['hash_id'] . '/contrat/' . collect(new ContratResource($contratCrm))['hash_id'];
                    $user_id = Devis::find($contratCrm->devis_id)->user_id;
                    $titre = 'Impayé du contrat';  

                    event(new EventNotificationUsers(
                        $user_id,
                        $contratCrm->id,
                        Auth::user()->courtier_id,
                        'CONTRATIMPAYE',
                        'Contrat',
                        'Impayé du contrat',
                        'Changement statut contrat de client '.$tiers['raison_sociale'].' de la fiche : '. $devisContratImpaye->num_fiche.' à '. $contratCrm->statut->libelle,
                        $titre . ' : ' . $devisContratImpaye->num_fiche,
                        $link
                    ));

                    // send mail
                    $requestMail = new \Illuminate\http\Request();
                    $requestMail['maj_contrat'] = true;
                    $modelMail = ModelMail::where('nom', 'Contrat impaye')->first();
                    $emails = [];
                    if ($modelMail) {
                        $requestMail['id'] = $modelMail->id;
                        $requestMail['modelName'] = 'Contrat impaye';
                        $requestMail['contrat_hash'] = Hashids::encode($contratCrm->id);
                        $emails = $modelMail->getModelProfilsEmails($devisContratImpaye);
                    }
                    
                    $sendemail = new ConfigMailingController();
                    foreach ($emails as $email) {
                        $requestMail['tier'] = $email;
                        if ($contratFile->motif_de_limpaye) {
                            $requestMail['motifId'] = [$contratFile->motif_de_limpaye];
                            $requestMail['motif'] = $motif_impaye_libelle;
                        };
                        $sendemail->envoi($requestMail);
                    }
                }
            }
        } else {
            // Update counter untouched file (not impayé)
            $file->nbr_contrat_untouched = $file->nbr_contrat_untouched + 1;
        }
    }

    public function remiseVigFunction($contratFile, $contratCrm, $file, $keepCommission = false)
    {
            $allowedRemiseStatutIds = [34, 23, 57, 58, 59, 40, 22, 107];
            // IF contrat CRM deja impayé Or not contrat client => Skip
        if (in_array($contratCrm->statut_id, $allowedRemiseStatutIds)) {
                
                $contratController = new ContratController();
                $contratCrm->hash_id = Hashids::encode($contratCrm->id);
                $remise_prt = Motif::where('slug', 'remise-vigueur-portefeuille-compagnie')->first();
    
                /*** Skip deposer en remise vig ****/
                /*
                // Action 5 must be first - Dépot remise en vigeur
                $request = new Request();
                $request->merge([
                    'action' => 5,
                    "type" => 'remise-vigueur-contrat-prt',
                    'contrat_hash' => $contratCrm->hash_id,
                ]);
                $result = $contratController->changerStatutContrat($request);
    
                // Add delay to respect history order per time
                sleep(1);
                */
    
                // Action 3 - Validation remise en vigeur
                $request = new Request();
                $request->merge([
                    'action' => 3,
                    "type" => 'remise-vigueur-contrat-prt',
                    "maj_contrat" => true,
                    'contrat_hash' => $contratCrm->hash_id,
                    'commentaire' => $contratFile->commentaire,
                    // 'contratEndDate' =>  $contratFile->date_fin_contrat ? Carbon::createFromFormat('d/m/Y', $contratFile->date_fin_contrat)->toDateString() : null,      // contratEndDate optionnal for commision recalcul
                    'keepCommission' => $keepCommission,
                    'motifId' => $remise_prt ? $remise_prt->id : null,
                ]);
                $result = $contratController->changerStatutContrat($request);
    
                // trace annulation PRT 
                $user = Auth::user();
                $tabInfoTrace = [
                    'slug'                => 'remise-vigueur-contrat-prt', 
                    'display_name'          => 'Remise en vigueur contrat PRT', 
                    'commentaires'         => "Contrat n_police $contratCrm->num_police remis en vigueur par $user->nom $user->prenom ",
                    'tracable_id'          =>  $contratCrm->id, 
                    'tracable_type'        =>  "App\Contrat",
                    'importfile_id'        =>  $file->id,  
                    ];
                event(new EventTracesContratCompany($tabInfoTrace));
                
                return $result;
        } else {
                // Update counter untouched file (not Remise vigueur)
                $file->nbr_contrat_untouched = $file->nbr_contrat_untouched + 1;
        }
    }

    // ================================    Annuler Interface and functions    =======================================> 
    
    // ================================    Réglements functions    =======================================> 

    public function autoReg(){
        return view('portefeuilles.reglement');
    }

    public function createRequestReg($value, $importFileId){

        $RegRequest = new \Illuminate\http\Request();
        $RegRequest->replace([
            "importPortefeuille" => true,
            "importFileId" => $importFileId,
        ]);  

        // Regelement
        !empty($value->n0_contrat) ? $RegRequest->merge(["n0_contrat" => $value->n0_contrat]) : null;
        !empty($value->numero_reg) ? $RegRequest->merge(["numero_reg" => $value->numero_reg]) : null;
        !empty($value->montant_reg) ? $RegRequest->merge(["montant_reg" => $value->montant_reg]) : null;
        !empty($value->date_f) ? $RegRequest->merge(["date_f" => Carbon::createFromFormat('d/m/Y', $value->date_f)->format('Y-m-d')]) : null;
        !empty($value->date_reg) ? $RegRequest->merge(["date_reg" => Carbon::createFromFormat('d/m/Y', $value->date_reg)->format('Y-m-d')]) : null;
        !empty($value->num_gdf) ? $RegRequest->merge(["num_gdf" => $value->num_gdf]) : null;
        !empty($value->ordre) ? $RegRequest->merge(["ordre" => $value->ordre]) : null;
        !empty($value->mode_paie_reg) ? $RegRequest->merge(["mode_paie_reg" => $value->mode_paie_reg]) : null;
        !empty($value->statut_id) ? $RegRequest->merge(["statut_id" => $value->statut_id]) : null;

        return $RegRequest;
    }

    // TBC
    public function importReglement(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        // FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        $importFileId = importfiles_reglement::max('id') + 1;
        $file = $request->file;
        $exploded = explode(",", $file);

        // Traitement du csv ou xls seulement
        if (str_contains($exploded[0], 'csv')) {
            $ext = 'csv';
        } else if (str_contains($exploded[0], 'xslx')) {
            $ext = 'xlsx';
        } else {
            $ext = 'xls';
        }
        $decode = base64_decode($exploded[1]);
        $filename = str_replace(' ', '', "imported" . substr(time(), 5) . $request->filename);

        $filename_raw = explode('.', $filename);
        $original_filename_raw = explode('.', $request->filename);

        $folder = 'contrat_company/reglements';
        if($this->uploadMode == "server"){
            // Upload original file to server
            $pathServeur = FonctionController::saveFile($file, $filename, $folder);
            $path = "upload/tmp/{$folder}/{$filename}";
            //file_put_contents($path, $decode);
            Storage::put($path, $decode);
        }else{
            // Upload original file to local
           /* $path =  public_path("upload/$folder/$filename");
            Storage::disk('public2')->put("/$folder/$filename",$decode); */
            $path = str_replace('\\', '/', public_path("upload/$folder/{$filename}"));
            Storage::disk('public2')->put("/$folder/$filename", $decode);
        }

        $array = $this->processExcelFile($path);
        
        if ($ext == "csv") {
            $array = Excel::toCollection(null, $path, null, 'Csv', [
                'formatDates' => true,
                'sheet_index' => 0,
            ]);            
        }
        
        // Delete empty rows
        $this->deleteEmptyRows($array);
        
        // Prevent importation of files having > 500 contrats
        if($array->count() > $this->fileLimit){
            return response()->json('Le fichier ne doit pas contenir plus de '.$this->fileLimit.' Reglement', 422);
        }
        
        // Get only assoc array of Reglements value without heading 
        $arrayToArray = $array->toArray();
        $convertedArray =  $this->convertFileStructure($request, $arrayToArray, $filename_raw, $filename, $folder, 'automobile');
        
        // Load contratsConverted File with proper path
        if($this->uploadMode == "server"){
            $convertedContratsFilePath = "$ftpUrl/".$convertedArray['contratConvertedFilePath'];
            $convertedContratsFile = file_get_contents($convertedContratsFilePath);
            $explode_filename_converted = explode('/', $convertedArray['contratConvertedFilePath']);
            $convertedContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_converted[count($explode_filename_converted)-1]));
            // file_put_contents($convertedContratsFilePath, $convertedContratsFile);
            //  Storage::put("upload/tmp/", $convertedContratsFile);
            Storage::put("upload/tmp/".$explode_filename_converted[count($explode_filename_converted)-1], $convertedContratsFile);
        }else{
            $convertedContratsFilePath = str_replace('\\', '/', public_path($convertedArray['contratConvertedFilePath']));
        }
        /*
        $converted_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) { $reader->formatDates(true, 'd/m/Y'); }, null, true)->get(); */
        
        $converted_contrats = $this->processExcelFile($convertedContratsFilePath);
        // Define storage path based on upload mode (TBC)
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";
        if($this->uploadMode == "server"){
            $convertedContratsFilePathStored = $this->storeExcelFile(
                new ArrayDataExport($converted_contrats),
                "{$explode_filename_converted[count($explode_filename_converted)-1]}.xlsx",
                $storagePath
            );

            $convertedContratsFilePathStored = FonctionController::saveFile($convertedContratsFilePathStored, ("{$explode_filename_converted[count($explode_filename_converted)-1]}"), $folder);
        // dd($convertedContratsFilePathStored);
        } else {
            $explodedFolderConverted = explode(DIRECTORY_SEPARATOR, $convertedArray['contratConvertedFilePath']);
            $extractedFilename = $explodedFolderConverted[count($explodedFolderConverted)-1];
            $extractedFolder =  $explodedFolderConverted[count($explodedFolderConverted)-2];

            $convertedContratsFilePathStored = $this->storeExcelFile(
                new ArrayDataExport($converted_contrats),
                $extractedFilename,
                $storagePath,
                $extractedFolder
            );
        }

        // END TBC
        // Validation
        $validationColumns = $this->validationColumnsReg($converted_contrats);
        
        if (!empty($validationColumns)) {
            return response()->json([
                'message' => 'Il y a des colonnes manquantes',
                'missColumns' => implode(" / ",$validationColumns),
            ], 422);
        }
        
        // point All reglements file to original file
        // $notFound_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        // $found_contrats = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        // $incorrect_reglements = Excel::selectSheetsByIndex(0)->load($convertedContratsFilePath, function ($reader) { $reader->formatDates(true, 'd/m/Y'); }, null, true)->get();
        $notFound_contrats = unserialize(serialize($converted_contrats));
        $found_contrats = unserialize(serialize($converted_contrats));
        $incorrect_reglements = unserialize(serialize($converted_contrats));
        

        $countContratFound = 0;
        $countContratNotFound = 0;
        $countReglementscreated = 0;
        $countReglementsIncorrect = 0;

        // Work with convertedcontrat file
        foreach ($converted_contrats as $key => $value) {
            $value_array = is_array($value) ? array_values($value) : array_values($value->all());
            // Check reglements incorrect informations in importation  => place it in Reglements incorrect file
            $validationErrors = $this->validationNewReg($value);
            // if($validationErrors){
            //     dd($validationErrors);
            // }
            if(!empty($validationErrors)){
                $countReglementsIncorrect++;
                unset($notFound_contrats[$key]);
                unset($found_contrats[$key]);
                continue;
            }
            
            //check if Reg contrat already existe in CRM using Num police Contrat
            if(!empty($value['n0_contrat'])){
                $contrat = Contrat::where('num_police', $value['n0_contrat'])->first();
            }else{
                // not found
                $contrat = null;
            }
            if ($contrat) {
                $countContratFound++;
                //remove from notFound
                unset($notFound_contrats[$key]);
                unset($incorrect_reglements[$key]);
            } else {
                // remove contrat from found
                $countContratNotFound++;
                unset($found_contrats[$key]);
                unset($incorrect_reglements[$key]);
            }
            
        }
        
        $storeFilesPaths = $this->storeReglementFiles($filename_raw, $filename, $folder, $notFound_contrats, $found_contrats, $incorrect_reglements, $array);

        // Store result in importfile_contratcompany table
        if($this->uploadMode == "server"){
            // server
            $dbImportFile = importfiles_reglement::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => $pathServeur,
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => $storeFilesPaths['contrat_found_path'],
                'nom_fichier_notfound' => $storeFilesPaths['contrat_notFound_path'],
                'nom_fichier_created' => $storeFilesPaths['reglement_newcreated_path'],
                'nom_fichier_incorrect' => $storeFilesPaths['incorrect_reglements_path'],
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_reglement_created' => $countReglementscreated,
                'nbr_reglement_incorrect' => $countReglementsIncorrect,
                'company' => $request->company,
                'active' => 1,
            ]);
        }else{
            // local
            $dbImportFile = importfiles_reglement::create([
                'nom_original' => $original_filename_raw[0],
                'nom_fichier_importe' => "upload/$folder/$filename",
                'nom_fichier_convert' => $convertedArray['contratConvertedFilePath'],
                'nom_fichier_found' => "upload/$folder/found$filename",
                'nom_fichier_notfound' => "upload/$folder/notFound$filename",
                'nom_fichier_created' => "upload/$folder/created$filename",
                'nom_fichier_incorrect' => "upload/$folder/incorrect$filename",
    
                'nbr_contrat_found' => $countContratFound,
                'nbr_contrat_notfound' => $countContratNotFound,
                'nbr_reglement_created' => $countReglementscreated,
                'nbr_reglement_incorrect' => $countReglementsIncorrect, 
                'company' => $request->company,
                'active' => 1,
            ]);

        }
        // Remove local temp file to load only
        if($this->uploadMode == "server"){
            File::delete($path);
            File::delete($convertedContratsFilePath);
        }
        
        return response()->json('Les Reglements importés avec succès');

    }

    public function loadReglementDetaille(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        //FonctionController::setConfigMySqlTarificateur(); // database hosts array is empty error

        $dateDebut = $request->dateDebut? Carbon::createFromFormat('Y-m-d', $request->dateDebut)->format('Y-m-d 00:00:00') : null;
        $dateFin = $request->dateFin? Carbon::createFromFormat('Y-m-d', $request->dateFin)->format('Y-m-d 23:59:59') : null;
        
        $files =  importfiles_reglement::latest()
        ->where(function($query) use($request, $dateDebut, $dateFin){
            if($dateDebut) $query->where('created_at', '>=', $dateDebut);
            if($dateFin) $query->where('created_at', '<=', $dateFin);
            if($request->fileName) $query->where('nom_original', 'like', '%'.$request->fileName.'%');
            if($request->company) $query->where('company', $request->company);
        })
        ->paginate(10);

        $companies = Compagnie::get();
        foreach($files as $file){
            $file['company'] = $companies->where('id',$file->company)->first()['libelle'] ??  null;
        }
        return response()->json([
            'files' => $files,
            'app_url' => env('APP_URL'),
            'uploadMode' => $this->uploadMode,
        ]);
    }


    public function loadReglementFromFiles(Request $request){
        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];

        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800); 

        $file = importfiles_reglement::find($request->fileId);
        if($this->uploadMode == "server"){
            $foundContratsFilePath = "$ftpUrl/$file->nom_fichier_found";
            $notFoundContratsFilePath = "$ftpUrl/$file->nom_fichier_notfound";
            $createdReglementsFilePath = "$ftpUrl/$file->nom_fichier_created";
            
            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $createdReglementsFile = file_get_contents($createdReglementsFilePath);
            

            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_notfound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_created = explode('/', $file->nom_fichier_created);
            

            $foundContratsFilePath = str_replace('\\', '/' , storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $notFoundContratsFilePath =  str_replace('\\', '/' ,storage_path("app/upload/tmp/".$explode_filename_notfound[count($explode_filename_notfound)-1]));
            $createdReglementsFilePath =  str_replace('\\', '/' ,storage_path("app/upload/tmp/".$explode_filename_created[count($explode_filename_created)-1]));            
            

            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($createdReglementsFilePath, $createdReglementsFile);

            // check if file contrats incorrect exist
            if($file->nom_fichier_incorrect){
                $incorrectReglementsFilePath = "$ftpUrl/$file->nom_fichier_incorrect";
                $incorrectReglemntsFile = file_get_contents($incorrectReglementsFilePath);
                $explode_filename_incorrect = explode('/', $file->nom_fichier_incorrect);
                $incorrectReglementsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_incorrect[count($explode_filename_incorrect)-1]));
                file_put_contents($incorrectReglementsFilePath, $incorrectReglemntsFile);
            }
    
        }else{
            $foundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_found));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $createdReglementsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_created));
            $incorrectReglementsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_incorrect));
        }

        $status = Statut::get();
        $modePaieAll = Modepaiement::get();

        $array_found =  $this->processExcelFile($foundContratsFilePath);
        $array_notFound =  $this->processExcelFile($notFoundContratsFilePath);
        $array_created =  $this->processExcelFile($createdReglementsFilePath);

        // Not add any additional information to excel file
        if($file->nom_fichier_incorrect){
            // Check old contrats not have this feature
            $array_incorrect = $this->processExcelFile($incorrectReglementsFilePath);
        }else{
            $array_incorrect = [];
        }


        // get all trace contrat_found with ids
        $found_contrats_num_polices = $array_found->pluck('n0_contrat')->all();
        $created_reglements_num_polices = $array_created->pluck('n0_contrat')->all();
        $all_contrats_polices = array_merge($found_contrats_num_polices, $created_reglements_num_polices);

        // // => All contrats 4 files
        $AllContartsObj = Contrat::whereIn('num_police',$all_contrats_polices)
                                ->join('devis', function($join) use($all_contrats_polices){
                                    $join->on('devis.id', '=', 'contrats.devis_id')
                                    ->whereIn('contrats.num_police',$all_contrats_polices);
                                })
                                ->join('fiches', 'fiches.num_fiche', '=', 'devis.num_fiche')
                                ->join('tiers', 'fiches.tiers_id', '=', 'tiers.id')
                                ->select('contrats.*', 'tiers.id as tier_id', 'devis.num_devis', 'devis.formule')
                                ->get();
        
        // Add devis_id to reglement contrat found array
        $reglementsFoundValues = $array_found->toArray();
        $reglementsFoundCollect = collect($reglementsFoundValues);
        $reglementsFoundNumContrats = $reglementsFoundCollect->pluck('n0_contrat');
        $reglementsFoundContratsData = Contrat::whereIn('num_police', $reglementsFoundNumContrats)->get();

        // START ARRAY FOUND
        $modified_array_found = [];
        
        foreach($array_found as $regV){
            $regV['devis_id'] = $reglementsFoundContratsData->where('num_police', $regV['n0_contrat'])->first() ? $reglementsFoundContratsData->where('num_police', $regV['n0_contrat'])->first()->devis_id : null;
            $modified_array_found[] = $regV;
        }
        $array_found = $modified_array_found;

        
        // clear modified array_found to prevent duplication
        $modified_array_found = [];

        // Change date format to fr
        foreach($array_found as $value){
            $value['date_f'] =  $value['date_f'] ?? null;
            $value['date_reg'] =  $value['date_reg'] ?? null;
            $value['mode_libelle'] = $modePaieAll->where('id',$value['mode_paie_reg'])->first()['libelle'] ??  null;
            $value['statut_reg_libelle'] = $status->where('id',$value['statut_id'])->first()['libelle'] ??  null;

            // Check this reglement already existe
            $reglementsExists = $this->checkReglementExist($value);
            $value['reg_exist'] = $reglementsExists ? true : false;
            if($value['reg_exist']){
                $value['reg_crm_data'] = $reglementsExists;
                $value['reg_crm_mode_libelle'] = $modePaieAll->where('id', $reglementsExists->mode_id)->first()['libelle'] ??  null;
                $value['reg_crm_statut_reg_libelle'] = $status->where('id', $reglementsExists->statut_id)->first()['libelle'] ??  null;
            }else{
                $value['reg_crm_data'] = null;
            }

            $conFound = $AllContartsObj->where('num_police', $value['n0_contrat'])->first();
            if($conFound){
                $value['statut_libelle'] = $status->where('id',$conFound->statut_id)->first()['libelle'] ??  null;
                $value['tier_hashid'] = Hashids::encode($conFound->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conFound->id);
                $value['num_devis'] = $conFound->num_devis;
                $value['formule'] =  json_decode($conFound['formule']);
            }else{
                $value['statut_libelle'] = null;
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['formule'] = null;
            }
            $modified_array_found[] = $value;
        }
        $array_found = $modified_array_found;

        // == END FOUND

        // == NOT FOUND
        $modified_array_notFound = [];
        foreach($array_notFound as $value){
            $value['date_f'] =  $value['date_f'] ?? null;
            $value['date_reg'] =  $value['date_reg'] ?? null;
            $value['mode_libelle'] = $modePaieAll->where('id',$value['mode_paie_reg'])->first()['libelle'] ??  null;
            $value['statut_reg_libelle'] = $status->where('id',$value['statut_id'])->first()['libelle'] ??  null;
            $modified_array_notFound[] = $value;
        }
        $array_notFound = $modified_array_notFound;
        // END NOT FOUND

        
        // START ARRAY CREATED
        $modified_array_created = [];
        foreach($array_created as $value){
            $value['date_f'] =  $value['date_f'] ?? null;
            $value['date_reg'] =  $value['date_reg'] ?? null;
            $value['mode_libelle'] = $modePaieAll->where('id',$value['mode_paie_reg'])->first()['libelle'] ??  null;
            $value['statut_reg_libelle'] = $status->where('id',$value['statut_id'])->first()['libelle'] ??  null;

            $conCreated = $AllContartsObj->where('num_police', $value['n0_contrat'])->first();
            if($conCreated){
                $value['tier_hashid'] = Hashids::encode($conCreated->tier_id);
                $value['contrat_hashid'] = Hashids::encode($conCreated->id);
                $value['num_devis'] = $conCreated->num_devis;
                $value['statut_libelle'] = $status->where('id',$conCreated->statut_id)->first()['libelle'] ??  null;
                $value['formule'] =  json_decode($conCreated['formule']);
            }else{
                $value['tier_hashid'] = null;
                $value['contrat_hashid'] = null;
                $value['num_devis'] = null;
                $value['statut_libelle'] = null; 
                $value['formule'] = null;
            }
            $modified_array_created[] = $value;
        }
        $array_created = $modified_array_created;
        // == END ARRAY CREATED
        
        // Add info to array incorrect if exist
        if($array_incorrect){
            $modified_array_incorrect = [];
            foreach($array_incorrect as $value){
                $validation = $this->validationNewReg($value);
                $value['validationErrors'] = $validation;
                $modified_array_incorrect[] = $value;
            }
            $array_incorrect = $modified_array_incorrect;
        }

        // delete temp local files
        if($this->uploadMode == "server"){
            File::delete($foundContratsFilePath);
            File::delete($notFoundContratsFilePath);
            File::delete($createdReglementsFilePath);
            if($file->nom_fichier_incorrect){
                File::delete($incorrectReglementsFilePath);
            }
        }

        return response()->json([
            'contratsFound' => $array_found,
            'contratsNotFound' => $array_notFound,
            'contratsCreated' => $array_created,
            'contratsIncorrect' => $array_incorrect,
        ]);
    }

    public function createReglementsMultiple(Request $request){

        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800); 

        $courtier_id = Auth::user()->courtier_id;
        FonctionController::setConfigMySql2($courtier_id);
        FonctionController::setConfigMySqlTarificateur();
        $fctController = new FonctionController();
        $ftpUrl = $fctController->getUrls($courtier_id)['ftpUrl'];


        $folder = 'contrat_company/reglements';
        $file = $request->file;
        $file = importfiles_reglement::find($file['id']);

        // Status fiche devi contrat
        
        if($this->uploadMode == "server"){
            $foundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_found;
            $notFoundContratsFilePath = $ftpUrl.'/'.$file->nom_fichier_notfound;
            $createdReglementsFilePath = $ftpUrl.'/'.$file->nom_fichier_created;
            $importReglementsFilePath = $ftpUrl.'/'.$file->nom_fichier_importe;

            $foundContratsFile = file_get_contents($foundContratsFilePath);
            $notFoundContratsFile = file_get_contents($notFoundContratsFilePath);
            $createdReglementsFile = file_get_contents($createdReglementsFilePath);
            $importReglementsFile = file_get_contents($importReglementsFilePath);

            $explode_filename_found = explode('/', $file->nom_fichier_found);
            $explode_filename_notFound = explode('/', $file->nom_fichier_notfound);
            $explode_filename_created = explode('/', $file->nom_fichier_created);
            $explode_filename_import = explode('/', $file->nom_fichier_importe);

            $foundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_found[count($explode_filename_found)-1]));
            $notFoundContratsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_notFound[count($explode_filename_notFound)-1]));
            $createdReglementsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_created[count($explode_filename_created)-1]));
            $importReglementsFilePath = str_replace('\\', '/', storage_path("app/upload/tmp/".$explode_filename_import[count($explode_filename_import)-1]));

            file_put_contents($foundContratsFilePath, $foundContratsFile);
            file_put_contents($notFoundContratsFilePath, $notFoundContratsFile);
            file_put_contents($createdReglementsFilePath, $createdReglementsFile);
            file_put_contents($importReglementsFilePath, $importReglementsFile);
            
        }else{
            $foundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_found));
            $notFoundContratsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_notfound));
            $createdReglementsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_created));
            $importReglementsFilePath = str_replace('\\', '/', public_path($file->nom_fichier_importe));
        }
        
        // $array_notFound = Excel::selectSheetsByIndex(0)->load($notFoundContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        /*$array_found = Excel::selectSheetsByIndex(0)->load($foundContratsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get();
        $array_created = Excel::selectSheetsByIndex(0)->load($createdReglementsFilePath, function ($reader) {$reader->formatDates(true, 'd/m/Y');}, null, true)->get(); */
        

        $array_found = $this->processExcelFile($foundContratsFilePath);
        $array_created = $this->processExcelFile($createdReglementsFilePath);

        $reglementsNotExist = $request->contrat;
        // Prevent creation too much contrats
        if(count($reglementsNotExist) > $this->fileLimit){
            return response()->json([
                'message' => 'Vous ne devez pas sélectionner plus de '.$this->fileLimit.' contrats',
                'type' => 'warning',
                'status' => 422
            ], 422);
        }

        $hasError = false;
        $errorResponses = [];

        try{
            foreach($reglementsNotExist as $reg){
                try{
                    DB::connection('mysql2')->beginTransaction();
                    $reglementNotExist = json_decode(json_encode($reg));
                    // Create Reglements
                    $RegRequest = $this->createRequestReg($reglementNotExist, $file->id, 'createReg', 'autoReg');

                    $regCreated = $this->autoCreateReglement($request, $RegRequest, $file);

                    // Remove from contrats not found and put it in newcreated contrats
                    $reglementNotExist_key = $array_found->search(function($i) use($reglementNotExist) {
                        return ($i['n0_contrat'] == $reglementNotExist->n0_contrat && $i['ordre'] == $reglementNotExist->ordre);
                    });
                    $reglementArray = $array_found[$reglementNotExist_key];
                    //Remove contrat from notfound array and put it in created array
                    unset($array_found[$reglementNotExist_key]);
                    // Remove error columns before adding to created array
                    $cleanedReglement = $this->cleanErrorColumns($reglementArray);
                    $array_created->push($cleanedReglement);
                

                    // update count found and count created in importfile_contrat
                    $file->nbr_contrat_found = $file->nbr_contrat_found - 1;
                    $file->nbr_reglement_created = $file->nbr_reglement_created + 1;
                    $file->save();
                    

                    Log::channel('injectionContrat')->info("successfully created reglement for contrat {$reglementNotExist->n0_contrat}: ");
                    DB::connection('mysql2')->commit();

                } catch (\Throwable $e) {
                    DB::connection('mysql2')->rollback();
                    $hasError = true;

                    // Refresh the file model after rollback
                    $file->refresh();
                    // Add error to the corresponding array
                    $array_found = $this->handleErrorContrat($reglementNotExist, $array_found, $file, $e, 'reglements');

                    Log::channel('injectionContrat')->error("Erreur CreateReglementsMultiple contrat {$reglementNotExist->n0_contrat}: " . $e->getMessage(), [
                        'error' => $e->getMessage(),
                        'line' => $e->getLine(),
                    ]);

                    $errorInfo = $this->handleException($e, $reglementNotExist->n0_contrat);
                    $errorResponses[] = $errorInfo;

                    continue;
                }

            }
        } catch (\Throwable $e) {
            // Catch any unexpected errors outside the main loop
        } finally {
            // Store final state files
            $this->storeFilesCreatedReglements($file, $array_found, $array_created, $foundContratsFilePath, 
                $notFoundContratsFilePath, $createdReglementsFilePath, $importReglementsFilePath, $folder);
        }

        // Return appropriate response based on single/multiple creations
        if(count($reglementsNotExist) <= 1 && count($errorResponses) > 0) {
            return response()->json($errorResponses[0], $errorResponses[0]['status']);
        } else {
            $msg = $hasError 
                ? 'Certains règlements n\'ont pas pu être créés. Veuillez vérifier les détails.'
                : 'Les règlements ont été créés avec succès';
                
            return response()->json([
                'message' => $msg,
                'type' => ($hasError ? 'warning' : 'success')
            ]);
        }

    }


    // ================================    Réglements functions    =======================================> 
    
    
    // ================================    Services    =======================================> 

    public function deleteEmptyRows($rows){
        foreach ($rows as $key => $value) {
            //$value_array = array_values($value->all());
            $value_array = is_array($value) ? array_values($value) : array_values($value->all());
            if ( (!$value_array) || ($this->containsOnlyNull($value_array)) ) {
                unset($rows[$key]);
            }
        }
    }
    
    public function createRequest($value, $importFileId, $type = 'updateContrat', $branche = 'sante'){

        /* 
            Not need validation anymore in create and update function => incorrect contrats traited first in the import
            $validator = $this->validation($value, $type);
        */

        $PFC = new PubliqueFicheController();
        $tiersController = new TiersController();
        if($type == 'updateContrat'){
            $adr = [
                'adresse' => !empty($value->adresse) ? $value->adresse : null,
                'adresse1' => !empty($value->adresse_2) ? $value->adresse_2 : null,
                'adresse2' => !empty ($value->adresse_3) ? $value->adresse_3 : null,
            ];

            try {
                $dateNs = !empty($value->date_de_naissance) ? Carbon::createFromFormat('d/m/Y', $value->date_de_naissance)->format('Y-m-d') : null;
            } catch (\Exception $e) {
                $dateNs = !empty($value->date_de_naissance) ? Carbon::parse($value->date_de_naissance)->format('Y-m-d') : null;
                // Log::channel('injectionContrat')->info("error carbon parse:: " . $e->getMessage());
            }


            try {
                $dateEff = !empty($value->date_deffet) ? Carbon::createFromFormat('d/m/Y', $value->date_deffet)->format('Y-m-d') : null;
            } catch (\Exception $e) {
                $dateEff = !empty($value->date_deffet) ? Carbon::parse($value->date_deffet)->format('Y-m-d') : null;
            }


            try {
                $dateEnrg =  !empty($value->date_denregistrement) ?Carbon::createFromFormat('d/m/Y', $value->date_denregistrement)->format('Y-m-d') : null;
            } catch (\Exception $e) {
                $dateEnrg =  !empty($value->date_denregistrement) ?Carbon::parse($value->date_denregistrement)->format('Y-m-d') : null;
            }


        }elseif($type == 'creationContrat'){
            $adr = [
                'adresse' => !empty($value->adresse) ? $value->adresse : null,
                'adresse1' => !empty($value->adresse_2) ? $value->adresse_2 : null,
                'adresse2' => !empty ($value->adresse_3) ? $value->adresse_3 : null,
                'adresse3' => null
            ];
            $dateNs = !empty($value->date_de_naissance) ? Carbon::createFromFormat('d/m/Y', $value->date_de_naissance)->format('Y-m-d') : '1111-11-11';
            $dateEff = !empty($value->date_deffet) ? Carbon::createFromFormat('d/m/Y', $value->date_deffet)->format('Y-m-d') : '1111-11-11';
            $dateEnrg =  !empty($value->date_denregistrement) ? Carbon::createFromFormat('d/m/Y', $value->date_denregistrement)->format('Y-m-d') : '1111-11-11';
        }

        $newRequest = new \Illuminate\http\Request();
        $newRequest->replace([
            "importPortefeuille" => true,
            "importFileId" => $importFileId,
            "nom" => !empty($value->nom) ? $value->nom : null,
            "prenom" => !empty($value->prenom) ? $value->prenom : null,
            "email" => !empty($value->email) ? $value->email : null,
            "typetierId" => 1,
            "adresse_insee" => [
                "code_postal" => !empty($value->code_postal) ? $value->code_postal : null,
                "ville" => !empty($value->ville) ? $value->ville : null,
                "pays" => null,
                "region" => null,
                "code_pays" => null
            ],
            // This special for tier-dpps-fam creation nor for all 3 new request places
            "adresse" => $adr,
            "date_naissance" => $dateNs,
            "date_effet" => $dateEff,
            "date_denregistrement" => $dateEnrg,
            "boite_postale" => null,
            "sexe" => (!empty ($value->civilite) && (strtolower($value->civilite) == "MME" || strtolower($value->civilite) == "MLLE")) ? "Femme" : "Homme",
            "civilite" => !empty ($value->civilite) ? strtolower($value->civilite) : null,
            "num_tel" =>  !empty($value->telephone) ?  $PFC->numeroTel($value->telephone) : null,
            "portable" => !empty($value->portable) ? $PFC->numeroTel($value->portable) : null,
            "regime" => !empty($value->regime) ? $value->regime : null,  
            "ncontrat" => !empty($value->n0_contrat) ? $value->n0_contrat : null,
            "cotisation_mensuelle_ttc" => !empty($value->cotisation_mensuelle_ttc) ? $value->cotisation_mensuelle_ttc : null,
            "garantie" => !empty($value->garantie) ? $value->garantie : null,
            "situation_familiale" => !empty($value->situation_de_famille) ? $value->situation_de_famille : null,
            "TTCValidationError" => !empty($value->TTCValidationError) ? $value->TTCValidationError : null,
            "NumPoliceValidationError" => !empty($value->NumPoliceValidationError) ? $value->NumPoliceValidationError : null,

        ]);  

        if($branche == 'automobile'){
            !empty($value->num_permis) ? $newRequest->merge(["num_permis" => $value->num_permis]) : $newRequest->merge(["num_permis" => null]);
            !empty($value->lieu_permis) ? $newRequest->merge(["lieu_permis" => $value->lieu_permis]) : $newRequest->merge(["lieu_permis" => null]);
            !empty($value->date_permis) ? $newRequest->merge(["date_permis" => Carbon::createFromFormat('d/m/Y', $value->date_permis)->format('Y-m-d')]) : $newRequest->merge(["date_permis" => null]);
            !empty($value->type_permis) ? $newRequest->merge(["type_permis" => $value->type_permis]) : $newRequest->merge(["type_permis" => null]);
            !empty($value->type_paiement) ? $newRequest->merge(["type_paiement" => $value->type_paiement]) : $newRequest->merge(["type_paiement" => null]);
            !empty($value->mode_paiement) ? $newRequest->merge(["mode_paiement" => $value->mode_paiement]) : $newRequest->merge(["mode_paiement" => null]);
            // !empty($value->dossier_complet) ? $newRequest->merge(["dossier_complet" => $value->dossier_complet]) : null;
            
            !empty($value->frais_dossier) ? $newRequest->merge(["frais_dossier" => $value->frais_dossier]) : $newRequest->merge(["frais_dossier" => null]);
            !empty($value->reste_a_payer) ? $newRequest->merge(["reste_a_payer" => $value->reste_a_payer]) : $newRequest->merge(["reste_a_payer" => null]);
            !empty($value->prorata) ? $newRequest->merge(["prorata" => $value->prorata]) : $newRequest->merge(["prorata" => null]);
            !empty($value->model) ? $newRequest->merge(["model" => $value->model]) : $newRequest->merge(["model" => null]);
            // !empty($value->immatriculation) ? $newRequest->merge(["immatriculation" => $value->immatriculation]) : $newRequest->merge(["immatriculation" => 'MatriculeVide'. substr(time(), 5)]);
            !empty($value->immatriculation) ? $newRequest->merge(["immatriculation" => $value->immatriculation]) : $newRequest->merge(["immatriculation" => null]);
            !empty($value->code_sra) ? $newRequest->merge(["code_sra" => $value->code_sra]) : $newRequest->merge(["code_sra" => null]);
            
        }
        return $newRequest;
    }

    public function calculeChangesTypeDB($contrat){
        if(!empty($contrat)){
            $allKeys = $this->getKeysMultidimensional($contrat);
            foreach($allKeys as $key=>$value) {
                if($value === 'old_Formule') {
                    return 'red';
                }
            }
            foreach($allKeys as $key=>$value) {
                if($value === 'updatedWithoutFormule') {
                    return 'blue';
                }
            }
            foreach($allKeys as $key=>$value) {
                if($value === 'old_Tarif total') {
                    return 'orange';
                }
            }
        }
        
        return 'green';
    }

    public function getKeysMultidimensional(array $array) 
    {
        $keys = array();
        foreach($array as $key => $value)
        {
            $keys[] = $key;
            if( is_array($value) ) { 
                $keys = array_merge($keys, $this->getKeysMultidimensional($value));
            }
        }

        return $keys;

    }

    public function calculeChangesType($contrat, $newRequest){
        $devi_formule = json_decode($contrat->devis->formule, true);
        // check Formule
        if($devi_formule['id'] != $newRequest->garantie){
            return 'red';
        }
        // Check Cotisation
        elseif($devi_formule['tarifTotal'] != $newRequest->cotisation_mensuelle_ttc){
            return 'orange';
        }
        else{
            return 'green';
        }
    }

    public function storeFiles($filename_raw, $filename, $folder, $notFound_contrats, $exist_contrats, $incorrect_contrats, $converted_contrats) {
        // Get the heading from converted contrat file
        $heading = $converted_contrats->isNotEmpty() ? array_keys($converted_contrats->first()) : null;
        
        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";
        

        // Store files 
        $filePaths = [
            'contrat_notFound_path' => $this->storeExcelFile(new ArrayDataExport($notFound_contrats), "notFound{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'contrat_found_path' => $this->storeExcelFile(new ArrayDataExport([$heading]), "found{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'contrat_exist_path' => $this->storeExcelFile(new ArrayDataExport($exist_contrats), "exist{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'contrat_newcreated_path' => $this->storeExcelFile(new ArrayDataExport([$heading]), "created{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'incorrect_contrats_path' => $this->storeExcelFile(new ArrayDataExport($incorrect_contrats), "incorrect{$filename_raw[0]}.xlsx", $storagePath, $folder)
        ];
        // If upload mode is server, store files on server
        if($this->uploadMode == "server") {
            foreach ($filePaths as $key => $path) {
                $filePaths[$key] = FonctionController::saveFile($path, str_replace(['_path', '_contrats', 'contrat_'], '', $key) . $filename, $folder);
            }
        }
    
        // Empty tmp folder if exists
        $tmpFolderPath = 'upload/tmp/' . $folder;
        if (Storage::exists($tmpFolderPath)) {
            Storage::deleteDirectory($tmpFolderPath);
        }
        return $filePaths;
    }
    

    public function storeFilesCreatedContrats($file, $array_notFound, $array_created, $foundContratsFilePath, 
    $notFoundContratsFilePath, $createdContratsFilePath, $importContratsFilePath, $folder){
        // Ecrase exiting file with new data
        $explode_filename_notfound = explode('/', $file->nom_fichier_notfound);
        $raw_filename_notfound = explode('.', $explode_filename_notfound[count($explode_filename_notfound)-1]);
        $raw_filename_notfound = $raw_filename_notfound[0];
        $explode_filename_created = explode('/', $file->nom_fichier_created);
        $raw_filename_created = explode('.', $explode_filename_created[count($explode_filename_created)-1]);
        $raw_filename_created = $raw_filename_created[0];

        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        // add error columns keys
        $array_notFoundData = $this->addErrorColumns($array_notFound);

        // dd($array_notFoundData);

        $contrat_notFound_path = $this->storeExcelFile(
            new ArrayDataExport($array_notFoundData),
            "{$raw_filename_notfound}.xlsx",
            $storagePath,
            $folder
        );
        $contrat_created_path = $this->storeExcelFile(
            new ArrayDataExport($array_created),
            "{$raw_filename_created}.xlsx",
            $storagePath,
            $folder
        );

        if($this->uploadMode == "server"){
            // Server
            $contrat_notFound_path = FonctionController::saveFile($contrat_notFound_path, ($explode_filename_notfound[count($explode_filename_notfound)-1]), $folder);
            $contrat_created_path = FonctionController::saveFile($contrat_created_path, ($explode_filename_created[count($explode_filename_created)-1]), $folder);

            // Delete local temp files server
            File::delete($foundContratsFilePath);
            File::delete($notFoundContratsFilePath);
            File::delete($createdContratsFilePath);
            File::delete($importContratsFilePath);
            
        }
    }


    public function storeFilesUpdatedContrats($file, $array_found, $array_exist, $importContratsFilePath,
    $foundContratsFilePath, $existContratsFilePath, $notFoundContratsFilePath, $folder){
        // Ecrase existing file with new data
        $explode_filename_found = explode('/', $file->nom_fichier_found);
        $raw_filename_found = explode('.', $explode_filename_found[count($explode_filename_found)-1]);
        $raw_filename_found = $raw_filename_found[0];
        $explode_filename_exist = explode('/', $file->nom_fichier_exist);
        $raw_filename_exist = explode('.', $explode_filename_exist[count($explode_filename_exist)-1]);
        $raw_filename_exist = $raw_filename_exist[0];


        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        $contrat_found_path = $this->storeExcelFile(
            new ArrayDataExport($array_found), 
            "{$raw_filename_found}.xlsx", 
            $storagePath,
            $folder
        );

        // add error columns keys
        $array_existData = $this->addErrorColumns($array_exist);

        $contrat_exist_path = $this->storeExcelFile(
            new ArrayDataExport($array_existData), 
            "{$raw_filename_exist}.xlsx", 
            $storagePath,
            $folder
        );

        if($this->uploadMode == "server"){
            $contrat_found_path = FonctionController::saveFile($contrat_found_path, ($explode_filename_found[count($explode_filename_found)-1]), $folder);
            $exist_contrats_path = FonctionController::saveFile($contrat_exist_path, ($explode_filename_exist[count($explode_filename_exist)-1]), $folder);

            // delete temp local files
            File::delete($importContratsFilePath);
            File::delete($foundContratsFilePath);
            File::delete($existContratsFilePath);
            File::delete($notFoundContratsFilePath);
        } 

        return [
            'contrat_found_path' => $contrat_found_path,
            'contrat_exist_path' => $contrat_exist_path
        ];
    }

    public function storeFilesAnnulation($filename_raw, $filename, $folder, $notFound_contrats, $exist_contrats, $incorrect_contrats, $converted_contrats, $typeImport){
        // Store found, modify, notfound, create contrats Exel files
        // Get the heading from converted contrat file
        $heading = $converted_contrats->isNotEmpty() ? array_keys($converted_contrats->first()) : null;

        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        // Generate the Excel files and store the paths separately
        $contratNotFoundPath = $this->storeExcelFile(new ArrayDataExport($notFound_contrats), "notFound{$filename_raw[0]}.xlsx", $storagePath, $folder);
        $contratExistPath = $this->storeExcelFile(new ArrayDataExport($exist_contrats), "exist{$filename_raw[0]}.xlsx", $storagePath, $folder);
        $incorrectContratsPath = $this->storeExcelFile(new ArrayDataExport($incorrect_contrats), "incorrect{$filename_raw[0]}.xlsx", $storagePath, $folder);
        $annulerContratsPath = $this->storeExcelFile(new ArrayDataExport([$heading]), "$typeImport{$filename_raw[0]}.xlsx", $storagePath, $folder);

        

        $filePaths = [
            'contrat_notFound_path' => $contratNotFoundPath,
            'contrat_exist_path' => $contratExistPath,
            'incorrect_contrats_path' => $incorrectContratsPath,
            'annuler_contrats_path' => $annulerContratsPath
        ]; 
        // Store exist, annuler, notfound, contrats Exel files
        if($this->uploadMode == "server"){
            // Server
            foreach ($filePaths as $key => $path) {
                $filePaths[$key] = FonctionController::saveFile($path, str_replace(['_path', '_contrats', 'contrat_'], '', $key) . $filename, $folder);
            } 

        }
        // Empty tmp folder if exists
        $tmpFolderPath = 'upload/tmp/' . $folder;
        if (Storage::exists($tmpFolderPath)) {
            Storage::deleteDirectory($tmpFolderPath);
        }
        // Return Files pathes in the server / local not needed
        return $filePaths;
    }

    public function storeFilesAnnulerContrats($file, $array_annuler, $array_exist, $notFoundContratsFilePath,
    $existContratsFilePath, $annulerContratsFilePath, $folder){
        // Ecrase annuler and exist files with new data
        $explode_filename_annuler = explode('/', $file->nom_fichier_annuler);
        $raw_filename_annuler = explode('.', $explode_filename_annuler[count($explode_filename_annuler)-1]);
        $raw_filename_annuler = $raw_filename_annuler[0];
        $explode_filename_exist = explode('/', $file->nom_fichier_exist);
        $raw_filename_exist = explode('.', $explode_filename_exist[count($explode_filename_exist)-1]);
        $raw_filename_exist = $raw_filename_exist[0];

        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        $contrat_annuler_path = $this->storeExcelFile(
            new ArrayDataExport($array_annuler),
            "{$raw_filename_annuler}.xlsx",
            $storagePath,
            $folder
        );

        // add error columns keys
        $array_existData = $this->addErrorColumns($array_exist);

        $contrat_exist_path = $this->storeExcelFile(
            new ArrayDataExport($array_existData),
            "{$raw_filename_exist}.xlsx",
            $storagePath,
            $folder
        );

        if($this->uploadMode == "server"){
            // Server
            $contrat_annuler_path = FonctionController::saveFile($contrat_annuler_path, ($explode_filename_annuler[count($explode_filename_annuler)-1]), $folder);
            

            $contrat_exist_path = FonctionController::saveFile($contrat_exist_path, ($explode_filename_exist[count($explode_filename_exist)-1]), $folder);

            // Delete local temp files
            File::delete($notFoundContratsFilePath);
            File::delete($existContratsFilePath);
            File::delete($annulerContratsFilePath);
        }
                
    }

    public function validationNew($value){
        // Convert to array for validation
        // $arrayValue = json_decode(json_encode($value), true);
        $arrayValue = is_array($value) ? $value : $value->toArray();
        $rulesNom = [                                                                              
            'nom' => 'required|string',
        ];
        $validatorNom = Validator($arrayValue, $rulesNom);

        $rulesPrenom = [                                                                              
            'prenom' => 'required|string',
        ];
        $validatorPrenom = Validator($arrayValue, $rulesPrenom);

        $rulesSF = [                                                                              
            'situation_de_famille' => 'nullable|integer|max:6|min:1',
        ];
        $validatorSF = Validator($arrayValue, $rulesSF);

        $rulesRegime = [                                                                              
            'regime' => 'nullable|integer|max:9|min:1',
        ];
        $validatorRegime = Validator($arrayValue, $rulesRegime);

        $rulesDateEnrg = [                                                                              
            'date_denregistrement' => 'nullable|date_format:d/m/Y',
            // 'date_denregistrement' => ['nullable', new DateValidCC('date_denregistrement')],
        ];
        $validatorDateEnrg = Validator($arrayValue, $rulesDateEnrg);

        $rulesDateEff = [                                                                              
            'date_deffet' => 'nullable|date_format:d/m/Y',
            // 'date_deffet' => ['nullable', new DateValidCC('date_deffet')],
        ];
        $validatorDateEff = Validator($arrayValue, $rulesDateEff);

        $rulesDateNais = [                                                                              
            'date_de_naissance' => 'nullable|date_format:d/m/Y',
            // 'date_de_naissance' => ['nullable', new DateValidCC('date_de_naissance')],
        ];
        $validatorDateNais = Validator($arrayValue, $rulesDateNais);

        $rulesGar = [                                                                              
            'garantie' => 'required|integer',
        ];
        $validatorGar = Validator($arrayValue, $rulesGar);

        $rulesTTC = [                                                                              
            'cotisation_mensuelle_ttc' => 'required|numeric',
        ];
        $validatorTTC = Validator($arrayValue, $rulesTTC);

        $rulesNumPolice = [                                                                              
            'n0_contrat' => 'required',
        ];
        $validatorNumPolice = Validator($arrayValue, $rulesNumPolice);


        // flag validation errors on the original request data used in (update and creation) import
        $validationError = [];
        if ($validatorNom->fails()) {
            $validationError['nom'] = true;
        }
        if ($validatorPrenom->fails()) {
            $validationError['prenom'] = true;
        }
        if ($validatorSF->fails()) {
            $validationError['stf'] = true;
        }
        if ($validatorRegime->fails()) {
            $validationError['regime'] = true;
        }
        if ($validatorDateEnrg->fails()) {
            $validationError['date_denregistrement'] = true;
        }
        if ($validatorDateEff->fails()) {
            $validationError['date_deffet'] = true;
        }
        if ($validatorDateNais->fails()) {
            $validationError['date_de_naissance'] = true;
        }
        if ($validatorGar->fails()) {
            $validationError['garantie'] = true;
        }
        if ($validatorTTC->fails()) {
            $validationError['ttc'] = true;
        }
        if ($validatorNumPolice->fails()) {
            $validationError['NumPolice'] = true;
        }
        return $validationError;

    }

    
    // Annulation + Impaye + RemiseVigueur
    public function validationNewAnnulation($value, $typeImport) {
        // Convert to array for validation
        $arrayValue = is_array($value) ? $value : $value->toArray();
        $validationError = [];

        /****Common validation rules****/
        $rules = [
            'n0_contrat' => 'required'
        ];

        // Add type-specific validation rules
        switch($typeImport) {
            case 'impaye':
                $rules += [
                    'date_dimpaye' => 'required|date_format:d/m/Y',
                    'date_deffet' => 'required|date_format:d/m/Y',
                    'motif_de_limpaye' => 'nullable|integer'
                ];
                break;

            case 'remiseVigueur':
                $rules += [
                    // 'date_fin_contrat' => 'required|date_format:d/m/Y',
                    'commentaire' => 'nullable|string'
                ];
                break;

            default: // For annulation
                $rules += [
                    'date_dannulation' => 'required|date_format:d/m/Y',
                    'date_deffet' => 'required|date_format:d/m/Y',
                    'motif_de_limpaye' => 'nullable|integer'
                ];
                break;
        }

        // Validate all rules at once
        $validator = Validator($arrayValue, $rules);

        // Map validation failures to error flags
        $errorMapping = [
            'n0_contrat' => 'NumPolice',
            'date_dimpaye' => 'DateImp',
            'date_dannulation' => 'DateAnn',
            'date_deffet' => 'DateEff',
            'motif_de_limpaye' => 'MotifImp',
            'date_fin_contrat' => 'DateFinCon',
            'commentaire' => 'Commentaire'
        ];

        foreach ($rules as $field => $rule) {
            if ($validator->errors()->has($field)) {
                $validationError[$errorMapping[$field]] = true;
            }
        }

        return $validationError;
    }

    public function validationColumns($array){ 
        // Columns 
        // $heading = $array->getHeading();
       // $heading = $array->isNotEmpty() ? $array->first()->values()->toArray() : [];  
        $heading = !empty($array) ? array_keys($array[0]) : [];

        $requiredColumnsSlug = ColumnsMaj::where('required', 1)
                                        ->where('type', 'sante')
                                        ->pluck('libelle')
                                        ->transform(function($cl){
                                            return $cl =$this->slugify($cl);
                                        })->toArray();

        // Slugify $heading so we can insure same format when comparing: 
        $headingSlug = collect($heading)->map(function($label) {
            return $this->slugify($label);
        })->toArray(); 
        
        $missColumns = array_diff($requiredColumnsSlug, $headingSlug);
        return $missColumns;
    }
    // Annulation + Impaye
    public function validationColumnsAnnulation($array, $typeImport){ 
        // Columns 
        //$heading = $array->getHeading();
        $heading = !empty($array) ? array_keys($array[0]) : [];

        // Slugify $heading so we can insure same format when comparing: 
        $headingSlug = collect($heading)->map(function($label) {
            return $this->slugify($label);
        })->toArray(); 

        // Hard coded
        if($typeImport == 'impaye'){
            $requiredColumnsSlug = ColumnsMaj::where('required', 1)
                                        ->where('type', 'imp')
                                        ->pluck('libelle')
                                        ->transform(function($cl){
                                            return $cl =$this->slugify($cl);
                                        })->toArray();
        }elseif($typeImport == 'remiseVigueur'){
            $requiredColumnsSlug = ColumnsMaj::where('required', 1)
                                        ->where('type', 'remiseVigueur')
                                        ->pluck('libelle')
                                        ->transform(function($cl){
                                            return $cl =$this->slugify($cl);
                                        })->toArray();
        }else{
            // Default annulation
            $requiredColumnsSlug = ColumnsMaj::where('required', 1)
                                        ->where('type', 'annulation')
                                        ->pluck('libelle')
                                        ->transform(function($cl){
                                            return $cl =$this->slugify($cl);
                                        })->toArray();
        }
        $missColumns = array_diff($requiredColumnsSlug, $headingSlug);
        return $missColumns;
    }

    

    function containsOnlyNull($array){
        return empty(array_filter($array, function ($a) { return $a !== null;}));
    }

    public function slugify($value){
        return Str::slug($value, '_', 'fr');
    }


    function recursive_change_key($arr, $set) {
        if (is_array($arr) && is_array($set) && !empty($arr) && !empty($set)) {
    		$newArr = array();
    		foreach ($arr as $k => $v) {
    		    $key = array_key_exists($this->slugify($k), $set) ? $set[$this->slugify($k)] : $k;
    		    $newArr[$key] = is_array($v) ? $this->recursive_change_key($v, $set) : $v;
    		}
    		return $newArr;
    	}
    	return $arr;    
    }

    function recursive_change_value($arr, $set, $type) {
        if (is_array($arr) && is_array($set) && !empty($arr) && !empty($set)) {
    		$newArr = array();
    		foreach ($arr as $k => $v) {
                if(is_array($v)){
                    $newArr[$k] = $this->recursive_change_value($v, $set, $type);
                }else{
                    // update only column $type
                    if($k == $type){
                        $value = array_key_exists($this->slugify($v), $set) ? $set[$this->slugify($v)] : $v;
                        $newArr[$k] = $value;
                    }else{
                        $newArr[$k] = $v;
                    }
                }
    		}
    		return $newArr;
    	}
    	return $arr;    
    }


    public function storeConvertedFile($filename_raw, $filename, $folder, $convertedArray){
        // Return the path of converted excel contrats file to loaded again and validated
        if($this->uploadMode == "server"){
           /* $contrat_converted =  Excel::create("converted".$filename_raw[0], function($excel) use($convertedArray) {
                $excel->sheet('Sheet 1', function($sheet) use($convertedArray) {
                    $sheet->fromArray($convertedArray);
                });
            })->store('xlsx', false,true); */

            //création de l'export
            Excel::store(new ArrayDataExport(new Fluent($convertedArray)), "tmp/converted{$filename_raw[0]}.xlsx", "public2"); 

            // obtention du chemin de l'export
            $contrat_converted = public_path("upload".DIRECTORY_SEPARATOR."tmp".DIRECTORY_SEPARATOR."converted{$filename_raw[0]}.xlsx");

           // $contrat_converted_path = FonctionController::saveFile($contrat_converted['full'], ("converted".$filename), $folder);
            $contrat_converted_path = FonctionController::saveFile($contrat_converted, ("converted".$filename), $folder);
        }else{
            // Local
            /*$contrat_converted =  Excel::create("converted".$filename_raw[0], function($excel) use($convertedArray) {
                $excel->sheet('Sheet 1', function($sheet) use($convertedArray) {
                    $sheet->fromArray($convertedArray);
                });
            })->store('xlsx', public_path("upload/$folder"),true);

            $contrat_converted_path =  "upload/$folder/converted$filename" ; */
            Excel::store(new ArrayDataExport(new Fluent($convertedArray)), "{$folder}/converted{$filename}", 'public2'); 
           // $contrat_converted_path =  storage_path("upload".DIRECTORY_SEPARATOR.$folder.DIRECTORY_SEPARATOR."converted$filename") ;
            $contrat_converted_path =  "upload".DIRECTORY_SEPARATOR.$folder.DIRECTORY_SEPARATOR."converted$filename";
        }
        return $contrat_converted_path;
    }

        
    // =============    Auto + REG    =================> 

    public function autoCreateContrat($dpp,$tiers, $fiche, $devis, $newRequest, $tarif, $branche){
        // If branche auto add additional field tier-dpp 
        // skip permis creation if no date permis et type permis
        if($newRequest->date_permis && $newRequest->type_permis){
            // Update fiels permi in dpps
            $this->autoDpps($dpp, $newRequest);
            // Check existe
            $permisDpp = $this->autoCheckPermiDpp($dpp, $newRequest);
            if(!$permisDpp){
                // create Permis DPP line
                $permisDpp = $this->autoCreatePermiDpp($dpp, $newRequest);
            }
    
            $fiche->type_permis_id = $newRequest->type_permis;
        }
        
        $devis->typepaiements_id = $newRequest->type_paiement;
        $devis->modepaiements_id = $newRequest->mode_paiement;

        $devis->prorata = $newRequest->prorata;
        $devis->frais_dossier = $newRequest->frais_dossier;
        $devis->reste_a_payer = $newRequest->reste_a_payer;
        // $devis->dossier_complet = $newRequest->dossier_complet;
        $devis->dossier_complet = null;

        // Skip véhicule creation if no matricule
        if($newRequest->immatriculation){
            // check vehicule
            $vehicule = $this->autoCheckVehicule($tiers, $newRequest, $branche);
            if(!$vehicule){
                $vehicule = $this->autoCreateVehicule($fiche, $devis, $tiers, $newRequest, $branche);
            }
            // Update (fiche - devis) vehicule
            $fiche->vehicule_id = $vehicule->id;
            $devis->vehicule_id = $vehicule->id;
            $fiche->save();
            $devis->save();
        }

    }

    public function autoDpps($dpp, $newRequest){
        $dpp->num_permis = $newRequest->num_permis;
        $dpp->lieu_permis = $newRequest->lieu_permis;
        $dpp->date_permis = $newRequest->date_permis;
        $dpp->type_permis = $newRequest->type_permis;
        $dpp->save();
        
    }

    public function autoCheckPermiDpp($dpp, $newRequest){
        $permisDpp = TypePermisDpp::where('dpps_id', $dpp->id)
                            ->where('type_id', $newRequest->type_permis)
                            ->where('active', 1)
                            ->first();
        return $permisDpp;
    }

    public function autoCreatePermiDpp($dpp, $newRequest){
        $permisDpp = TypePermisDpp::create([
            'dpps_id' => $dpp->id,
            'date' => $newRequest->date_permis,
            'type_id' => $newRequest->type_permis,
            'active' => 1,
        ]);

        return $permisDpp;
    }

    public function autoCheckVehicule($tiers, $newRequest, $branche){
        $vehicule = Vehicule::where('tiers_id', $tiers->id)
                            ->where('immatriculation', $newRequest->immatriculation)
                            ->where('active', 1)
                            ->first();
        return $vehicule;
    }

    public function autoCreateVehicule($fiche, $devis, $tiers, $newRequest, $branche){
        // Create vehicule
        $vehicule = Vehicule::create([
            'tiers_id' => $tiers->id,
            'branche_id' => $branche->id,
            'model' => $newRequest->model,
            'apple_comm' => $newRequest->model,
            'immatriculation' => $newRequest->immatriculation,
            'code_sra' => $newRequest->code_sra,
            'active' => 1,
        ]);
        return $vehicule;
    }

    public function autoCreateReglement($request, $RegRequest, $file){

        $ncontrat = $RegRequest->n0_contrat;
        $contrat = Contrat::where('num_police', $ncontrat)->first();
        $devis = Devis::find($contrat->devis_id);
        $user = Auth::user();

        // check reglement existe 
        $RegRequest->merge(["devis_id" => $contrat->devis_id]);
        $regExiste = $this->checkReglementExist($RegRequest);
        // Update existed reglement
        if($regExiste){
            $regUpdated = $regExiste;
            
            Reglement::where("devis_id", $devis->id)
                ->where('ordre', $RegRequest->ordre)
                ->where('active', 1)
                ->update([
                    'devis_id' => $devis->id,
                    'numero' => $RegRequest->numero_reg,
                    'montant' => $RegRequest->montant_reg,
                    'date' => Carbon::parse($RegRequest->date_f)->toDateString(),
                    'num_gdf' => $RegRequest->num_gdf,
                    'ordre' => $RegRequest->ordre,
                    'date_regle' => Carbon::parse($RegRequest->date_reg)->toDateString(),
                    'mode_id' => $RegRequest->mode_paie_reg,
                    'statut_id' => $RegRequest->statut_id,
                    'updated_at' => Carbon::now(),
                ]);

            // Update reglemnt 
            $tabInfoTrace = [
                'slug'                => 'update-reglement-prt', 
                'display_name'          => 'Mettre à jour règlement PRT', 
                'commentaires'         => "Reglement n0_contrat $RegRequest->n0_contrat a été mis à jour par $user->nom $user->prenom ",
                'tracable_id'          =>  $regUpdated->id, 
                'tracable_type'        =>  "App\Reglement",
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
            return $regUpdated;
        }else{
    
            $regCreated = Reglement::create([
                'devis_id' => $devis->id,
                'numero' => $RegRequest->numero_reg,
                'montant' => $RegRequest->montant_reg,
                'date' => Carbon::parse($RegRequest->date_f)->toDateString(),
                'num_gdf' => $RegRequest->num_gdf,
                'ordre' => $RegRequest->ordre,
                'date_regle' => Carbon::parse($RegRequest->date_reg)->toDateString(),
                'mode_id' => $RegRequest->mode_paie_reg,
                'statut_id' => $RegRequest->statut_id,
            ]);

            // Add trace reglemnt created in trace Contratcompany
            $tabInfoTrace = [
                'slug'                => 'creer-reglement-prt', 
                'display_name'          => 'Création réglement PRT', 
                'commentaires'         => "Reglement n0_contrat $RegRequest->n0_contrat a été créé par $user->nom $user->prenom ",
                'tracable_id'          =>  $regCreated->id, 
                'tracable_type'        =>  "App\Reglement",
                'importfile_id'        =>  $file->id,  
                ];
            event(new EventTracesContratCompany($tabInfoTrace));
            return $regCreated;
        }


    }

    public function checkReglementExist($regValue){
        $regExists = Reglement::where('devis_id', $regValue['devis_id'])
                            ->where('ordre', $regValue['ordre'])
                            ->where('active', 1)
                            ->first();
        return $regExists;

    }

    public function storeReglementFiles($filename_raw, $filename, $folder, $notFound_contrats, $found_contrats, $incorrect_reglements, $array){
        // Store found, modify, notfound, create contrats Excel files
        

        $storagePath = $this->uploadMode == "server" ?  "upload/tmp/{$folder}" : "{$folder}";

        $heading = !empty($array) ? array_keys($array[0]) : [];

        $filePaths = [
            'contrat_notFound_path' => $this->storeExcelFile(new ArrayDataExport($notFound_contrats), "notFound{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'contrat_found_path' => $this->storeExcelFile(new ArrayDataExport($found_contrats), "found{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'reglement_newcreated_path' => $this->storeExcelFile(new ArrayDataExport([$heading]), "created{$filename_raw[0]}.xlsx", $storagePath, $folder),
            'incorrect_reglements_path' => $this->storeExcelFile(new ArrayDataExport($incorrect_reglements), "incorrect{$filename_raw[0]}.xlsx", $storagePath, $folder)
        ];

        if ( $this->uploadMode == "server") {

            /*$contrat_notFound_path = FonctionController::saveFile($contrat_notFound_path, ("notFound".$filename), $folder);
            $contrat_found_path = FonctionController::saveFile($contrat_found_path, ("found".$filename), $folder);
            $reglement_newcreated_path = FonctionController::saveFile($newcreated_reglements_path, ("created".$filename), $folder);
            $incorrect_reglements_path = FonctionController::saveFile($incorrect_reglements_path, ("incorrect".$filename), $folder); */
            
            // Store Each file in the server (str_replace() is for filename formatting)
            foreach ($filePaths as $key => $path) {
                $filePaths[$key] = FonctionController::saveFile($path, str_replace(['_path', 'reglement_', 'contrat_'], '', $key) . $filename, $folder);
            }
        }

        // Empty tmp folder after import if exists

        $tmpFolderPath = 'upload/tmp/' . $folder;
        Storage::exists($tmpFolderPath) ? Storage::deleteDirectory($tmpFolderPath) : null;

        return $filePaths;
    }
    

    public function storeFilesCreatedReglements($file, $array_found, $array_created, $foundContratsFilePath, 
    $notFoundContratsFilePath, $createdReglementsFilePath, $importReglementsFilePath, $folder){
        // Ecrase exiting file with new data
        $explode_filename_found = explode('/', $file->nom_fichier_found);
        $raw_filename_found = explode('.', $explode_filename_found[count($explode_filename_found)-1]);
        $raw_filename_found = $raw_filename_found[0];
        $explode_filename_created = explode('/', $file->nom_fichier_created);
        $raw_filename_created = explode('.', $explode_filename_created[count($explode_filename_created)-1]);
        $raw_filename_created = $raw_filename_created[0];

        // Define storage path based on upload mode
        $storagePath = $this->uploadMode == "server" ? "upload/tmp/{$folder}" : "{$folder}";

        $reglement_found_path = $this->storeExcelFile(
            new ArrayDataExport($array_found), 
            "{$raw_filename_found}.xlsx", 
            $storagePath,
            $folder
        );
        $reglement_created_path = $this->storeExcelFile(
            new ArrayDataExport($array_created),
            "{$raw_filename_created}.xlsx", 
            $storagePath,
            $folder
        );

        if($this->uploadMode == "server"){
            // Server            
            $reglement_found_path = FonctionController::saveFile($reglement_found_path, ($explode_filename_found[count($explode_filename_found)-1]), $folder);        
            $reglement_created_path = FonctionController::saveFile($reglement_created_path, ($explode_filename_created[count($explode_filename_created)-1]), $folder);

            // Delete local temp files server
            File::delete($foundContratsFilePath);
            File::delete($notFoundContratsFilePath);
            File::delete($createdReglementsFilePath);
            File::delete($importReglementsFilePath);   
        }

        return [
            'reglement_found_path' => $reglement_found_path,
            'reglement_created_path' => $reglement_created_path
        ];
    }

    public function validationNewAuto($value){
        //$arrayValue = $value->toArray();

        $arrayValue = is_array($value) ? $value : $value->toArray();


        $rulesFraisDos = [                                                                              
            'frais_dossier' => 'nullable|numeric',
        ];
        $validatorFraisDos = Validator($arrayValue, $rulesFraisDos);

        $rulesResPay = [                                                                              
            'reste_a_payer' => 'nullable|numeric',
        ];
        $validatorResPay = Validator($arrayValue, $rulesResPay);
        
        $rulesProrata = [                                                                              
            'prorata' => 'nullable|numeric',
        ];
        $validatorProrata = Validator($arrayValue, $rulesProrata);

        $rulesTypePaie = [                                                                              
            'type_paiement' => 'nullable|int|max:4|min:1',
        ];
        $validatorTypePaie = Validator($arrayValue, $rulesTypePaie);
        
        $rulesModePaie = [                                                                              
            'mode_paiement' => 'nullable|int|max:3|min:1',
        ];
        $validatorModePaie = Validator($arrayValue, $rulesModePaie);
        
        // $rulesDossier = [                                                                              
        //     'dossier_complet' => 'required|int',
        // ];
        // $validatorDossier = Validator($arrayValue, $rulesDossier);
        
        // $rulesNumPermis = [                                                                              
        //     'num_permis' => 'nullable|string',
        // ];
        // $validatorNumPermis = Validator($arrayValue, $rulesNumPermis);

        $rulesLieuPermis = [                                                                              
            'lieu_permis' => 'nullable|string',
        ];
        $validatorLieuPermis = Validator($arrayValue, $rulesLieuPermis);

        $rulesTypePermis = [                                                                              
            'type_permis' => 'nullable|int|max:9|min:1',
        ];
        $validatorTypePermis = Validator($arrayValue, $rulesTypePermis);

        $rulesDatePermis = [                                                                              
            'date_permis' => 'nullable|date_format:d/m/Y',
            // 'date_permis' => ['required', new DateValidCC('date_permis')],
        ];
        $validatorDatePermis = Validator($arrayValue, $rulesDatePermis);

        $rulesModel = [                                                                              
            'model' => 'nullable|string',
        ];
        $validatorModel = Validator($arrayValue, $rulesModel);

        $rulesMatricule = [                                                                              
            'immatriculation' => 'nullable|string',
        ];
        $validatorMatricule = Validator($arrayValue, $rulesMatricule);

        $rulesSra = [                                                                              
            'code_sra' => 'nullable|string',
        ];
        $validatorSra = Validator($arrayValue, $rulesSra);

        // flag validation errors on the original request data used in (update and creation) import
        $validationError = [];
        if ($validatorFraisDos->fails()) {
            $validationError['fraisDos'] = true;
        }
        if ($validatorResPay->fails()) {
            $validationError['resPay'] = true;
        }
        if ($validatorProrata->fails()) {
            $validationError['prorata'] = true;
        }
        if ($validatorTypePaie->fails()) {
            $validationError['typePaie'] = true;
        }
        if ($validatorModePaie->fails()) {
            $validationError['modePaie'] = true;
        }
        // if ($validatorDossier->fails()) {
        //     $validationError['dossier'] = true;
        // }
        // if ($validatorNumPermis->fails()) {
        //     $validationError['numPermis'] = true;
        // }
        if ($validatorLieuPermis->fails()) {
            $validationError['lieuPermis'] = true;
        }
        if ($validatorTypePermis->fails()) {
            $validationError['typePermis'] = true;
        }
        if ($validatorDatePermis->fails()) {
            $validationError['datePermis'] = true;
        }
        if ($validatorModel->fails()) {
            $validationError['model'] = true;
        }
        if ($validatorMatricule->fails()) {
            $validationError['matricule'] = true;
        }
        if ($validatorSra->fails()) {
            $validationError['sra'] = true;
        }
        return $validationError;

    }

    public function validationNewReg($value){
        // Convert to array for validation
       // $arrayValue = $value->toArray();
        $arrayValue = is_array($value) ? $value : $value->toArray();
        $rulesNumPolice = [                                                                              
            'n0_contrat' => 'required',
        ];
        $validatorNumPolice = Validator($arrayValue, $rulesNumPolice);
        
        $rulesNumReg = [                                                                              
            'numero_reg' => 'nullable',
        ];
        $validatorNumReg = Validator($arrayValue, $rulesNumReg);
        
        $rulesMontReg = [                                                                              
            'montant_reg' => 'required|numeric',
        ];
        $validatorMontReg = Validator($arrayValue, $rulesMontReg);

        $rulesDateF = [                                                                              
            'date_f' => 'required|date_format:d/m/Y',
            // 'date_f' => ['required', new DateValidCC('date_f')],
        ];
        $validatorDateF = Validator($arrayValue, $rulesDateF);
        
        $rulesDateReg = [                                                                              
            'date_reg' => 'required|date_format:d/m/Y',
            // 'date_reg' => ['required', new DateValidCC('date_reg')],
        ];
        $validatorDateReg = Validator($arrayValue, $rulesDateReg);

        $rulesNumGdf = [                                                                              
            // 'num_gdf' => 'required_if:statut_id,73,78,79,80,81',
            'num_gdf' => 'nullable|numeric',
        ];
        $validatorNumGdf = Validator($arrayValue, $rulesNumGdf);
        
        $rulesOrdre = [                                                                              
            'ordre' => 'required|int',
        ];
        $validatorOrdre = Validator($arrayValue, $rulesOrdre);
        
        $rulesModeReg = [                                                                              
            'mode_paie_reg' => 'required|int|max:3|min:1',
        ];
        $validatorModeReg = Validator($arrayValue, $rulesModeReg);
        
        $rulesStatutReg = [                                                                              
            'statut_id' => 'required|int',
        ];
        $validatorStatutReg = Validator($arrayValue, $rulesStatutReg);


        // flag validation errors on the original request data used in (update and creation) import
        $validationError = [];
        if ($validatorNumPolice->fails()) {
            $validationError['NumPolice'] = true;
        }
        if ($validatorNumReg->fails()) {
            $validationError['NumReg'] = true;
        }
        if ($validatorMontReg->fails()) {
            $validationError['MontReg'] = true;
        }
        if ($validatorDateF->fails()) {
            $validationError['DateF'] = true;
        }
        if ($validatorDateReg->fails()) {
            $validationError['DateReg'] = true;
        }
        if ($validatorNumGdf->fails()) {
            $validationError['NumGdf'] = true;
        }
        if ($validatorOrdre->fails()) {
            $validationError['Ordre'] = true;
        }
        if ($validatorModeReg->fails()) {
            $validationError['ModeReg'] = true;
        }
        if ($validatorStatutReg->fails()) {
            $validationError['StatutReg'] = true;
        }
        return $validationError;

    }

    public function validationColumnsAuto($array){ 
        // Columns 
        //$heading = $array->getHeading();
        $heading = !empty($array) ? array_keys($array[0]) : [];
        $requiredColumnsSlug = ColumnsMaj::where('required', 1)->where(function($query){
            $query->where('type', 'auto')->orWhere('type', 'sante');
        })->pluck('libelle')->transform(function($cl){
            return $cl =$this->slugify($cl);
        })->toArray();

        $missColumns = array_diff($requiredColumnsSlug, $heading);
        return $missColumns;
    }
    public function validationColumnsReg($array){ 
        // Columns 
        //$heading = $array->getHeading();
        $heading = !empty($array) ? array_keys($array[0]) : [];

        $requiredColumnsSlug = ColumnsMaj::where('required', 1)
                                        ->where('type', 'reg')
                                        ->pluck('libelle')
                                        ->transform(function($cl){
                                                        return $cl =$this->slugify($cl);
                                                    })->toArray();

                                                            // Slugify $heading so we can insure same format when comparing: 
        $headingSlug = collect($heading)->map(function($label) {
            return $this->slugify($label);
        })->toArray(); 
        $missColumns = array_diff($requiredColumnsSlug, $headingSlug);
        return $missColumns;
    }

    /**
     * creates a collection from an excel file and then converts to associative array
    */
    
    function processExcelFile($filePath) {
        $array = Excel::toCollection([], $filePath)->first();

        $headings = $array->shift()->toArray();

        // slugify headings
        $headingSlug = collect($headings)->map(function($label) {
            return $this->slugify($label);
        })->toArray(); 

        // Define date columns (for formatting) //colonnes de dates, existantes dans la table columns_maj
        
        $dateColumns = [
            'date_denregistrement', 
            'date_deffet', 
            'date_de_naissance', 
            'date_reg', 
            'date_f', 
            'date_mise_en_demeure_suite_impaye', 'date_remise_au_contentieux suite_impaye',
            'date_denregistrement résiliation',
            'date_de_fin_dacs',
            'date_dannulation',
            'date_permis',
            'date_dimpaye',
            'date_mise_en_demeure_suite_impaye',
            'date_remise_au_contentieux_suite_impaye',
            'date_denregistrement_resiliation',
            'date_deffet_resiliation',
            'date_de_fin_dACS',
            'date_fin_contrat',
        ];

        // Format all date values to d/m/Y
        $formattedArray = $array->map(function($row) use ($headingSlug, $dateColumns) {
            foreach ($row as $key => $value){
                // Check if the column is a date column and if the value looks like a date
                if (in_array($headingSlug[$key], $dateColumns) && is_numeric($value) ) {
                    $formattedRow[$headingSlug[$key]] = Date::excelToDateTimeObject((int) $value)->format('d/m/Y');

                } else {
                    $formattedRow[$headingSlug[$key]] = $value;
                }
            }
            return $formattedRow;
        });
        
        //return the associative array with slugified headings
        return $formattedArray->map(function($row) use ($headingSlug) {
            return array_combine($headingSlug, $row);
        });

    }

    /**
     * Store the given export data as an excel file
     */
    private function storeExcelFile($export, $filename, $storagePath = null, $folder = null)
    {
        if ($this->uploadMode == 'server' ) {
        $fullPath = $storagePath.'/'.$filename;
        $fullPath = $storagePath.'/'.$filename;

            $fullPath = $storagePath.'/'.$filename;

            Excel::store($export, $fullPath);
            return str_replace('\\', '/', storage_path("app".'/'.$fullPath)); // Return the storage path if stored successfully
        }
        else {
            $fullPath = $storagePath.'/'.$filename;
            Excel::store($export, $fullPath, 'public2');
            return str_replace('\\', '/', public_path("upload/$folder/{$filename}"));
        }

    }
    // =============    Auto + REG    =================> 


    // ================================    Services    =======================================> 
    

    public function changeContratsImpayesFichesGenStatut(Request $request){
        $user = Auth::user();
		FonctionController::setConfigMySql2($user->courtier_id);

        try {
            $validatedData = $request->validate([
                'num_police' => 'required|array|min:1',
                'num_police.*' => 'string',
                'statut_slug' => 'required|string|exists:mysql2.statuts,slug',
                'commentaire' => 'nullable|string'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => $e->errors()], 422);
        }


        $numPolice = $validatedData['num_police'];
        $statutSlug = $validatedData['statut_slug'];
        // $commentaire = $validatedData['commentaire'] ?? null;

        // The 'exists:statuts,slug' rule ensures $statut exists.
        $statut = Statut::where('slug', $statutSlug)->firstOrFail();

        $statutContratClient = Statut::where('slug', 'contrat-client')->firstOrFail();

        // Fetch fiches associated via the contrat_impaye_id link
        $fiches = Fiche::where(function($query) {
                        $query->whereBetween('fiches.created_at', ['2025-03-14', '2025-04-23']);
                    })
                    ->join('contrats', 'fiches.contrat_impaye_id', '=', 'contrats.id')
                    ->join('devis', 'contrats.devis_id', '=', 'devis.id')
                    ->whereIn('contrats.num_police', $numPolice)
                    ->where('contrats.statut_id', $statutContratClient->id)
                    ->where('formule->cieCode', 'eurodommages')
                    ->select('fiches.id', 'fiches.num_fiche', 'fiches.statut_id', 'fiches.userable_id', 'fiches.userable_type', 'fiches.dispatchable_id', 'fiches.dispatchable_type', 'contrats.num_police')
                    ->get();

        if ($fiches->isEmpty()) {
            throw new \Exception("Aucune fiche trouvée pour les numéros de police fournis.");
        }

        $updatedCount = 0;
        $skippedCount = 0;
        $processedPoliceNumbers = []; // Keep track of police numbers for found fiches

        foreach($fiches as $fiche) {
            $processedPoliceNumbers[] = $fiche->num_police; // Track processed police numbers

            if($fiche->statut_id == $statut->id) {
                Log::channel('injectionContrat')->info("Fiche impayée numéro {$fiche->num_fiche} (Contrat {$fiche->num_police}) déjà avec le statut '{$statut->libelle}'.");
                $skippedCount++;
                continue;
            }

            $oldStatutLibelle = $fiche->statut->libelle ?? 'N/A';
            $fiche->statut_id = $statut->id;
            $fiche->save();

            $updatedCount++;

            // trace changement statut fiche impayé generated
            $traceC = 'changer-statut-fiche-impaye-batch';
            $titre = 'changer statut fiche impayée générée par batch';

            $tabInfoTraceMatiere = [
                'courtier_id'        => $user->courtier_id,
                'fiche_id'           => $fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $fiche->userable_id,
                'userable_type'      => $fiche->userable_type,
                'entitable_id'       => $fiche->id,
                'entitable_type'     => 'Fiche',
                'dispatchable_id'    => $fiche->dispatchable_id,
                'dispatchable_type'  => $fiche->dispatchable_type,
                'statut_id'          => $fiche->statut_id,
                'slug'               => $traceC,
                'commentaire'        => $titre.' : '.$statut->libelle,
                'complement'         => $request->commentaire,
                'active'             => 1,
            ];

            event(new EventTraceMatieres($tabInfoTraceMatiere));

            $statusActionFiche = new Statutaction;
            $statusActionFiche->user_id = Auth::user()->id;
            $statusActionFiche->statut_id = $fiche->statut_id;
            $statusActionFiche->entitable_id = $fiche->id;
            $statusActionFiche->entitable_type = "Fiche";
            $statusActionFiche->save();

            Log::channel('injectionContrat')->info("Statut de la fiche impayée numéro {$fiche->num_fiche} (Contrat {$fiche->num_police}) changé de '{$oldStatutLibelle}' vers '{$statut->libelle}'.");

        }

        // Determine which requested police numbers didn't have a corresponding fiche found
        $notFoundPolice = array_diff($numPolice, array_unique($processedPoliceNumbers));
        if (!empty($notFoundPolice)) {
            Log::channel('injectionContrat')->warning("Aucune fiche impayée générée trouvée pour les numéros de police suivants: " . implode(', ', $notFoundPolice));
        }

        $feedbackMessage = "Traitement terminé pour le statut '{$statut->libelle}'. {$updatedCount} fiche(s) mise(s) à jour.";
        $feedbackMessage .= " {$skippedCount} fiche(s) étaient déjà au statut demandé.";
        
        if (!empty($notFoundPolice)) {
            $feedbackMessage .= " Les numéros de police suivants n'ont pas été trouvés ou n'avaient pas de fiche impayée générée associée: " . implode(', ', $notFoundPolice) . ".";
        }

        // Keep a record of the action result in the logs
        Log::channel('injectionContrat')->info($feedbackMessage);

        return response()->json(['success' => $feedbackMessage], 200);
    }

    public function changeContratsNumPolicesBatch(Request $request){
        $user = Auth::user();
		FonctionController::setConfigMySql2($user->courtier_id);

        try {
            $validatedData = $request->validate([
                'numPolicesMigrations' => 'nullable|array',
                'commentaire' => 'nullable|string'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => $e->errors()], 422);
        }

        // Array of num_police holding contrats to be migrated
        $numPolicesMigrations = $request->numPolicesMigrations ?? [
            'oldNumPolice' => 'newNumPolice',
        ];

        $commentaire = $request->commentaire ?? 'Migration automatique de numéro de police MAJ Contrats';

        // Fetch contrats
        $contrats = Contrat::whereIn('contrats.num_police', array_keys($numPolicesMigrations))
                    ->join('devis', 'contrats.devis_id', '=', 'devis.id')
                    ->where('formule->cieCode', 'CEGEMA')
                    ->select('contrats.*')
                    ->get();

        if ($contrats->isEmpty()) {
            return response()->json(['error' => "Aucun contrat trouvé pour les numéros de police fournis."], 404);
        }

        $updatedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $results = [];
        // Array of num_police not found in the database
        $notFoundPolices = array_keys($numPolicesMigrations);
        $contratController = new ContratController();

        foreach($contrats as $contrat) {
            $oldNumPolice = $contrat->num_police;
            $newNumPolice = $numPolicesMigrations[$oldNumPolice];

            // Remove from notFoundPolices
            $notFoundPolices = array_diff($notFoundPolices, [$oldNumPolice]);

            // Skip if old and new num_police are the same
            if ($oldNumPolice == $newNumPolice) {
                $skippedCount++;
                $message = "Contrat skipped: {$oldNumPolice} - Ancien et nouveau numéro de police identiques";
                Log::channel('injectionContrat')->info($message);
                $results[] = [
                    'old_num_police' => $oldNumPolice,
                    'new_num_police' => $newNumPolice,
                    'status' => 'skipped',
                    'message' => $message
                ];
                continue;
            }

            try {
                // Create a request object to pass to ModifierNumPolice
                $modifierRequest = new Request([
                    'contrat_hash' => Hashids::encode($contrat->id),
                    'num_police' => $newNumPolice,
                    'commentaire' => $commentaire,
                ]);

                // Call ModifierNumPolice from ContratController
                $contratController->ModifierNumPolice($modifierRequest);

                $updatedCount++;
                $message = "Contrat updated: {$oldNumPolice} -> {$newNumPolice} - Numéro de police modifié avec succès";
                Log::channel('injectionContrat')->info($message);
                $results[] = [
                    'old_num_police' => $oldNumPolice,
                    'new_num_police' => $newNumPolice,
                    'status' => 'success',
                    'message' => $message
                ];
            } catch (\Exception $e) {
                $errorCount++;
                $errorMessage = $e->getMessage();
                $message = "Contrat error: {$oldNumPolice} -> {$newNumPolice} - Error: {$errorMessage}";
                Log::channel('injectionContrat')->error($message);
                $results[] = [
                    'old_num_police' => $oldNumPolice,
                    'new_num_police' => $newNumPolice,
                    'status' => 'error',
                    'message' => $errorMessage
                ];
            }
        }

        $feedbackMessage = "Traitement terminé. {$updatedCount} contrat(s) mis à jour.";
        $feedbackMessage .= " {$skippedCount} contrat(s) ignorés.";

        if ($errorCount > 0) {
            $feedbackMessage .= " {$errorCount} contrat(s) en erreur.";
        }

        if (!empty($notFoundPolices)) {
            $notFoundCount = count($notFoundPolices);
            $feedbackMessage .= " {$notFoundCount} contrat(s) non trouvé(s).";
            
            // Log details of not found contracts
            $notFoundPolicesLog = implode(', ', $notFoundPolices);
            $message = "*contrat(s) non trouvé(s): [{$notFoundPolicesLog}]";
            Log::channel('injectionContrat')->warning($message);
        }

        // Keep a record of the action result in the logs
        Log::channel('injectionContrat')->info("**{$feedbackMessage}");

        return response()->json([
            'success' => $feedbackMessage,
            'results' => $results,
            'notFoundPolices' => $notFoundPolices
        ], 200);
    }


}