<?php

namespace App\Services;

use App\Lead;
use Carbon\Carbon;
use App\StatusPrestataire;
use App\Events\EventLeadUpdated;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Crypt;
use App\Http\Controllers\FonctionController;

class PrestataireService
{

    /**
     * Inform Prestataire about a lead update, via webhooks.
     *
     * @param Lead $lead
     * @param string|null $status Optional status (default: "appel").
     * @param float|null $conversionValue Optional conversion value.
     */

    public function handlePrestatairesWebhooks(EventLeadUpdated $event) 
    {
        // Configure database
        FonctionController::setConfigMySql2($event->courtierId);

        $lead = Lead::findOrFail($event->leadId);

        $groupePubSlug = $lead->getGroupePubSlug();
        $prestataire = $lead->groupepubprov?->provenance?->prestataire;
        $prestataireNomReel = $lead->getPrestataireNomReel();

        // Note: we could consider adding a param to the groupepub as well if they decide to activate by groupe pub for other prestataires
        if(!$prestataire || !$prestataire->uses_webhook || ($prestataireNomReel == 'YACLA' && $groupePubSlug != 'y-w-h')) {
            return; // early return for prestataires who aren't concerned
        }

        $leadData = isset($lead->data) ? json_decode($lead->data) : null; 
        $leadEmail = $leadData->email ?? null ;
        $leadTel = getFirstNonEmpty($leadData, 'tel_mobile', 'tel_bureau', 'tel_domicile', 'phone', 'tel', 'fax');
        $leadStatut = $event->leadStatut;
        $conversionValue = $event->conversionValue;

        if ($prestataire->uses_known_format) { // if the prestataire uses the same format as what we've established in the documentation
            $this->sendDataToPrestataire($leadEmail, $leadTel, $leadStatut, $conversionValue, $prestataire);
        }

        else if ($prestataireNomReel == 'WEEDO IT' || $prestataireNomReel == 'KASTALEADS') { // weedo it uses different format
            $leadCreationDate = $lead ->created_at;
            $idCommand = $prestataireNomReel == 'KASTALEADS' ? "Malussia" : null;

            $this->sendDataToWeit($leadEmail, $leadTel, $leadStatut, $leadCreationDate, $prestataire, $idCommand);
        }

    }

    /**
     * General function for most prestataires
     */
    private function sendDataToPrestataire($leadEmail, $leadTel, $leadStatut, $conversionValue, $prestataire)
    {
        // get prestataire's status mapping
        //@TODO : normalize this with a pivot table so that many prestataires could use the same mapping without duplicating data
        $clientState = StatusPrestataire::where('prestataire_id', $prestataire->id)
                            ->where('internal_status', $leadStatut)
                            ->first()?->prestataire_status; 
                            
        if (!$clientState) {
            Log::channel('prestataire_webhook')->error("lead status does not match any internal clientState, lead status: {$leadStatut}" ); 
            return; 
        }

        $prestataireUrl = $prestataire->webhook_url;
        $authorizationHeader = Crypt::decryptstring($prestataire->webhook_token);

        if (empty($prestataireUrl) || empty($authorizationHeader)) {
            Log::channel('prestataire_webhook')->critical("Prestataire URL or Authorization header not set in database. Prestataire:" . $prestataire->nom_reel);
            return;
        }
        
        $data = [
            'lead' => [
                'email' => $leadEmail,
                'tel_mobile' => $leadTel, 
            ],
            'clientState' => $clientState 
        ];

        if ($conversionValue) {
            $data['converionValue'] = $conversionValue;
        }

        $response = Http::withHeaders([
            'Authorization' => $authorizationHeader,
            'Content-Type' => 'application/json',
        ])
        ->put($prestataireUrl, $data);

        $responseBody = json_decode($response->getBody()->getContents(), true);

        if ($responseBody['success'] == true ) {
            Log::channel('prestataire_webhook')
                ->info(
                    'Prestataire webhook: ', $prestataire->nom_reel,
                    'Prestataire API Success, response:', 
                    $responseBody, 'data sent: ', 
                    $data);
        } else {
            Log::channel('prestataire_webhook')
                ->error('API Error from Prestataire API', [
                    'prestataire' => $prestataire->nom_reel,
                    'response_from_api' => $responseBody, 
                    'data_sent_to_api' => $data, 
                ]);
        }

    }

    /** 
     * Specific for WEIT and (kastaleads )
     * the $idCommand is an additional param required by kastaleads only
     */
    private function sendDataToWeit($leadEmail, $leadTel, $leadStatut, $leadCreationDate, $prestataire, $idCommand)
    {

        $clientState = StatusPrestataire::where('prestataire_id', $prestataire->id)
                            ->where('internal_status', $leadStatut)
                            ->first()?->prestataire_status; 
                            
        if (!$clientState) {
            Log::channel('prestataire_webhook')->error("{$prestataire->nom_reel}: lead status does not match any internal clientState, lead status: {$leadStatut}" ); 
            return; 
        }

        // Get prestataire URL and Authorization Header
        $prestataireUrl = $prestataire->webhook_url;
        $authorizationHeader = Crypt::decryptstring($prestataire->webhook_token);

        if (empty($prestataireUrl) || empty($authorizationHeader)) {
            Log::channel('prestataire_webhook')->critical('Prestataire URL or Authorization header not set in database. Prestataire:' . $prestataire->nom_reel);
            return;
        }
        
        $data = [
            'id_client' => 582, // waiting for prestataire's response as to how to handle this param
            'email' => $leadEmail ?? "", // in their documentation they have email set to an empty string
            'telephone' => $leadTel, 
            'statuts' => $clientState, 
            'date' => Carbon::createFromFormat('d/m/Y H:i', $leadCreationDate)->format('d/m/Y')
        ];

        if($idCommand) {
            $data['id_commande'] = $idCommand;
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '. $authorizationHeader,
            'Content-Type' => 'application/json',
        ])
        ->post($prestataireUrl, $data);

        if ($response->failed()) {
            Log::channel('prestataire_webhook')->error('Webhook request failed', [
                'prestataire' => $prestataire->nom_reel,
                'data sent' => $data,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        } else {
            Log::channel('prestataire_webhook')->info('Webhook request successful', [
                'prestataire' => $prestataire->nom_reel,
                'data sent' => $data,
                'response status' => $response->status(),
                'response body' => $response->body(),
            ]);
        }
    }

}