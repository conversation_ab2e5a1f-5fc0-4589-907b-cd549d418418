<?php

namespace App\Http\Controllers;

use random;
use App\Fam;
use App\Dpps;
use App\Site;
use App\User;
use DateTime;
use App\Devis;
use App\Fiche;
use App\Gamme;
use App\Motif;
use App\Tiers;
use App\Client;
use App\Equipe;
use App\Regime;
use App\Statut;
use ZipArchive;
use App\Branche;
use App\Contrat;
use App\DevisLR;
use App\Parente;
use App\Service;
use App\Societe;
use App\Courtier;
use App\SiteUser;
use App\Groupepub;
use App\ModelMail;
use App\Reglement;
use App\Typefiche;
use Carbon\Carbon;
use Pusher\Pusher;
use App\EquipeUser;
use App\Noteclient;
use App\Permission;
use App\Profession;
use App\BrancheUser;
use App\Groupemotif;
use App\Prestataire;
use App\ServiceUser;
use App\SocieteUser;
use App\TypeService;
use App\Modepaiement;
use App\Notification;
use App\Statutaction;
use App\TraceMatiere;
use App\Typepaiement;
use App\Groupebranche;
use App\Questionnaire;
use App\PermissionUser;
use App\BrancheCourtier;
use App\InfractionMotif;
use App\QuestionnaireDda;
use App\CommissionCabinet;
use App\CommissionFormule;
use App\BrancheStatutMotif;
use App\RecalculeCacFailed;
use App\SituationFamiliale;
use Hamcrest\Type\IsObject;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\GroupepubProvenance;
use App\Groupequestionnaire;
use App\Informationbancaire;
use App\RecalculeCacActions;
use App\RecalculeCacSucceed;
use App\TypePermisDpp;
use Illuminate\Http\Request;
use App\BrancheEntiteService;
use App\Exports\ViewDataExport;
use App\Groupequestionnairedda;
use App\Exports\ArrayDataExport;
use App\Events\EventTraceMatieres;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Resources\DevisResource;
use App\Http\Resources\FicheResource;
use App\Http\Resources\TiersResource;
use Illuminate\Support\Facades\Crypt;
use App\Events\EventNotificationUsers;
use App\Http\Resources\ContratResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\DevisController;
use App\Http\Controllers\FicheController;
use App\Http\Controllers\TiersController;
use Illuminate\Database\Eloquent\Builder;
use App\Http\Requests\ContratTracesRequest;
use App\Http\Controllers\FonctionController;
use Illuminate\Validation\ValidationException;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Resources\CommissionCabinetResource;
use App\Profil;

class ContratController extends Controller
{
    public function index()
    {
        /** @var User $user */
        $user = Auth::user();
        
        if ($user->isAdminCourtier()|| $user->isSuperAdmin()) {
            return view('errors.401');
        } else {
            FonctionController::setConfigMySql2($user->courtier_id);
            $serviceValidation = 0;
            
            if ($user->isGestionnaire()) { 
                $serviceUser = ServiceUser::where('user_id', $user->id)->first();

                if ($serviceUser->service_id == 1) { // service validation
                    $serviceValidation = 1;
                }
            }

            return view('contrats.index', [
                'service' => $serviceValidation
            ]);
        }
    }

    public function getUsers($export = null)
    {
        /** @var User $user */
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();
        
        FonctionController::setConfigMySql2($user->courtier_id); 

        $t = new TiersController;
        $userIds = [];
        $seeAll = false;
        //La permission pour voir tous les contrat
        $seeAll = $user->permissions()->where('name', 'voir-tous-contrats')->exists();

        //permission voir tout contrats de son site
        $hasPermissionVoirContratsSite = $user->permissions()->where('name','voir-contrats-site')->exists();
        
        if ((($user->isAnimateur() || $user->isResponsableSite() || $user->isResponsableSociete()) && !$seeAll) || ($hasPermissionVoirContratsSite && !$seeAll)) { 
            $userIds = array_collapse($t->listUsers($user->id, $hasPermissionVoirContratsSite));
        } elseif ($user->isResponsableCourtier() || $seeAll) { 

            $userIds = User::where('courtier_id', $user->courtier_id)->pluck('id');
        } elseif ($user->isConseillerOrCommercial() && !$hasPermissionVoirContratsSite) {
            $userIds = array($user->id);

        } elseif ($user->isGestionnaire()) { // Gestionnaire || Responsable gestion
            $usersId = [];
            $serviceId = ServiceUser::where('user_id', $user->id)
                                    ->whereActive(1)
                                    ->pluck('service_id');


            $societeIds = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Societe')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');


            $siteIds[] = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Site')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');

            $equipeIds[] = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Equipe')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');
                                              
            
            $societeIds = SocieteUser::whereIn('societe_id', $societeIds)
                                    ->where('deleted', NULL)
                                    ->get()
                                    ->pluck('societe_id');


            $usersId[] = SocieteUser::whereIn('societe_id', $societeIds)
                                    ->where('user_id', '!=' , $user->id)
                                    ->where('deleted', NULL)
                                    ->whereActive(1)
                                    ->get()
                                    ->pluck('user_id');

            $siteIds = Site::whereIn('societe_id', $societeIds)
                            ->whereActive(1)
                            ->get()
                            ->pluck('id');

            $usersId[] = SiteUser::whereIn('site_id', $siteIds)
                                ->where('deleted', NULL)
                                ->get()
                                ->pluck('user_id');

            $equipeIds = Equipe::whereIn('site_id', $siteIds)
                                ->whereActive(1)
                                ->get()
                                ->pluck('id');

            $usersId[] = EquipeUser::whereIn('equipe_id', $equipeIds)
                                    ->where('deleted', NULL)
                                    ->get()
                                    ->pluck('user_id');
            
            $collection = collect(array_collapse($usersId));

            $unique = $collection->unique();

            $userIds = $unique->values()->all();
        }

        return $userIds;
    }

    public function statusParPermissions($export = null)
    {
        /** @var User $user */
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();
        $user->load(['profil', 'permissions']);

        FonctionController::setConfigMySql2($user->courtier_id);

        $permissionsUser = PermissionUser::whereUserId($user->id)
                                        ->get()
                                        ->pluck('permission_id')
                                        ->toArray();

        $statutsContrat = [];

        if (in_array(198, $permissionsUser)) {
            $statutsContrat[] = 20;
        }

        if (in_array(199, $permissionsUser)) {
            $statutsContrat[] = 21;
        }

        if (in_array(200, $permissionsUser)) {
            $statutsContrat[] = 23;
        }

        if (in_array(201, $permissionsUser)) {
            $statutsContrat[] = 22;
        }

        if (in_array(251, $permissionsUser)) {
            $statutsContrat[] = 34;
        }

        if (in_array(280, $permissionsUser)) {
            $statutsContrat[] = 40;
        }
        if (in_array(329, $permissionsUser)) {
            $statutsContrat[] = 57;
        }
         if (in_array(332, $permissionsUser)) {
            $statutsContrat[] = 58;
        }
         if (in_array(333, $permissionsUser)) {
            $statutsContrat[] = 59;
        }
         if (in_array(348, $permissionsUser)) {
            $statutsContrat[] = 75;
        }

        if (in_array(374, $permissionsUser)) {
            $statutsContrat[] = 92;
        }

        if (in_array(1090, $permissionsUser)) {
            $statutsContrat[] = 105;
        }

        if (in_array(1102, $permissionsUser)) {
            $statutsContrat[] = 107;
        }

        $CanlisterContratsImpaye = $user->permissions()
            ->where('name', 'lister-contrats-impaye')
            ->exists();

        if ($CanlisterContratsImpaye) { //CHECK LATER
            $statutsContrat[] = 107;
        }

        $CanlisterContratsDuplication = $user->permissions()
            ->where('name', 'lister-contrats-duplication')
            ->exists();
        
        if ($CanlisterContratsDuplication) {
            $statutDup = Statut::where('slug', 'duplication')->where('groupestatut_id', 3)->first();
            $statutsContrat[] = $statutDup->id ?? '';
        }

        return $statutsContrat;
    }

    public function ContratsDroitQuery($export = null)
    {
        /** @var User $user */
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();
        $user->load(['profil', 'permissions']);
        
        $asaf = false;

        $canSeeAllStatutsContrats = $user->permissions->where('name', 'voir-tous-statuts-contrats')->first() ? true : false;

        $allowedUsers = $this->getUsers($export);

        $allowedStatuts = [];
        if (!$canSeeAllStatutsContrats) {
            $allowedStatuts = $this->statusParPermissions($export);
        }
        
        FonctionController::setConfigMySql2($user->courtier_id);

        // Gestionnaire ASAF
        if ($user->isGestionnaire()) {
            $gestionnaire = ServiceUser::where('user_id', $user->id)->first();
            $service = Service::where('id', $gestionnaire->service_id)
                                    ->whereRaw("JSON_CONTAINS(type , '3')")
                                    ->first();
            if ($service) {
                $asaf = true;
            }
        }
        /** @var User $user */
        $user = Auth::user();
        $contrats = Contrat::where('contrats.active', 1)
                    ->when(!$canSeeAllStatutsContrats, function($query) use ($allowedStatuts){
                        $query->whereIn('contrats.statut_id', $allowedStatuts);
                    });
                
        if ($user->isGestionnaire()) {
            $serviceGestionController = new ServiceGestionController;
            $splitedEquipes = $serviceGestionController->getGestionnaireEquipesBranches($user->id); // Gestionnaire only Get Contrats with "Branches" affected to his service
            
            if (count($splitedEquipes)) {
                $contrats = $contrats->whereHas('devis', function ($query) use ($splitedEquipes) {
                    $query->whereHas('fiche', function ($query) use ($splitedEquipes) {
                        $ficheController = new FicheController;
                        $query = $ficheController->filtreFichesByServiceBranche($query, $splitedEquipes);
                    });
                });
            } else {
                // if no equipes allowed to be managed by this service, Return 0 devis
                $contrats->whereRaw('1 = 0');
            }
        }
        $contrats->join('devis', function($join) use ($allowedUsers, $asaf, $user) {
            $join->on('devis.id', 'contrats.devis_id')
                ->whereIn('devis.user_id', $allowedUsers)
                ->when($asaf, function ($query) {
                    $query->where('formule->cieCode', 'ASAF');
                });
                
        })
        ->select('contrats.*');

        return $contrats;

    }

    public function loadContrats()
    {
        $user = Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        $contratPag = $this->ContratsDroitQuery()
                            ->where('contrats.statut_id', '!=', 40)
                            ->with(['devis' => function ($query) {
                                $query->with('typefiche');
                                $query->with('cible');
                                $query->with('modePaiement');
                                $query->with('typePaiement');
                                $query->with('infosBancairesPrelevement');
                                $query->with('infosBancairesRemboursement');
                                $query->with(['reglements' => function($query) {
                                    $query->with('mode');
                                    $query->with('statut');
                                }]);
                                $query->with(['devisLR' => function ($q) {
                                    $q->with('statut');
                                    $q->with('motif');
                                    $q->whereActive(1);
                                }]);
                            }])
                            ->with(['client' => function ($query) {
                                $query->with('tiers');
                            }])
                            ->with('statut')
                            ->with(['noteclients' => function($q) {
                                $q->orderBy('type', 'asc');
                            }])                            
                            ->OrderBy('contrats.date_revalidation', 'desc')
                            ->paginate(15);
                           
        $contrats = $this->listContratss($contratPag);

        return $contrats;
    }

    public function listContratss($contrats, $export = null)
    {
        $allContratsDevis = Devis::whereIn('id', $contrats->pluck('devis_id'))
                                ->with('fiche')
                                ->with('tiers')
                                ->get();

        $lastStatuts = Statutaction::whereIn('entitable_id', $contrats->pluck('id'))
                                    ->where('entitable_type', 'Contrat')
                                    ->whereActive(1)
                                    ->latest()
                                    ->get();

       /* $motifReport = Motif::where('slug', "report-date-deffet")->where('groupemotif_id', 4)->first();*/
        $allContratsUsers = User::whereIn('id', $allContratsDevis->pluck('user_id'))->get();

        foreach ($contrats as $keyC => $contrat) {            
            DevisResource::withoutWrapping();

            $devis = $allContratsDevis->where('id', $contrat->devis_id)->first();
            
            $dateEffetFr = new Carbon($contrat->devis->date_effet);
            $contrat->devis->date_effet_fr = $dateEffetFr->format('d/m/Y');
            $devis = new DevisResource($devis);
            $devis_hash = collect($devis)['hash_id'];
            $contrat->hash_devisid = $devis_hash;
            $contrat->num_devis = $devis->num_devis;

            // date contrat
            Carbon::setLocale('fr');
            $dateInsert = new Carbon($contrat->created_at);
            $contrat->cree_le = $dateInsert->format('d/m/Y');
            $diff = Carbon::now()->diffForHumans($dateInsert); 
            $dates = explode (' ' , $diff);

            if ($dates[1] == 'minutes' || $dates[1] == 'minute') {
                $str = $dates[1][0].$dates[1][1].$dates[1][2];
            } else if ($dates[1] == 'semaine' || $dates[1] == 'semaines') {
                $str = 'w';
            } else {
                $str = $dates[1][0];
            }

            $duree = $dates[0].' '.$dates[1];
            $dureeAbr = $dates[0].' '.$str;
            $contrat->duree = $duree;
            $contrat->dureeAbr = $dureeAbr;

            $ficheDetails = $this->listDetailsFichee($devis->fiche->id, $devis->tiers,$export);
            $contrat->fiche = new FicheResource($this->testDroitsFiche($ficheDetails, $export));

            if ($contrat->fiche->branche_id == 3) {
                foreach ($contrat->fiche->infractions as $infraction) {
                    $infraction->sinistre;
                    $motifsIds = json_decode($infraction->motifs_id, TRUE);

                    $infraction->motifs = InfractionMotif::whereIn('id', $motifsIds)->get();
                }

                $contrat->fiche->sinistres;
                $contrat->devis->vehicule = $contrat->devis->vehicule();
            }

            // compteurs nbr contrat par branche
            // $contrat->cpts = $this->nbrContratGroupedByBranche($contrat->fiche['tiers_id'], $export);
            $contrat->formule = json_decode($devis->formule, TRUE);

            $contrat->revalidationAnim = false;

            $ficheController = new FicheController;
            $contrat->affecte_a = $ficheController->affectee_a($contrat->fiche['id'], $devis->user_id, $export);
            
            if (!$export) {
                $contrat->cree_par = $ficheController->cree_par($contrat->fiche);
                $contrat->cree_prov = $ficheController->cree_prov($contrat->fiche);
            }
            
            $lastStatut = $lastStatuts->where('entitable_id', $contrat->id)->take(2)->values();
           
            if (count($lastStatut) > 1) {
                if ($lastStatut[0]->statut_id == 21) {
                    $gestionairesIds = ServiceUser::all()
                                                ->pluck('user_id')
                                                ->toArray();
                    
                    if (!in_array($lastStatut[0]->user_id, $gestionairesIds) && $lastStatut[1]->statut_id == 34) {
                        $contrat->revalidationAnim = true;
                    }
                    else {
                        $contrat->revalidationAnim = false;
                    }
                } 
               /* else if ($motifReport && $contrat->statut_id == 23 && $lastStatut[0]->statut_id == 23 && in_array($motifReport->id,json_decode($lastStatut[0]->motif_id, TRUE))) {
                        $contrat->report_date_effet = true;
                }*/
            }
            
            if ($devis->devis_report) $contrat->reportDate = true;

            $contratUser = $allContratsUsers->where('id', $devis->user_id)->first();;
            $contrat->username = FonctionController::realUsername($contratUser->username);
            $contrat->userImg = $contratUser->photo;
            $contrat->testValGestionnaireContrat = $this->testValGestionnaireContrat($devis->id);
            $brancheId = $contrat->fiche->branche_id;
            $groupeBranche = groupebranche::whereRaw("JSON_CONTAINS(groupebranches.branches, '$brancheId')")->first();
            
            $contrat->grpBranche = $groupeBranche;
        }

        ContratResource::withoutWrapping();
        
        return ContratResource::collection($contrats);
    }

    public function listContrats($contrats, $export = null, $expC = null)
    {

        $motifReport = Motif::where('slug',"report-date-deffet")->where('groupemotif_id',4)->first();

        foreach ($contrats as $keyC => $contrat) {
            DevisResource::withoutWrapping();
            
            $devis = Devis::find($contrat->devis_id)
                        ->load('typefiche')
                        ->load(['devisLR' => function ($q) {
                            $q->with('statut');
                            $q->with('motif');
                            $q->whereActive(1);
                        }]);
            
            $devis = new DevisResource($devis);
            $devis_hash = collect($devis)['hash_id'];
            $contrat->lr = $devis->devisLR;
            $contrat->typefiche = $devis->typefiche;

            $infos_contrat = collect();
            
            $infos_contrat['label'] = $devis->label;
            $infos_contrat['remise'] = $devis->remise;
            $infos_contrat['montant'] = $devis->montant;
            $infos_contrat['jour_pre'] = $devis->jour_pre;
            $infos_contrat['paiement_cb'] = $devis->paiement_cb;
            $infos_contrat['devis_origin'] = $devis->devis_origin;
            $infos_contrat['frais_dossier'] = $devis->frais_dossier;
            $infos_contrat['nbr_mois_gratuit'] = $devis->nbr_mois_gratuit;
            
            $extra_doc = collect();
            
            $extra_doc['lien_pdf'] = $devis->lien_pdf;
            $extra_doc['date_signature'] = $devis->date_signature;
            $extra_doc['lien_signature'] = $devis->lien_signature;
            $extra_doc['transaction_id'] = $devis->transaction_id;
            $extra_doc['date_souscription'] = $devis->date_souscription;

            $statut = Statut::find($contrat->statut_id);
            
            if (preg_match_all('/\b(\w)/', strtoupper($statut->libelle), $m)) {
                $abr = implode('', $m[1]); 
            }

            $statut->abr = $abr;
            $contrat->statut = $statut;

            // date contrat
            Carbon::setLocale('fr');
            $dateInsert = new Carbon($contrat->created_at);
            $contrat->cree_le = $dateInsert->format('d/m/Y');
            $diff = Carbon::now()->diffForHumans($dateInsert); 
            $dates = explode (' ' , $diff);

            if ($dates[1] == 'minutes' || $dates[1] == 'minute') {
                $str = $dates[1][0].$dates[1][1].$dates[1][2];
            } else if ($dates[1] == 'semaine' || $dates[1] == 'semaines') {
                $str = 'w';
            } else {
                $str = $dates[1][0];
            }

            $duree = $dates[0].' '.$dates[1];
            $dureeAbr = $dates[0].' '.$str;
            $contrat->duree = $duree;
            $contrat->dureeAbr = $dureeAbr;

            // client
            $client = Client::find($contrat->client_id);
            
            if ($client) {
                $client->tiers = Tiers::find($client->tiers_id);
            }

            $contrat->client = $client;

            $ficheDetails = $this->listDetailsFiche($devis->num_fiche, $export);
            $contrat->fiche = new FicheResource($this->testDroitsFiche($ficheDetails, $export));

            // compteurs nbr contrat par branche
            $contrat->cpts = $this->nbrContratGroupedByBranche($contrat->fiche['tiers_id'], $export);
            $contrat->formule = json_decode($devis->formule,TRUE);
            $contrat->typesFiche = Typefiche::where('branche_id', $ficheDetails->branche_id)->get();
            $contrat->typefiche_id = $devis->typefiche_id;
            $contrat->date_validation = $devis->date_validation;

            if ($contrat->formule['venteCouplee']) {
                $devisController = new DevisController;
                $contrat->devisLies = $devisController->devisLies($devis, 1, $export);
            }

            $contrat->mode_paiement = Modepaiement::find($devis->modepaiements_id);
            $contrat->type_paiement = Typepaiement::find($devis->typepaiements_id);
            $contrat->infoBancaire_pre = Informationbancaire::find($devis->infobancaires_pre_id);
            $contrat->infoBancaire_rem = Informationbancaire::find($devis->infobancaires_rem_id);
            $contrat->revalidationAnim = false;

            $contrat->infos = $infos_contrat;
            $contrat->extra_doc = $extra_doc;

            $ficheController = new FicheController;
            $contrat->affecte_a = $ficheController->affectee_a($contrat->fiche['id'], $devis->user_id, $export);

            if($expC)
                $contrat->traces = $this->getContratsTraces($contrat, $export);
            
            if(!$export){
                $contrat->cree_par = $ficheController->cree_par($contrat->fiche);
                $contrat->cree_prov = $ficheController->cree_prov($contrat->fiche);
            }
            
            $lastStatut = Statutaction::where('entitable_id', $contrat->id)
                                    ->where('entitable_type', 'Contrat')
                                    ->whereActive(1)
                                    ->latest()
                                    ->take(2)
                                    ->get();
           
            if (count($lastStatut) > 1) {

                if ($lastStatut[0]->statut_id == 21) {
                    $gestionairesIds = ServiceUser::all()
                                                ->pluck('user_id')
                                                ->toArray();
                    
                    if (!in_array($lastStatut[0]->user_id, $gestionairesIds) && $lastStatut[1]->statut_id == 34) {
                        $contrat->revalidationAnim = true;
                    }
                    else {
                        $contrat->revalidationAnim = false;
                    }
                }
                else if($motifReport && $contrat->statut_id == 23 && $lastStatut[0]->statut_id == 23 && in_array($motifReport->id,json_decode($lastStatut[0]->motif_id, TRUE))) {
                        $contrat->report_date_effet = true;

                }
            }
            if($devis->devis_report) $contrat->reportDate = true;
            /** @var User $contratUser */
            $contratUser = User::with('profil')->find($devis->user_id);
            $contrat->username = FonctionController::realUsername($contratUser->username);
            $contrat->userImg = $contratUser->photo;

            if ($contratUser->isResponsableSociete()) {
                $societeId = SocieteUser::where('user_id', $devis->user_id)
                                        ->where('deleted', NULL)
                                        ->first()
                                        ->societe_id;

            } elseif ($contratUser->isResponsableSite()) {
                $siteId = SiteUser::where('user_id', $devis->user_id)
                                ->where('deleted', NULL)
                                ->first()
                                ->site_id;

                $societeId = Site::whereId($siteId)
                                ->first()
                                ->societe_id;

            } elseif ($contratUser->isEquipeMember()) {
                $equipeId = EquipeUser::where('user_id', $devis->user_id)
                                    ->where('deleted', NULL)
                                    ->pluck('equipe_id');

                $equipe = Equipe::where('id', $equipeId[0])->first();
                $societeId = Site::whereId($equipe->site_id)->first()->societe_id;
            } else {
                $societeId = $ficheDetails->societe()->id;
            }

            $societe = Societe::whereId($societeId)->first();  
            $contrat->societe = $societe->nom;
            $contrat->societeLogo = $societe->photo;
            $contrat->hash_devisid = $devis_hash;
            $contrat->num_devis = $devis->num_devis;
            $contrat->contratShow = false;
            $contrat->controlShow = false;
            $contrat->borderEdit = '';
        }

        ContratResource::withoutWrapping();
        return ContratResource::collection($contrats);
    }

    public function listDroits()
    {
        /** @var User $user */
        $user = Auth::user();

        $contrats = [];
        $data = [];
        $asaf = false;

        FonctionController::setConfigMySql2($user->courtier_id); 

        $t = new TiersController;
        
        // Récuperation des utilisateurs Ids

        if($user->isResponsableSociete() && $user->isAnimateur()){
            $userIds = array_collapse($t->listUsers($user->id));
        }
        
        elseif ($user->isResponsableCourtier() || $user->isResponsableSite()) {
            $userIds = User::where('courtier_id', $user->courtier_id)->pluck('id');
        }

        elseif ($user->isConseillerOrCommercial()) {
            $userIds = array($user->id);
        }

        elseif ($user->isGestionnaire()) {
            $usersId = [];
            $serviceId = ServiceUser::where('user_id', $user->id)
                                    ->whereActive(1)
                                    ->pluck('service_id');


            $societeIds = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Societe')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');


            $siteIds[]    = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Site')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');

            $equipeIds[]  = BrancheEntiteService::whereIn('service_id', $serviceId)
                                                ->whereEntitableType('Equipe')
                                                ->whereActive(1)
                                                ->pluck('entitable_id');
                                              
            
            $societeIds     = SocieteUser::whereIn('societe_id', $societeIds)->where('deleted', NULL)->get()->pluck('societe_id');


            $usersId[]      = SocieteUser::whereIn('societe_id', $societeIds)->where('user_id', '!=' ,$user->id)->where('deleted', NULL)->whereActive(1)->get()->pluck('user_id');

            $siteIds        = Site::whereIn('societe_id', $societeIds)->whereActive(1)->get()->pluck('id');
            $usersId[]      = SiteUser::whereIn('site_id', $siteIds)->where('deleted', NULL)->get()->pluck('user_id');

            $equipeIds      = Equipe::whereIn('site_id', $siteIds)->whereActive(1)->get()->pluck('id');
            $usersId[]      = EquipeUser::whereIn('equipe_id', $equipeIds)->where('deleted', NULL)->get()->pluck('user_id');
            
            $collection = collect(array_collapse($usersId));

            $unique = $collection->unique();

            $userIds = $unique->values()->all();
        } else {
            return view('errors.401');
        }

        $data = $this->listPermission();

        // Gestionnaire ASAF
        if ($user->isGestionnaire()) {
            $gestionnaire = ServiceUser::where('user_id', $user->id)->first();
            $service = Service::where('id', $gestionnaire->service_id)
                                    ->whereRaw("JSON_CONTAINS(type , '3')")
                                    ->first();
            if ($service) {
                $asaf = true;
            }
        }

        // Le responsable courtier a le droit de voir toutes les fiches
        $allDevis = Devis::whereIn('id', $data->pluck('devis_id'))->get();

        if ($user->isResponsableCourtier() || $user->isResponsableSite()) {
            $contrats = $data;
        } else {
            foreach ($data as $d) {
                $devis = $allDevis->where('id', $d->devis_id)->first();
                $UserId = $devis->user_id;
                
                if (in_array($UserId, $userIds)) {

                    if (!$asaf) {
                        $contrats[] = $d;
                    } else if (json_decode($devis->formule)->cieCode == 'ASAF') {
                        $contrats[] = $d;
                    }
                }
            }
        }

        return $contrats;
    }

    public function listPermission()
    {
        $user = Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        $permissionsUser = PermissionUser::whereUserId($user->id)
                                        ->get()
                                        ->pluck('permission_id')
                                        ->toArray();

        $statutsContrat = [];

        if (in_array(198, $permissionsUser)) {
            $statutsContrat[] = 20;
        }

        if (in_array(199, $permissionsUser)) {
            $statutsContrat[] = 21;
        }

        if (in_array(200, $permissionsUser)) {
            $statutsContrat[] = 23;
        }

        if (in_array(201, $permissionsUser)) {
            $statutsContrat[] = 22;
        }

        if (in_array(251, $permissionsUser)) {
            $statutsContrat[] = 34;
        }
        if (in_array(280, $permissionsUser)) {
            $statutsContrat[] = 40;
        }
        $contrats = Contrat::whereIn('statut_id', $statutsContrat)->get();
        
        return $contrats;
    }

    public function loadContratsTiers($id)
    {
        $user = Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        $devisIds = Devis::where('tiers_id', $id)->get()->pluck('id');

        $data = Contrat::whereIn('devis_id', $devisIds)
                        ->with(['devis' => function ($query) {
                            $query->with('typefiche');
                            $query->with('modePaiement');
                            $query->with('typePaiement');
                            $query->with(['reglements' => function($query) {
                                $query->with('mode');
                                $query->with('statut');
                            }]);
                            $query->with('infosBancairesPrelevement');
                            $query->with('infosBancairesRemboursement');
                            $query->with(['devisLR' => function ($q) {
                                $q->with('statut');
                                $q->with('motif');
                                $q->whereActive(1);
                            }]);
                        }])
                        ->with(['client' => function ($query) {
                            $query->with('tiers');
                        }])
                        ->with('statut')
                        ->latest()
                        ->get();

        $contrats = $this->listContratss($data);

        return ['contrats' => $contrats, 'contratsCount' => count($data)];
    }

    public function listDetailsFichee($ficheId, $tiers, $export = null)
    {        
        Carbon::setLocale('fr');

        $fiche = Fiche::whereId($ficheId)->first();

        $fiche->listBeneficiaires = $fiche->listBeneficiaires();

        if ($fiche->beneficiaires) {
            $fiche->beneficiaires = json_decode($fiche->beneficiaires);
            // Les membres de la famille
            $beneficiaires = [];
            $i = 0;

            foreach ($fiche->beneficiaires as $beneficiaire) {
                $fam = Fam::where('tiers_id', $tiers['id'])->where('dpps_id', $beneficiaire)->first();
                $fam->parente = Parente::whereId($fam['parent_id'])->first()->libelle;
                
                $dpp = Dpps::whereId($beneficiaire)->first();
                $dateNaiss = new Carbon($dpp->date_naissance);
                $fam->date_naissance = $dateNaiss->format('d/m/Y');
                
                $fam->nom = $dpp->nom;
                $fam->sexe = $dpp->sexe;
                $fam->prenom = $dpp->prenom;
                

                if ($fam['parent_id'] == 2) {
                    $fam->age = $dpp->age;
                    $fam->regime = Regime::whereId($dpp->regime_id)->first()->libelle;
                    $fam->regime_id = $dpp->regime_id;
                    $fam->num_secsocial = $dpp->num_secsocial ? decrypt($dpp->num_secsocial) : '';
                    $fam->orgaffiliation = $dpp->orgaffiliation;
                }

                if ($fam['parent_id'] == 3) {
                    if ($fam['acharge'] == 0) {
                        $fam->ayantDroit = 'C';
                        $fam->regime = 'Conjoint';
                    } else if ($fam['acharge'] == 1) {
                        $fam->ayantDroit = 'P';
                        $fam->regime = 'Assuré';
                    }

                    $i++;
                }

                $beneficiaires[] = $fam;
            }

            $fiche->beneficiaires = $beneficiaires;
        } else {
            $fiche->beneficiaires = [];
        }
             
        if ($tiers['num_dpp'] != 0) {
            $dpp = Dpps::where('tiers_id', $tiers->id)->first();
            $dateNaissance = new Carbon($dpp->date_naissance);
            $dpp->date_naiss = $dateNaissance->format('d/m/Y');
            
            if ($dpp->regime_id) {
                $dpp->regime = Regime::whereId($dpp->regime_id)->first()->libelle;      
            }

            if ($dpp->profession_id) {
                $dpp->profession = Profession::whereId($dpp->profession_id)->first()->libelle;      
            }     

            if ($dpp->situation_familiale) {
                $dpp->situation = SituationFamiliale::whereId($dpp->situation_familiale)->first()->libelle;      
            }     

            $dpp->num_secsocial = ($dpp->num_secsocial) ? decrypt($dpp->num_secsocial) : NULL;

            $tiers->dpp = $dpp;  

            $detailsSouscripteur = explode(":",$fiche->souscripteur);
            $tiers->parente = ($detailsSouscripteur[1] == $tiers->id) ? 'Souscripteur' : 'Assuré'; 
            $fiche->souscripteur_nom = isset(Tiers::find($detailsSouscripteur[1])->raison_sociale) ? Tiers::find($detailsSouscripteur[1])->raison_sociale : '';

            if ($tiers['dpp']['regime_id'] != 0) {
                $tiers->regime = Regime::whereId($tiers['dpp']['regime_id'])->first();
            }
            
            if ($tiers['dpp']['profession_id'] != 0) {
                $tiers->profession = Profession::whereId($tiers['dpp']['profession_id'])->first();
            }
        }

        $dateEffetFr = new Carbon($fiche->date_effet);
        $fiche->date_effet_fr = $dateEffetFr->format('d/m/Y');
        
        $fiche->tiers = new TiersResource($tiers);

        if ($fiche->branche_id == 3) {
            $fiche->vehicule = $fiche->vehicule();
            $fiche->permis = $fiche->permis();

            $fiche->auto_reponses = json_decode($fiche->auto_reponses);
            $fiche->conducteur_secondaire = $fiche->conducteur_secondaire();    
        }
        
        $fiche->branche = Branche::whereId($fiche->branche_id)->first();

        return $fiche;
    }

    public function listDetailsFiche($num, $export = null)
    {

        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

        FonctionController::setConfigMysql2($user->courtier_id);

        $fiche = Fiche::whereNumFiche($num)->first();
        Carbon::setLocale('fr');

        $tiers = Tiers::whereId($fiche->tiers_id)->first();

        $fiche->listBeneficiaires = $fiche->listBeneficiaires();

        if ($fiche->beneficiaires) {
            $fiche->beneficiaires = json_decode($fiche->beneficiaires);
            // Les membres de la famille
            $beneficiaires = [];
            $i = 0;

            foreach ($fiche->beneficiaires as $beneficiaire) {
                $fam = Fam::where('tiers_id', $tiers['id'])->where('dpps_id', $beneficiaire)->first();
                $fam->parente = Parente::whereId($fam['parent_id'])->first()->libelle;
                
                $dpp = Dpps::whereId($beneficiaire)->first();
                $dateNaiss = new Carbon($dpp->date_naissance);
                $fam->date_naissance = $dateNaiss->format('d/m/Y');
                
                $fam->nom = $dpp->nom;
                $fam->sexe = $dpp->sexe;
                $fam->prenom = $dpp->prenom;
                

                if ($fam['parent_id'] == 2) {
                    $fam->age = $dpp->age;
                    $fam->regime = Regime::whereId($dpp->regime_id)->first()->libelle;
                    $fam->regime_id = $dpp->regime_id;
                    $fam->num_secsocial = $dpp->num_secsocial ? decrypt($dpp->num_secsocial) : '';
                    $fam->orgaffiliation = $dpp->orgaffiliation;
                }

                if ($fam['parent_id'] == 3) {
                    if ($fam['acharge'] == 0) {
                        $fam->ayantDroit = 'C';
                        $fam->regime = 'Conjoint';
                    } else if ($fam['acharge'] == 1) {
                        $fam->ayantDroit = 'P';
                        $fam->regime = 'Assuré';
                    }

                    $i++;
                }

                $beneficiaires[] = $fam;
            }

            $fiche->beneficiaires = $beneficiaires;
        } else {
            $fiche->beneficiaires = [];
        }
             
        if ($tiers['num_dpp'] != 0) {
            $dpp = Dpps::where('tiers_id', $tiers->id)->first();
            $dateNaissance = new Carbon($dpp->date_naissance);
            $dpp->date_naiss = $dateNaissance->format('d/m/Y');
            
            if ($dpp->regime_id) {
                $dpp->regime = Regime::whereId($dpp->regime_id)->first()->libelle;      
            }

            if ($dpp->profession_id) {
                $dpp->profession = Profession::whereId($dpp->profession_id)->first()->libelle;      
            }     

            if ($dpp->situation_familiale) {
                $dpp->situation = SituationFamiliale::whereId($dpp->situation_familiale)->first()->libelle;      
            }     

            $dpp->num_secsocial = ($dpp->num_secsocial) ? decrypt($dpp->num_secsocial) : NULL;

            $tiers->dpp = $dpp;  

            $detailsSouscripteur = explode(":",$fiche->souscripteur);
            $tiers->parente = ($detailsSouscripteur[1] == $tiers->id) ? 'Souscripteur' : 'Assuré'; 
            $fiche->souscripteur_nom = Tiers::find($detailsSouscripteur[1])->raison_sociale;

            if ($tiers['dpp']['regime_id'] != 0) {
                $tiers->regime = Regime::whereId($tiers['dpp']['regime_id'])->first();
            }
            
            if ($tiers['dpp']['profession_id'] != 0) {
                $tiers->profession = Profession::whereId($tiers['dpp']['profession_id'])->first();
            }
        }

        $dateEffetFr = new Carbon($fiche->date_effet);
        $fiche->date_effet_fr = $dateEffetFr->format('d/m/Y');
        
        $fiche->tiers = new TiersResource($tiers);
        
        $fiche->branche = Branche::whereId($fiche->branche_id)->first();

        return $fiche;
    }

    public function listDetailsFicheExport($num)
    {
        $fiche = Fiche::whereNumFiche($num)->first();

        $tiers = Tiers::whereId($fiche->tiers_id)->first();

        $fiche->listBeneficiaires = $fiche->listBeneficiaires();
        $fiche->groupepub = '';
        
        if ($fiche->groupepubprov) {
            $fiche->groupepub = $fiche->groupepubprov->groupepub->nom;
        }
        
        if ($tiers['num_dpp'] != 0) {
            $dpp = Dpps::where('tiers_id', $tiers->id)->with('regime', 'profession')->first();

            $dpp->num_secsocial = ($dpp->num_secsocial) ? decrypt($dpp->num_secsocial) : NULL;

            $tiers->dpp = $dpp;
        }
        
        $fiche->tiers = $tiers;

        return $fiche;
    }

   public function rechercheContrat(Request $request, $export = null)
    {
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        $missingDoc = [];
        if ($request->missingDoc) {
            $missingDoc = collect($request->missingDoc)->pluck('id');
        }

        $rechercheTiers = $request->nom || $request->prenom || $request->tel || $request->email || $request->missingDoc || ($request->gels !== null && $request->gels != 2);

        // groupe pub request
        $grpPubProvenances = [];

        if ($request->grpPub) {
            $grpPubProvenances = GroupepubProvenance::where('groupepub_id', $request->grpPub )->pluck('id');

        } elseif ($request->prestataire) {
            $grpPubs = Groupepub::where('prestataire_id', $request->prestataire)->pluck('id');
            $grpPubProvenances = GroupepubProvenance::whereIn('groupepub_id', $grpPubs)->pluck('id');
        }

        switch ($request->typeDate) {
            case 1:
                $columnDate = 'contrats.created_at';
                break;

            case 2:
                $columnDate = 'date_effet';
                break;

            case 3:
                $columnDate = 'date_souscription';
                break;

            case 4:
                $columnDate = 'date_signature';
                break;

            case 6:
                $columnDate = 'devis.date_validation';
                break;

            case 9:
                $columnDate = 'fiches.created_at';
                break;

            case 10:
                $columnDate = 'contrats.date_validation';
                break;
            
            case 11:
                $columnDate = 'devis.date_effet';
                break;

            default:
                $columnDate = '';
                break;
        }

        $rechercheParPrestataire = $request->grpPub || $request->prestataire;
        $rechercheFicheParDate = $request->typeDate == 2 || $request->typeDate == 9;
        $rechercheFiche = $rechercheParPrestataire || $request->num_fiche || $request->brancheId || $rechercheFicheParDate;

        $dateDebut = $request->dateDebut ? new Carbon($request->dateDebut) : '';
        if ($request->for_plafond) {
            $dateFin = $request->dateFin ? $request->dateFin : '';
        } else {
            $dateFin = $request->dateFin ? (new Carbon($request->dateFin))->copy()->addHour(23)->addMinutes(59)->addSeconds(59) : '';
        }

        // fiche
        $fiches = '';
        if ($rechercheFiche) {
            $fiches = Fiche::when($request->num_fiche, function ($query) use ($request) {
                        $query->where('num_fiche', $request->num_fiche);
                    })
                    ->when($request->brancheId, function ($query) use ($request) {
                        $query->where('branche_id', $request->brancheId);
                    })
                    ->when($grpPubProvenances, function ($query) use ($grpPubProvenances) {
                        $query->whereIn('groupepub_provenance_id', $grpPubProvenances);
                    })
                    ->when($rechercheFicheParDate, function ($query) use ($request, $columnDate, $dateDebut, $dateFin) {
                        if ($request->dateDebut && $request->dateFin) {
                            $query->whereBetween($columnDate, [$dateDebut, $dateFin]);
                        } elseif ($request->dateDebut) {
                            $query->where($columnDate, '>=' , $dateDebut);
                        } elseif ($request->dateFin) {
                            $query->where($columnDate, '<=' , $dateFin);
                        }
                    })
                    ->select('num_fiche');
        }

        $rechercheParStructure = $request->societeId || $request->siteId || $request->equipeId || $request->animateurId || $request->conseillerId;
        $rechercheParStructureCreatedBy = $request->societeIdCreatedBy || $request->siteIdCreatedBy || $request->equipeIdCreatedBy || $request->animateurIdCreatedBy || $request->conseillerIdCreatedBy;
        $rechercheDevisParDate = $request->typeDate == 3 || $request->typeDate == 4 || $request->typeDate == 6 || $request->typeDate == 11;

        $rechercheDevis = $rechercheDevisParDate || $request->num_devis || $request->codeCompagnie || $request->codeGamme || $request->garantieId || $rechercheParStructure || $request->typeFiche || $request->promesse || $request->isReglementsRegle || $request->dossierComplet || $request->typeContrat || $request->immatriculation || $request->sra || $request->tarifMin || $request->tarifMax;

        // Les utilisateurs
        $users = [];

        if ($request->conseillerId) {
            $users[] = $request->conseillerId;
        } elseif ($request->animateurId) {
            $users[] = $request->animateurId;
        } elseif ($request->equipeId) {
            $equipeMembers = EquipeUser::where('equipe_id', $request->equipeId)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
            $users = $equipeMembers;
        } elseif ($request->siteId) {
            $responsablesSite = SiteUser::where('site_id', $request->siteId)
                                ->where('deleted', NULL)
                                ->pluck('user_id');

            $equipes = Equipe::where('site_id', $request->siteId)->pluck('id');
            $equipesMembers = EquipeUser::whereIn('equipe_id', $equipes)
                                        ->where('deleted', NULL)
                                        ->pluck('user_id');

            $usersId[] = $responsablesSite;
            $usersId[] = $equipesMembers;

            $users = array_collapse($usersId);
        } elseif ($request->societeId) {
            $responsablesSociete = SocieteUser::where('societe_id', $request->societeId)
                                    ->where('deleted', NULL)
                                    ->pluck('user_id');
           
            $sites = Site::where('societe_id', $request->societeId)->pluck('id');
            $responsablesSite = SiteUser::whereIn('site_id', $sites)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
            
            $equipes = Equipe::whereIn('site_id', $sites)->pluck('id');
            $equipesMembers = EquipeUser::whereIn('equipe_id', $equipes)
                                    ->where('deleted', NULL)
                                    ->pluck('user_id');
            
            $usersId[] = $equipesMembers;
            $usersId[] = $responsablesSite;
            $usersId[] = $responsablesSociete;

            $users = array_collapse($usersId);
        }

        // Les utilisateurs
        $usersCreatedBy = [];

        if ($request->conseillerIdCreatedBy) {
            $usersCreatedBy[] = $request->conseillerIdCreatedBy;
        } elseif ($request->animateurIdCreatedBy) {
            $usersCreatedBy[] = $request->animateurIdCreatedBy;
        } elseif ($request->equipeIdCreatedBy) {
            $equipeMembersCreatedBy = EquipeUser::where('equipe_id', $request->equipeIdCreatedBy)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
            $usersCreatedBy = $equipeMembersCreatedBy;
        } elseif ($request->siteIdCreatedBy) {
            $responsablesSiteCreatedBy = SiteUser::where('site_id', $request->siteIdCreatedBy)
                                ->where('deleted', NULL)
                                ->pluck('user_id');

            $equipesCreatedBy = Equipe::where('site_id', $request->siteIdCreatedBy)->pluck('id');
            $equipesMembersCreatedBy = EquipeUser::whereIn('equipe_id', $equipesCreatedBy)
                                        ->where('deleted', NULL)
                                        ->pluck('user_id');

            $usersIdCreatedBy[] = $responsablesSiteCreatedBy;
            $usersIdCreatedBy[] = $equipesMembersCreatedBy;

            $usersCreatedBy = array_collapse($usersIdCreatedBy);
        } elseif ($request->societeIdCreatedBy) {
            $responsablesSocieteCreatedBy = SocieteUser::where('societe_id', $request->societeIdCreatedBy)
                                    ->where('deleted', NULL)
                                    ->pluck('user_id');
           
            $sitesCreatedBy = Site::where('societe_id', $request->societeIdCreatedBy)->pluck('id');
            $responsablesSiteCreatedBy = SiteUser::whereIn('site_id', $sitesCreatedBy)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
            
            $equipesCreatedBy = Equipe::whereIn('site_id', $sitesCreatedBy)->pluck('id');
            $equipesMembersCreatedBy = EquipeUser::whereIn('equipe_id', $equipesCreatedBy)
                                    ->where('deleted', NULL)
                                    ->pluck('user_id');
            
            $usersIdCreatedBy[] = $equipesMembersCreatedBy;
            $usersIdCreatedBy[] = $responsablesSiteCreatedBy;
            $usersIdCreatedBy[] = $responsablesSocieteCreatedBy;

            $usersCreatedBy = array_collapse($usersIdCreatedBy);
        }

        // devis
        $devis = '';
        if ($rechercheDevis || $rechercheTiers || $rechercheFiche || $rechercheParStructureCreatedBy) {
            $devis = Devis::when($request->num_devis, function ($query) use ($request) {
                        $query->where('num_devis', $request->num_devis);
                    })
                    ->when($request->typeFiche, function ($query) use ($request) {
                        $query->where('typefiche_id', $request->typeFiche);
                    })
                    ->when($rechercheParStructure, function ($query) use ($users) {
                        $query->whereIn('user_id', $users);
                    })
                    ->when($rechercheParStructureCreatedBy, function ($query) use ($usersCreatedBy) {
                        $query->whereHas('fiche', function ($q) use ($usersCreatedBy) {
                            $q->whereIn('cree_par', $usersCreatedBy);
                        });
                    })
                    ->when($request->garantieId, function ($query) use ($request) {
                        $query->where('formule->id', (int)$request->garantieId);
                    })
                    ->when($request->codeGamme, function ($query) use ($request) {
                        $query->where('formule->codeGamme', $request->codeGamme);
                    })
                    ->when($request->codeCompagnie, function ($query) use ($request) {
                        $query->where('formule->cieCode', $request->codeCompagnie);
                    })
                    ->when($request->tarifMin, function ($query) use ($request) {
                        $query->where('formule->tarif', '>=', (double)$request->tarifMin);
                    })
                    ->when($request->tarifMax, function ($query) use ($request) {
                        $query->where('formule->tarif', '<=', (double)$request->tarifMax);
                    })
                    ->when($request->isReglementsRegle == 1, function ($query) {
                        $query->where('reste_a_payer',  0);
                    })
                    ->when($request->isReglementsRegle == 2, function ($query) {
                        $query->where('reste_a_payer', '>', 0);
                    })
                    ->when($request->dossierComplet, function ($query) use ($request) {
                        $query->where('dossier_complet', $request->dossierComplet);
                    })
                    ->when($request->typeContrat, function ($query) use ($request) {
                        $query->where('contrat_tempo', $request->typeContrat);
                    })
                    ->when($request->dossierComplet === 0, function ($query) {
                        $query->whereNull('dossier_complet');
                    })
                    ->when($rechercheDevisParDate, function ($query) use ($request, $columnDate, $dateDebut, $dateFin) {
                            if ($request->dateDebut && $request->dateFin) {
                                $query->whereBetween($columnDate, [$dateDebut, $dateFin]);
                            } elseif ($request->dateDebut) {
                                $query->where($columnDate, '>=' , $dateDebut);
                            } elseif ($request->dateFin) {
                                $query->where($columnDate, '<=' , $dateFin);
                            }
                    })->when(($request->has('promesse') && $request->promesse != 2) , function ($query) use ($request) {
                        if($request->promesse == 0)
                            $query->where('date_promesse', null);
                        elseif($request->promesse == 1)
                            $query->where('date_promesse', '!=', null);
                    })
                    ->when($request->immatriculation || $request->sra, function ($query) use ($request) {
                        $query->join('vehicules', 'vehicules.id', 'devis.vehicule_id');
                        $query->when($request->immatriculation, function ($q) use ($request) {
                            $q->where('vehicules.immatriculation', $request->immatriculation);
                        });
                        $query->when($request->sra, function ($q) use ($request) {
                            $q->where('vehicules.code_sra', $request->sra);
                        });
                    })
                    ->when($request->sra, function ($query) use ($request) {
                        $query->where('vehicules.code_sra', $request->sra);
                    })
                    ->when(($request->has('returnFormule') && $request->promesse), function ($query) use ($request) {
                        $query->select('devis.id', 'devis.tiers_id', 'devis.num_fiche', 'devis.user_id as devis_user_id', 'devis.date_validation as date_validation_prod', 'devis.formule');
                    })
                    ->when((!$request->returnFormule), function ($query) use ($request) {
                        $query->select('devis.id', 'devis.tiers_id', 'devis.num_fiche', 'devis.user_id as devis_user_id', 'devis.date_validation as date_validation_prod');
                    });
        }
        
        // tiers
        $tiers = '';
        if ($rechercheTiers) {
            $tiers = Tiers::join('dpps', 'dpps.id', 'tiers.num_dpp')
                        ->when($request->nom, function ($query) use ($request) {
                            $query->where('nom', 'like', '%' .$request->nom . '%');
                        })
                        ->when($request->prenom, function ($query) use ($request) {
                            $query->where('prenom', 'like', '%' .$request->prenom . '%');
                        })
                        ->when($request->email, function ($query) use ($request) {
                            $query->where(function($q) use ($request) {
                                $q->where('email', 'like', '%' .$request->email . '%');
                                $q->orWhere('numemail', 'like', '%' .$request->email . '%');
                            });
                        })
                        ->when($request->tel, function ($query) use ($request) {
                            $query->where(function($q) use ($request) {
                                $q->where('num_tel', 'like', '%' .$request->tel . '%');
                                $q->orWhere('num_post_tel', 'like', '%' .$request->tel . '%');
                                $q->orWhere('portable', 'like', '%' .$request->tel . '%');
                            });
                        })
                        ->when($missingDoc, function ($query) use ($missingDoc) {
                            $query->leftJoin('document_tiers', 'document_tiers.tiers_id', 'tiers.id');
                            $query->whereNotIn('document_tiers.document_id', $missingDoc);
                            $query->distinct();
                        })
                        ->when($missingDoc, function ($query) use ($missingDoc) {
                            $query->leftJoin('document_tiers', 'document_tiers.tiers_id', 'tiers.id');
                            $query->whereNotIn('document_tiers.document_id', $missingDoc);
                            $query->distinct();
                        })
                        ->when(($request->has('gels') && $request->gels != 2 && $request->gels !== null), function ($query) use ($request) {
                            $query->where('dpps.is_gels', $request->gels);
                        })
                        ->select('tiers.id as tiersId');
        }

        $rechercheContratParDate = $request->typeDate == 1 || $request->typeDate == 10 || $request->for_plafond;
        
        $contrats = $this->ContratsDroitQuery($export)
                        ->withCount('commissionCabinet as countCommissionCabinet')
                        ->when($request->num_police, function ($query) use ($request) {
                            $query->where('num_police', $request->num_police);
                        })
                        ->when(($request->has('rgpd') && $request->rgpd != 2 && $request->rgpd !== null), function ($query) use ($request) {
                            $query->where('contrats.rgpd', $request->rgpd);
                        })
                        ->when($request->statutId, function ($query) use ($request) {
                            $query->where('contrats.statut_id', $request->statutId);
                        })
                        ->when($request->for_plafond && $dateDebut && $dateFin, function ($query) use ($request, $dateDebut, $dateFin) {
                            // $query->whereHas('devis', function ($q) use ($dateDebut) {
                            //     $q->whereYear('devis.date_validation', '=', $dateDebut->year);
                            // }
            
                            $query->whereHas('commissionCabinet', function ($q) {
                                $q->where('types_commission_id', 1); //precompte
                                // $q->withTrashed();  //deleted commissions are included as well
                            });
                            $query->when($dateDebut && $dateFin, function ($query) use ($dateDebut, $dateFin) {
                                // $query->whereBetween('devis.date_validation', [$dateDebut, $dateFin]);
                                // $query->whereBetween('contrats.created_at', [$dateDebut, $dateFin]);
                                $query->whereBetween('devis.date_effet', [$dateDebut, $dateFin]);
                            });
                            // $query->whereNotNull('type_contrat');
            
            
                            // if ($request->report_effet_precompte_date == 'date_effet') {
                            //     //if date precompte after report is date_effet: use fiches.date_effet for reported contrats & contrats.date_validation for nouvelle affaire contracts
                            //     $query->where('type_contrat', 'nouvelle_affaire')
                            //         ->whereYear('contrats.date_validation', '=', $dateDebut->year);
            
                            //     $query->orWhere(function ($q) use ($dateDebut) {
                            //         $q->where('type_contrat', 'report_effet')
                            //         ->whereHas('devis', function ($q) use ($dateDebut) {
                            //             $q->whereHas('fiche', function ($q) use ($dateDebut) {
                            //                 $q->whereYear('fiches.date_effet', '=', $dateDebut->year);
                            //             });
                            //         });
                            //     });
                            // } elseif ($request->report_effet_precompte_date == 'contrats.date_validation') { 
                            //         //if date precompte after report is contrats.date_validation: use contrats.date_validation for all contracts
                            //         $query->whereYear('contrats.date_validation', '=', $dateDebut->year)
                            //             ->whereNotNull('type_contrat');
                            // }
            
                        })
                        ->when($rechercheContratParDate && !$request->for_plafond, function ($query) use ($request, $columnDate, $dateDebut, $dateFin) {
                            if ($request->dateDebut && $request->dateFin) {
                                $query->whereBetween($columnDate, [$dateDebut, $dateFin]);
                            } elseif ($request->dateDebut) {
                                $query->where($columnDate, '>=' , $dateDebut);
                            } elseif ($request->dateFin) {
                                $query->where($columnDate, '<=' , $dateFin);
                            }
                        })
                        ->filterByHoliday();

        $results = DB::connection('mysql2')
                    ->table(DB::connection('mysql2')->raw("({$contrats->toSql()}) as contrats")) 
                    ->mergeBindings($contrats->getQuery())
                    ->when($rechercheDevis || $rechercheTiers || $rechercheFiche || $rechercheParStructureCreatedBy, function ($query) use ($devis) {
                        $query->join( DB::connection('mysql2')->raw("({$devis->toSql()}) as devis"), 'devis.id',  'contrats.devis_id')
                            ->mergeBindings($devis->getQuery());
                    })
                    ->when($rechercheFiche, function ($query) use ($fiches) {
                        $query->join( DB::connection('mysql2')->raw("({$fiches->toSql()}) as fiches"), 'fiches.num_fiche',  'devis.num_fiche')
                            ->mergeBindings($fiches->getQuery());
                    })
                    ->when($rechercheTiers, function ($query) use ($tiers) {
                        $query->join( DB::connection('mysql2')->raw("({$tiers->toSql()}) as tiers"), 'tiers.tiersId', 'devis.tiers_id')
                            ->mergeBindings($tiers->getQuery());
                    });

        if ($request->returnFormule) {
            return $rechercheDevis ? $results->select('contrats.*', 'date_validation_prod', 'devis_user_id', 'devis.formule') : $results->select('contrats.*');
        }

        $results = $rechercheDevis ? $results->select('contrats.*', 'date_validation_prod', 'devis_user_id') : $results->select('contrats.*');
        
        return $results;
    }

    public function rechercheAvanceeContrat(Request $request)
    {
        $results = $this->rechercheContrat($request)
                        ->orderBy('contrats.date_revalidation', 'desc')
                        ->paginate(15);

        $hydrated = Contrat::hydrate($results->all())
                            ->load(['devis' => function ($query) {
                                $query->with('typefiche');
                                $query->with('cible');
                                $query->with('modePaiement');
                                $query->with('typePaiement');
                                $query->with('infosBancairesPrelevement');
                                $query->with('infosBancairesRemboursement');
                                $query->with(['devisLR' => function ($q) {
                                    $q->with('statut');
                                    $q->with('motif');
                                    $q->whereActive(1);
                                }]);
                                $query->with(['reglements' => function($query) {
                                    $query->with('mode');
                                    $query->with('statut');
                                }]);
                            }])
                            ->load(['client' => function ($query) {
                                $query->with('tiers');
                            }])
                            ->load('statut')
                            ->load('noteclients');

        $contrats = $results->setCollection($hydrated);
        
        return $this->listContratss($contrats);

        $user = Auth::user();

        $users = [];
        $datefin = '';
        $datedebut = '';
        
        FonctionController::setConfigMySql2($user->courtier_id); 
        
        // Les utilisateurs
        if ($request->conseillerId) {

            $users[] = $request->conseillerId;

        } elseif ($request->equipeId && !$request->conseillerId) {

            $users = EquipeUser::where('equipe_id', $request->equipeId)
                                ->where('deleted', NULL)
                                ->pluck('user_id');

        } elseif ($request->siteId && !$request->equipeId && !$request->conseillerId) {
            $usersId[] = SiteUser::where('site_id', $request->siteId)
                                ->where('deleted', NULL)
                                ->pluck('user_id');

            $equipes = Equipe::where('site_id', $request->siteId)->pluck('id');

            $usersId[] = EquipeUser::whereIn('equipe_id', $equipes)
                                ->where('deleted', NULL)
                                ->pluck('user_id');

            $users = array_collapse($usersId);
          
        } elseif ($request->societeId && !$request->siteId && !$request->equipeId && !$request->conseillerId) {
            $usersId[] = SocieteUser::where('societe_id', $request->societeId)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
           
            $sites = Site::where('societe_id', $request->societeId)->pluck('id');
            $usersId[] = SiteUser::whereIn('site_id', $sites)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
            
            $equipes = Equipe::whereIn('site_id', $sites)->pluck('id');
            $usersId[] = EquipeUser::whereIn('equipe_id', $equipes)
                                ->where('deleted', NULL)
                                ->pluck('user_id');
           
            $users = array_collapse($usersId);
        }

        // Created by Users
        if ($request->conseillerIdCreatedBy) {
            $usersCreatedBy[] = $request->conseillerIdCreatedBy;
        } elseif ($request->equipeIdCreatedBy && !$request->conseillerIdCreatedBy) {
            $usersCreatedBy = EquipeUser::where('equipe_id', $request->equipeIdCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');
        } elseif ($request->siteIdCreatedBy && !$request->equipeIdCreatedBy && !$request->conseillerIdCreatedBy) {
            $usersIdCreatedBy[] = SiteUser::where('site_id', $request->siteIdCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');

            $equipesCreatedBy = Equipe::where('site_id', $request->siteIdCreatedBy)->pluck('id');

            $usersIdCreatedBy[] = EquipeUser::whereIn('equipe_id', $equipesCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');

            $usersCreatedBy = array_collapse($usersIdCreatedBy);
        } elseif ($request->societeIdCreatedBy && !$request->siteIdCreatedBy && !$request->equipeIdCreatedBy && !$request->conseillerIdCreatedBy) {
            $usersIdCreatedBy[] = SocieteUser::where('societe_id', $request->societeIdCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');

            $sitesCreatedBy = Site::where('societe_id', $request->societeIdCreatedBy)->pluck('id');
            $usersIdCreatedBy[] = SiteUser::whereIn('site_id', $sitesCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');

            $equipesCreatedBy = Equipe::whereIn('site_id', $sitesCreatedBy)->pluck('id');
            $usersIdCreatedBy[] = EquipeUser::whereIn('equipe_id', $equipesCreatedBy)
                ->where('deleted', NULL)
                ->pluck('user_id');

            $usersCreatedBy = array_collapse($usersIdCreatedBy);
        }
        
        // // Les dates
        if ($request->dateDebut) {
            $datedebut = new Carbon($request->dateDebut);
        }

        if ($request->dateFin) {
            $df = Carbon::parse($request->dateFin);
            $datefin =  $df->addHour(23)->addMinutes(59)->addSeconds(59);
        }
        
        // Type Dpp
        if ($request->dppId) {
            if ($request->dppId == 1) {
                $raison_sociale = ucfirst(strtolower($request->nom)).' '.ucfirst(strtolower($request->prenom));
            } elseif ($request->dppId == 2) {
                $raison_sociale = ucfirst(strtolower($request->raison_sociale));
            }
        }

        $data = $this->listDroits();

        $contratIds = collect($data)->pluck('id');

        $deviIds = Contrat::whereIn('id', $contratIds)
                        ->where(function($query) use ($request, $datedebut, $datefin) {
                            if ($request->num_police) {
                                $query->where('num_police', $request->num_police);
                            }

                            if ($request->has('rgpd') && $request->rgpd != 2 && $request->rgpd != null) {
                                $query->where('num_police', $request->num_police);
                                $query->where('rgpd', $request->rgpd);
                            }

                            if ($request->statutId) {
                                $query->where('statut_id', $request->statutId);
                            }

                            if ($request->typeDate) {
                                if ($request->typeDate == 1) {
                                    if ($request->dateDebut && !$request->dateFin) {
                                        $query->where('created_at', '>=' ,$datedebut);
                                    } elseif ($request->dateFin && !$request->dateDebut) {
                                        $query->where('created_at', '<=' , $datefin);
                                    } elseif ($request->dateDebut && $request->dateFin) {
                                        $query->whereBetween('created_at', [$datedebut, $datefin]);
                                    }
                                }
                            }
                        })->get()
                        ->pluck('devis_id');
                                
        $devisInfos = Devis::whereIn('id', $deviIds)
                            ->where( function($query) use ($request, $users, $usersCreatedBy, $datedebut, $datefin){
                                if($request->num_devis){
                                    $query->where('num_devis', $request->num_devis);
                                }

                                if($request->societeId || $request->siteId || $request->equipeId || $request->conseillerId){
                                    $query->whereIn('user_id', $users);
                                }

                                if ($request->societeIdCreatedBy || $request->siteIdCreatedBy || $request->equipeIdCreatedBy || $request->conseillerIdCreatedBy) {
                                    $query->whereHas('fiche', function ($q) use ($usersCreatedBy) {
                                        $q->whereIn('cree_par', $usersCreatedBy);
                                    });
                                }

                                if ($request->typeDate) {
                                    if($request->typeDate == 3){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->where('date_souscription', '>=' ,$datedebut );
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                            $query->where('date_souscription', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('date_souscription', [$datedebut , $datefin]);
                                        }
                                    }
                                    if($request->typeDate == 4){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->where('date_signature', '>=' ,$datedebut );
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                            $query->where('date_signature', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('date_signature', [$datedebut , $datefin]);
                                        }
                                    }
                                    if($request->typeDate == 6){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->where('date_validation', '>=' ,$datedebut );
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                            $query->where('date_validation', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('date_validation', [$datedebut , $datefin]);
                                        }
                                    }
                                    
                                    if($request->typeDate == 11){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->where('date_effet', '>=' ,$datedebut );
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                            $query->where('date_effet', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('date_effet', [$datedebut , $datefin]);
                                        }
                                    }
                                }
          
                            })->get();

        $ficheIds = collect($devisInfos)->pluck('num_fiche');

        //Prestataires et grp publicitaires 
        $grp_provenance =[];
        if($request->prestataire && $request->prestataire != 0){
            if($request->grpPub && $request->grpPub != 0)
                $grp_provenance =  GroupepubProvenance::where('groupepub_id',$request->grpPub)->pluck('id')->toArray();
            else
                $grp_provenance =  GroupepubProvenance::whereIn('groupepub_id',Prestataire::find($request->prestataire)->groupepubs->pluck('id'))->pluck('id')->toArray(); 
            
        }
        $fichesInfos = Fiche::whereIn('num_fiche', $ficheIds)
                            ->where( function($query) use ($request,$datedebut, $datefin, $grp_provenance){
                                if($request->num_fiche){
                                    $query->where('num_fiche', $request->num_fiche);
                                }
                                if($request->brancheId != 0){
                                    $query->where('branche_id', $request->brancheId);
                                }
                                if ($request->typeDate) {
                                    if($request->typeDate == 2){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->whereDate('date_effet', '>=' ,$datedebut );
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                             $query->whereDate('date_effet', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('date_effet', [$datedebut , $datefin]);
                                        }
                                    }
                                    if($request->typeDate == 9){
                                        if($request->dateDebut && !$request->dateFin) {
                                            $query->whereDate('created_at','>=',$datedebut);
                                        }elseif ($request->dateFin && !$request->dateDebut) {
                                            $query->whereDate('created_at', '<=' , $datefin);
                                        }elseif ($request->dateDebut && $request->dateFin) {
                                            $query->whereBetween('created_at', [$datedebut , $datefin]);
                                        }
                                    }
                                   
                                }
                              if($request->prestataire && $request->prestataire != 0 || $request->grpPub && $request->grpPub != 0) 
                                $query->whereIn('groupepub_provenance_id',$grp_provenance);
                            })->get();
        
        $tiersIds = collect($fichesInfos)->pluck('tiers_id');

        $tiers = Tiers::whereIn('id', $tiersIds)
                        ->where(function($q) use ($raison_sociale){
                            if($raison_sociale){
                                $q->where('raison_sociale', 'like', '%'.$raison_sociale.'%');
                            }
                        })->where(function($q) use ($request){
                            if($request->email){
                                $q->where('email', 'like', '%'.$request->email.'%')
                                    ->orWhere('numemail', 'like', '%'.$request->email.'%');
                            }
                        })->where(function($q) use ($request){
                            if($request->tel != null){
                                $q->where('num_tel', $request->tel)
                                    ->orWhere('num_post_tel', $request->tel)
                                    ->orWhere('portable', $request->tel);
                            }
                        })->get();
        
        $fichesTiersIds     = Fiche::whereIn('tiers_id', $tiers->pluck('id'))->pluck('id');
        $collectionFiche    = collect($fichesTiersIds);
        $intersectFiche      = $collectionFiche->intersect($fichesInfos->pluck('id'));

        $num_fiches = Fiche::whereIn('id', $intersectFiche->all())->get()->pluck('num_fiche');

        $devisFichesIds      = Devis::whereIn('num_fiche', $num_fiches)->pluck('id');
        $collectionDevis     = collect($devisFichesIds);
        $intersectDevis      = $collectionDevis->intersect($devisInfos->pluck('id'));

        $list_devis              = Devis::whereIn('id', $intersectDevis->all())->get();
       
        $devisFinal = [];

        if($request->codeCompagnie && !$request->codeGamme && !$request->garantieId){
            foreach ($list_devis as $key => $ld) {
                $formule = json_decode($ld->formule, TRUE);
                if ($formule['cieCode'] == $request->codeCompagnie) {
                    $devisFinal[] = $ld;
                }
            }
        }elseif($request->codeCompagnie && $request->codeGamme && !$request->garantieId){
            foreach ($list_devis as $key => $ld) {
                $formule = json_decode($ld->formule, TRUE);
                if ($formule['codeGamme'] == $request->codeGamme) {
                    $devisFinal[] = $ld;
                }
            }
        }elseif($request->codeCompagnie && $request->codeGamme && $request->garantieId){
            foreach ($list_devis as $key => $ld) {
                $formule = json_decode($ld->formule, TRUE);
                if ($formule['id'] == $request->garantieId) {
                    $devisFinal[] = $ld;
                }
            }
        }else{
            $devisFinal = $list_devis;
        }

        $tarif_min = $request->tarif_min ;
        $tarif_max = $request->tarif_max ;

        if($request->tarif_min && $request->tarif_max){
            $devisFinal = collect($devisFinal)->filter(function($item) use ($tarif_min, $tarif_max) {
                $formule = json_decode($item->formule, TRUE);
                return $formule['tarifTotal'] >= $tarif_min && $formule['tarifTotal'] <= $tarif_max;
            });
        }elseif ($request->tarif_min && !$request->tarif_max) {
            $devisFinal = collect($devisFinal)->filter(function($item) use ($tarif_min) {
                $formule = json_decode($item->formule, TRUE);
                return $formule['tarifTotal'] >= $tarif_min;
            });
        }elseif (!$request->tarif_min && $request->tarif_max) {
            $devisFinal = collect($devisFinal)->filter(function($item) use ($tarif_max) {
                $formule = json_decode($item->formule, TRUE);
                return $formule['tarifTotal'] <= $tarif_max;
            });
        }

        $deviIds_semi_final_list = Devis::whereIn('id', collect($devisFinal)->pluck('id'))
                                ->where(function($q) use ($users){
                                    if($users){
                                        $q->whereIn('user_id', $users);
                                    }
                                })->get()->pluck('id');

        $deviIds_final_list = $deviIds_semi_final_list;

        if ($request->typeDate) {
            if($request->typeDate == 7 ){      
                $deviIds_final_list = Statutaction::where('entitable_type', 'Devis')
                                            ->whereIn('entitable_id', $deviIds_semi_final_list)
                                            ->where('statut_id' ,21)
                                            ->where(function($query) use ($request, $datedebut, $datefin){
                                                    
                                                if($request->dateDebut && !$request->dateFin) {
                                                    $query->where('created_at', '>=' ,$datedebut );
                                                }elseif ($request->dateFin && !$request->dateDebut) {
                                                    $query->where('created_at', '<=' , $datefin);
                                                }elseif ($request->dateDebut && $request->dateFin) {
                                                    $query->whereBetween('created_at', [$datedebut , $datefin]);
                                                }
                                                        
                                            })->get()->pluck('entitable_id');
            }
            elseif($request->typeDate == 6 ){ // date validation production
                $deviIds_final_list = Statutaction::where('entitable_type', 'Devis')
                                            ->whereIn('entitable_id', $deviIds_semi_final_list)
                                            ->where('statut_id' ,13)
                                            ->where(function($query) use ($request, $datedebut, $datefin){
                                                    
                                                if($request->dateDebut && !$request->dateFin) {
                                                    $query->where('created_at', '>=' ,$datedebut );
                                                }elseif ($request->dateFin && !$request->dateDebut) {
                                                    $query->where('created_at', '<=' , $datefin);
                                                }elseif ($request->dateDebut && $request->dateFin) {
                                                    $query->whereBetween('created_at', [$datedebut , $datefin]);
                                                }
                                                        
                                            })->get()->pluck('entitable_id');
            }

        }

        $contratIds_semi_final_list = Contrat::whereIn('devis_id', $deviIds_final_list)->get()->pluck('id');

        if($request->typeDate && $request->typeDate == 8 ){      
            $contratIds_final_list = Statutaction::where('entitable_type', 'Contrat')
                                        ->whereIn('entitable_id', $contratIds_semi_final_list)
                                        ->whereIn('statut_id' ,[20, 107])
                                        ->where(function($query) use ($request, $datedebut, $datefin){
                                                
                                            if($request->dateDebut && !$request->dateFin) {
                                                $query->where('created_at', '>=' ,$datedebut );
                                            }elseif ($request->dateFin && !$request->dateDebut) {
                                                $query->where('created_at', '<=' , $datefin);
                                            }elseif ($request->dateDebut && $request->dateFin) {
                                                $query->whereBetween('created_at', [$datedebut , $datefin]);
                                            }
                                                    
                                        })->get()->pluck('entitable_id');
        }else {
            $contratIds_final_list = $contratIds_semi_final_list;
        }

        $contrat_final_list  = Contrat::whereIn('id', $contratIds_final_list)->latest()->paginate(20);
        
        $contrats = $this->listContrats($contrat_final_list);

        return $contrats;
    }

    public function getContratsTraces($contrat, $export = null)
    {
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        if (!$contrat) {
            return [];   
        }
        
        $validC = TraceMatiere::where(function ($query) use ($contrat) {
                                $query->where('entitable_id', $contrat->id)
                                    ->Where('entitable_type', 'Contrat')
                                    ->whereIn('slug', ['validation-contrat', 'validation-contrat-client', 'validation-contrat-n-police']);
                            })
                            ->orderBy('created_at', 'desc')
                            ->get();

        $validD = TraceMatiere::where(function ($query) use ($contrat){
                                    $query->where('entitable_id',$contrat->devis->id)
                                            ->Where('entitable_type','Devis')
                                            ->Where('slug', 'valider-depot-production');
                                            })->orderBy('created_at', 'desc')  
                                            ->get();
      
        $usersIDsC = $validC->pluck('user_id');
        
        $usersIDsD = $validD->pluck('user_id');

        $usersC = count($usersIDsC) ? User::whereId($usersIDsC)->first() : [];
        $usersD = count($usersIDsD) ? User::whereId($usersIDsD)->first() : [];
        
        $validC->transform(function ($data) use ($usersC) {
            $data->nom = $usersC->where('id', $data->user_id)->first()->nom;
            $data->prenom = $usersC->where('id', $data->user_id)->first()->prenom;

            return $data;
        }); 
        
        $validD->transform(function ($data) use ($usersD) {
            $data->nom = $usersD->where('id', $data->user_id)->first()->nom;
            $data->prenom = $usersD->where('id', $data->user_id)->first()->prenom;

            return $data;
        }); 

        return ['validD' => $validD, 'validC' => $validC];
    }
    
    public function getContratsExport(Request $request)
    {
        $results = $this->rechercheContrat($request)->latest()->get();
        // hydrate a model with the results from a DB call
        $contrats = Contrat::hydrate($results->all());
        $contrats = $this->listContrats($contrats, null, 1);
        
        return $contrats;
    }

    public function getContratsExportTracesDevis($contrats, $export){
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        if (!$contrats) {
            return [];   
        }

        $validD = TraceMatiere::where(function ($query) use ($contrats){
                                    $query->whereIn('entitable_id',$contrats->pluck('devis_id'))
                                            ->Where('entitable_type','Devis')
                                            ->Where('slug', 'valider-depot-production');
                                            })->orderBy('created_at', 'desc')  
                                            ->get();
        
        $usersIDsD = $validD->pluck('user_id');

        //The WhereId method expects a single Id, not an array, the following line throws exception, so it has been replaced
        //$usersD = count($usersIDsD) ? User::whereId($usersIDsD)->first() : [];
        $usersD = count($usersIDsD) ? User::whereIn('id', $usersIDsD)->get() : [];

        return $validD->transform(function ($data) use ($usersD) {
            $data->nom = $usersD->where('id', $data->user_id)->first()->nom;
            $data->prenom = $usersD->where('id', $data->user_id)->first()->prenom;

            return $data;
        }); 
    }
    
    public function getContratsExportTracesContrat($contrats, $export){
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id);

        if (!$contrats) {
            return [];   
        }

        $validC = TraceMatiere::where(function ($query) use ($contrats) {
                                $query->whereIn('entitable_id', $contrats->pluck('id'))
                                    ->Where('entitable_type', 'Contrat')
                                    ->whereIn('slug', ['validation-contrat', 'validation-contrat-client', 'validation-contrat-n-police']);
                            })
                            ->orderBy('created_at', 'desc')
                            ->get();
      
        $usersIDsC = $validC->pluck('user_id');

        //The WhereId method expects a single Id, not an array, the following line throws exception, so it has been replaced
        //$usersC = count($usersIDsC) ? User::whereId($usersIDsC)->first() : [];
        $usersC = count($usersIDsC) ? User::whereIn('id', $usersIDsC)->get() : [];

        
        return $validC->transform(function ($data) use ($usersC) {
            $data->nom = $usersC->where('id', $data->user_id)->first()->nom;
            $data->prenom = $usersC->where('id', $data->user_id)->first()->prenom;

            return $data;
        }); 

    }

    public function getContratsExportMotifs($contrats){
        $user = Auth::user();  
        FonctionController::setConfigMySql2($user->courtier_id);

        if (!$contrats) {
            return collect([]);   
        }
        $contratsIds = $contrats->pluck('id');

        $lastMotifs = Statutaction::whereIn('entitable_id', $contratsIds)
                    ->where('entitable_type', 'Contrat')
                            ->whereIn('id', function($query) use($contratsIds) {
                                $query->selectRaw('MAX(id)')
                                    ->from('statutactions')
                                    ->whereIn('entitable_id', $contratsIds)
                                    ->where('entitable_type', 'Contrat')
                                    ->groupBy('entitable_id');
                            })
                            ->with('motifs')
                            ->select('id', 'entitable_id', 'motif_id')
                            ->orderBy('created_at', 'desc')
                            ->get();

                            
        
        foreach($lastMotifs as $lm){
            if ($lm->motifs->isNotEmpty()) {
                $lm->motifsLibelle = $lm->motifs->pluck('libelle')->implode(' // ');
            } else {
                // $lm->motifsLibelle = Motif::whereIn('id',(array)json_decode($lm['motif_id']))->first() ? Motif::whereIn('id',(array)json_decode($lm['motif_id']))->pluck('libelle')->implode(' // ') : null;
                $lm->motifsLibelle = null;
            }
        }
        
        return $lastMotifs;
    }

    public function listContratsExport($contrats, $request = null, $export = null, $expC = null)
    {
        $ficheController = new FicheController;

        if($request){   
            $brancheID = $request->brancheId;
        }else{
            $brancheID = null;
        }

        $allTracesD = $this->getContratsExportTracesDevis($contrats, $export);
        $allTracesC = $this->getContratsExportTracesContrat($contrats, $export);
        $contratsMotifs = $this->getContratsExportMotifs($contrats);
        $traces = [];

        foreach ($contrats as $keyC => $contrat) {
            // reglements
            $reglSansGdf = Statut::where('slug', 'regle-sans-gdf')->first();
            $regle = Statut::where('slug', 'regle')->first();

            $reglements = Reglement::join('devis', 'devis.id', 'reglements.devis_id')
                ->where('devis.id', $contrat->devis_id)
                ->whereIn('reglements.statut_id', [$regle->id, $reglSansGdf->id]) //reglé & reglé sans gdf
                ->select(
                    DB::raw("SUM(IF(reglements.ordre=1,reglements.montant,0)) + SUM(IF(reglements.ordre != 1,reglements.montant,0)) AS montantGlobal")
                )
                ->first();
                
            if ($reglements && isset($reglements->montantGlobal)) {
                $contrat->montantGlobal = $reglements->montantGlobal;
            }

            //Nbre Reglements
            $allReglements = Reglement::where('devis_id', $contrat->devis_id)->get();
            $contrat->reglementsCount = count($allReglements);

            //reglements Etat
            $valid = true;
            foreach ($allReglements as $reglement) {
                if ($reglement->statut_id != $regle->id && $reglement->statut_id != $reglSansGdf->id) {
                    $valid = false;
                    break;
                }
            }

            $contrat->situationReglement = $valid ? 'Réglé' : 'Non Réglé';
            
            //statut
            $statut = Statut::find($contrat->statut_id);
            $contrat->statutLibelle = $statut ? $statut->libelle : '';

            //validation Dossier Animateur
            $lastValidationDossierAnim = TraceMatiere::where('slug', 'validation-dossier-animateur')
                ->where('entitable_type', 'Devis')
                ->where('entitable_id', $contrat->devis_id)
                ->latest()
                ->first();
        
            $contrat->lastValidationDossierAnim = $lastValidationDossierAnim && ($contrat->devis->dossier_complet == 3 || $contrat->devis->dossier_complet == 2) ?  Carbon::parse($lastValidationDossierAnim->created_at)->format('Y-m-d') : '';
            $contrat->validationDossierAnimComplement = $lastValidationDossierAnim && ($contrat->devis->dossier_complet == 3 || $contrat->devis->dossier_complet == 2) ?  $lastValidationDossierAnim->complement : '';
            //Etat Depos Doss Anim
            $contrat->etatValidationDossierAnim = $contrat->devis->dossier_complet == 3 || $contrat->devis->dossier_complet == 2 ? 'OUI' : 'NON';

            //validation Dossier gestionnaire
            $lastValidationDossierGest = TraceMatiere::where('slug', 'validation-dossier-gestionnaire')
                ->where('entitable_type', 'Devis')
                ->where('entitable_id', $contrat->devis_id)
                ->latest()
                ->first();
            
            $contrat->lastValidationDossierGest = $lastValidationDossierGest && $contrat->devis->dossier_complet == 3 ?  Carbon::parse($lastValidationDossierGest->created_at)->format('Y-m-d') : '';
            $contrat->validationDossierGestComplement = $lastValidationDossierGest && $contrat->devis->dossier_complet == 3 ?  $lastValidationDossierGest->complement : '';
            //Etat Depos Doss Anim
            $contrat->etatValidationDossierGest = $contrat->devis->dossier_complet == 3 ? 'OUI' : 'NON';
            
            $devis = $contrat->devis;
            
            $contrat->fiche = $this->listDetailsFicheExport($devis->num_fiche);

            $contrat->formule = json_decode($devis->formule, TRUE);

            $contrat->affecte_a = $ficheController->affectee_a($contrat->fiche['id'], $devis->user_id, $export);

            if ($expC){
                $traces['validD'] = $allTracesD->where('fiche_id', $contrat->devis->fiche->id)->first();
                $traces['validC'] = $allTracesC->where('fiche_id', $contrat->devis->fiche->id)->first();
                $contrat->traces = $traces;
            }
            if($contratsMotifs->isNotEmpty()){
                // Affect motif
                $contrat->statutAction = $contratsMotifs->where('entitable_id', $contrat->id)->first();
            }else{
                $contrat->statutAction = null;
            }
            if ($brancheID == '3' || $brancheID == '8' || $brancheID == '33' || $brancheID == '17') {
                $contrat->vehicule = $contrat->devis->vehicule2;

                $dpp = Dpps::where('tiers_id', $contrat->fiche->tiers_id)->first();
                $contrat->dpp = $dpp;
        
                if ($dpp)
                    $contrat->permis = (TypePermisDpp::where('type_id', $contrat->fiche->type_permis_id)->where('dpps_id', $dpp->id)->with(['type'])->first());  
                else
                    $contrat->permis = null;
            }
        }
        ContratResource::withoutWrapping();
        return ContratResource::collection($contrats);
    }

    public function listReglementsExport($contrats)
    {
        $reglSansGdf = Statut::where('slug', 'regle-sans-gdf')->first();
        $regle = Statut::where('slug', 'regle')->first();

        //pluck ids from contrats 
        $contratsIds = array_pluck($contrats, 'id');

        //get all reglements Réglé or Réglé sans GDF using contrats ids
        $reglements = Reglement::whereHas('devis', function ($query) use ($contratsIds, $reglSansGdf, $regle) {
            $query->whereHas('contrat', function ($query) use ($contratsIds) {
                $query->whereIn('id', $contratsIds);
            })
            ->whereIn('reglements.statut_id', [$regle->id, $reglSansGdf->id]); //reglé & reglé sans gdf
        })->get();

        foreach ($reglements as $reglement) {
            // Id Fiche
            $fiche = $reglement->devis->fiche;
            $reglement->idAssurnet = $fiche && $fiche->assurnet_id ? $reglement->devis->fiche->assurnet_id : '';

            //Mode reglement
            $modepaiement = Modepaiement::find($reglement->mode_id);
            $reglement->modepaiement = $modepaiement ? $modepaiement->libelle : '';

            //date reglement
            $dateReglement = Statutaction::where('entitable_type', 'Reglement')
                ->where('entitable_id', $reglement->id)
                ->whereIn('statut_id', [$regle->id, $reglSansGdf->id])
                ->latest()
                ->first();
            
            $reglement->dateReglement = $dateReglement ? Carbon::parse($dateReglement->created_at)->format('Y-m-d') : '';
            $reglement->dateValidation = $dateReglement ? Carbon::parse($dateReglement->created_at)->format('Y-m-d') : '';

            //date Creation reglement
            $reglement->dateCreation = $reglement->created_at ? Carbon::parse($reglement->created_at)->format('Y-m-d') : '';

            //Createur 
            $creator = User::find($reglement->devis->user_id);
            $reglement->creator = $creator ? $creator->prenom . ' ' .$creator->nom : '';

            //Situation Reglement & Etat Reglement
            if ($reglement->statut_id == $regle->id) {
                $reglement->situationReglement = $regle->libelle;
                $reglement->etatReglement = $regle->libelle;
            } elseif ($reglement->statut_id == $reglSansGdf->id) {
                $reglement->situationReglement = $reglSansGdf->libelle;
                $reglement->etatReglement = $reglSansGdf->libelle;
            }
        }
        
        return $reglements;
    }

    public function export(Request $request)
    {
        ini_set('max_execution_time', 1800); 
        ini_set('max_input_time', 1800);
        ini_set('memory_limit', '-1');

        // $contrats = $this->getContratsExport($request);
        $results = $this->rechercheContrat($request)->latest()->get();

        // Remove contrat rgpd from export
        $results = $results->reject(function($element) {
            return $element->rgpd == 1;
        });

        // hydrate a model with the results from a DB call
        $contratsHydrate = Contrat::hydrate($results->all())
                        ->load(['devis' => function ($query) {
                            $query->with('typefiche');
                            $query->with('infosBancairesPrelevement');
                            $query->with('infosBancairesRemboursement');
                        }])
                        ->load('statut');
        $contrats = $this->listContratsExport($contratsHydrate, $request, null, 1);

        $brancheID = $request->brancheId;
        if ($brancheID == '3' || $brancheID == '8' || $brancheID == '33' || $brancheID == '17') {
            //if Automobile return Zip contains both Contrats File & Reglements File

            $reglements = $this->listReglementsExport($contratsHydrate);

            // Create and prepare the files for export: 
            $contratsFile = Excel::raw(new ViewDataExport(compact('contrats', 'brancheID'), 'exports.exportContrats'), 'Xlsx');
            $reglementsFile = Excel::raw(new ViewDataExport(compact('reglements'), 'exports.exportReglements'), 'Xlsx');


            // Create a zip and add both Excel files to it
            $zip = new ZipArchive;
            $fileName = storage_path('app/contrats_reglements.zip');

            if ($zip->open($fileName, ZipArchive::CREATE) === TRUE) {
                $zip->addFromString('contrats.xlsx', $contratsFile);
                $zip->addFromString('reglements.xlsx', $reglementsFile);
                $zip->close();
            }

            // Return the zip a download
            return response()->download($fileName);
        } else { // else only return contrats file
            return Excel::download(new ViewDataExport(compact('contrats', 'brancheID'), 'exports.exportContrats'), 'contrats.xlsx');
        }
    }
    
    public function exportTraces(ContratTracesRequest $request)
    {
        $contrats = $this->getAllContratsTraces($request, true)['data'];
        return Excel::download(new ViewDataExport(compact('contrats'), 'exports.exportTraceContrats'), 'contrats.xlsx');
    }
    
    public function exportAssurnet($date_validation, Request $request)
    { 
        $results = $this->rechercheContrat($request, 1)->where('contrats.statut_id', 20)->whereBetween('contrats.date_validation',[$date_validation.' 00:00:00', $date_validation.' 23:59:59'])->latest()->get();
        //$results = $this->rechercheContrat($request, 1)->where('contrats.statut_id', 21)->whereBetween('contrats.date_validation',[$date_validation.' 00:00:00', $date_validation.' 23:59:59'])->latest()->get();
        // hydrate a model with the results from a DB call
        $contrats = Contrat::hydrate($results->all());
        $contrats = $this->listContrats($contrats, 1);
       
        $brancheID = $request->brancheId;
        return Excel::download(new ViewDataExport(compact('contrats', 'brancheID'), 'exports.exportContrats'), 'contrats.xlsx');


    }
    public function exportAssurnetNumPolice($date_validation, Request $request)
    { 
        //$results = $this->rechercheContrat($request, 1)->where('contrats.statut_id', 20)->whereBetween('contrats.date_validation',[$date_validation.' 00:00:00', $date_validation.' 23:59:59'])->latest()->get();
        $results = $this->rechercheContrat($request, 1)->whereBetween('contrats.created_at',[$date_validation.' 00:00:00', $date_validation.' 23:59:59'])->latest()->get();
        // hydrate a model with the results from a DB call
        $contrats = Contrat::hydrate($results->all());
        $contrats = $this->listContrats($contrats, 1);
       
        $brancheID = $request->brancheId;
        return Excel::download(new ViewDataExport(compact('contrats', 'brancheID'), 'exports.exportContrats'), 'contrats.xlsx');
    }

    public function exportDate(Request $request)
    {
        FonctionController::setConfigMySql2(auth::User()->courtier_id);

        
        //devis
        $devis = [];
        $datedebutV = 0 ;
        $datefinV = 0 ;
        
        if ($request->dateDebutP) {
            $datedebutV = new Carbon($request->dateDebutP);
        }
        
        if ($request->dateFinP) {
            $df = Carbon::parse($request->dateFinP);
            $datefinV =  $df->addHour(23)->addMinutes(59)->addSeconds(59);
        }
        
        $devis = Devis::where(function ($query) use ($request, $datedebutV , $datefinV) {
            
            if ($datedebutV && $datefinV) {
                $query->whereBetween('date_validation', [$datedebutV, $datefinV]);
            } elseif ($datedebutV) {
                $query->where('date_validation', '>=' , $datedebutV);
            } elseif ($datefinV) {
                $query->where('date_validation', '<=' , $datefinV);
            }
        })->pluck('id');

        $results = $this->rechercheContrat($request)
                        ->whereIn('contrats.statut_id', [20, 22, 23, 58, 57, 92])
                        ->whereIn('contrats.devis_id', $devis)
                        ->latest()
                        ->get();
        // hydrate a model with the results from a DB call
        $contrats = Contrat::hydrate($results->all())
                        ->load(['devis' => function ($query) use ($request) {
                            //$query->whereBetween('devis.date_validation',[$request->date_debut_p.' 00:00:00', $request->date_fin_p.' 23:59:59']);
                            $query->with('typefiche');
                            $query->with('infosBancairesPrelevement');
                            $query->with('infosBancairesRemboursement');
                        }])
                        ->load('statut');
        $contrats = $this->listContratsExport($contrats, $request, null, 1);

        return Excel::download(new ViewDataExport(compact('contrats'), 'exports.exportContratsAssurnet'), 'contrats.xlsx');
    }

  
    public function changerStatutContrat(Request $request)
    {
        $sendemail = new ConfigMailingController();
        $devisController = new DevisController();

        $user = Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id); 
       
        $contrat = Contrat::findByHash($request->contrat_hash);
        $devis = Devis::find($contrat->devis_id);
        $devisFiche = Devis::where('num_fiche', $devis->num_fiche)->get();
        
        if ($contrat->statut_id == 21 || $contrat->statut_id == 75) {
            if ($request->action == 1) {
                if ($contrat->statut_id == 21) {
                    $request->validate([
                        'num_police' => 'required',
                    ]);
                    $existingPolice = Contrat::where('num_police', $request->num_police)->exists();
                    if ($existingPolice) {
                        return response()->json(['error' => 'This police number already exists in our records.'], 409);
                    }
                    $contrat->statut_id = 20;
                    $contrat->num_police = $request->num_police;
                    $contrat->date_validation = Carbon::now();
                    $contrat->save();

                    $model = ModelMail::where('nom', 'Validation de contrat')->where('branche_id', $contrat->devis->fiche->branche_id)->first();
                    $emails = [];
                    
                    if ($model) {
                        $request['id'] = $model->id;
                        $emails = $model->getModelProfilsEmails($devis);
                    }
                        
                    foreach ($emails as $email) {
                        $request['tier'] = $email;
                        $sendemail->envoi($request);
                    }

                    $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();

                    $sendemail1 = new ConfigMailingController();
                    $model1 = ModelMail::where('nom', 'Validation service gestion')->first();
                    $request1 = new Request();
                    $request1->setMethod('POST');

                    $emails = [];

                    if ($model1) {
                        $emails = $model1->getModelProfilsEmails($devis);
                    }

                    foreach ($emails as $email) {
                        $request1->replace([
                            'contrat_hash' => $request['contrat_hash'],
                            'id' => $model1 ? $model1->id : '',
                            'num_police' => $request['num_police'],
                            'commentaire' => $request['commentaire'],
                            'tier' => $email
                        ]);
                        
                        $sendemail1->envoi($request1);
                    }
                }
                //Validation du contrat en attente de saisie
                elseif($contrat->statut_id == 75){
                    $grpBranchsSante = json_decode(Groupebranche::find(1)->branches);
                    $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();
                    $contrat->statut_id = 21;
                    $contrat->save();
                    
                    $contratImpaye = Fiche::where('contrat_impaye_id', $contrat->id)->first();

                    if ($fiche && in_array($fiche->branche_id, $grpBranchsSante) && !$contratImpaye) {
                        //envoi de mail au client
                        $sendemail1 = new ConfigMailingController();
                        $model1   = ModelMail::where('nom','Coordonnees du service clients')->first();
                        if ($model1) {
                            $emails = [];
                            $emails = $model1->getModelProfilsEmails($devis);
                           
                            $request['id'] = $model1->id;

                            foreach ($emails as $email) {
                                $request['tier'] = $email;
                                $sendemail1->envoi($request);
                            }
                        }
                    }
                }
               

                
            } else if ($request->action == 2) {
                DB::beginTransaction();

            try {
                $request->validate([
                    'dateChute' => 'required',
                ]);

                //Reprise
                $this->calculContratReprise($contrat, $request->dateChute);

                $contrat->date_chute = $request->dateChute;
                $contrat->statut_id = 34; 
                $devis->statut_id = 19;

                $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();
                $fiche->statut_id = 30;
                $fiche->save();

                $statusActionFiche = new Statutaction;
                $statusActionFiche->user_id = Auth::user()->id;
                $statusActionFiche->statut_id = $fiche->statut_id;
                $statusActionFiche->entitable_id = $fiche->id;
                $statusActionFiche->entitable_type = "Fiche"; 
                $statusActionFiche->save();
                $devis->save();

                $devisController->recalculerCadSante($devis);

                $lettres = DevisLR::where('devis_id', $devis->id)->get();

                foreach ($lettres as $key => $lettre) {
                    $lettre->active = 0;
                    $lettre->save();
                }
                DB::commit();
            } catch (ValidationException $e) {
                DB::rollback();
                throw $e;
            }
            }

            $tiers_id = Fiche::whereNumFiche(Devis::whereId($contrat->devis_id)->first()->num_fiche)->first()->tiers_id;
            $client = Client::whereTiersId($tiers_id)->first();
             
            if ($client) {
                $client->nbr_contrat++;
            } else {
                $client = new Client();
                $client->tiers_id = $tiers_id;
                $client->nbr_contrat = 1;
            }

            $client->save(); 
            $contrat->client_id = $client->id;
        } else if ($request->action == 3) {     // Validation remise en vigeur
            DB::beginTransaction();

            try {
            $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first(); 
            if ($contrat->statut_id == 22 || $contrat->statut_id == 23 || $contrat->statut_id == 57 || $contrat->statut_id == 58 || $contrat->statut_id == 59 || $contrat->statut_id == 40 || $contrat->statut_id == 34 || $contrat->statut_id == 107) {
                $lastStatut = Statutaction::where('entitable_id', $contrat->id)
                                    ->where('entitable_type', 'Contrat')
                                    ->whereActive(1)
                                    ->whereNotIn('statut_id',[22,23,57,34,58,59,40,107])
                                    ->latest()
                                    ->take(1)
                                    ->first();
                if ($contrat->statut_id == 34 && $lastStatut && $lastStatut->statut_id == 20 ) {
                    $contrat->statut_id = 21;
                    $contrat->date_revalidation = Carbon::now();
                }
                elseif($contrat->statut_id == 34)  $contrat->date_revalidation = Carbon::now();
                $contrat->statut_id = $lastStatut ? $lastStatut->statut_id : 75;
                if($contrat->statut_id == 20)
                  $contrat->date_validation = Carbon::now();
            }

            // Recalcul Commission Cabinet remise en vigeur
            $commissions = $contrat->commissionCabinet()->withTrashed()->get();
            if (!$commissions->isEmpty()) {
                $devisController->recalculCommissionCabinet($request, $contrat->id, false, $contrat->statut_id, $request->keepCommission); //remise en vigeur
            }
                
            $devis->statut_id = 26;
            $fiche->statut_id = 25;
            $fiche->save();

            
            // If contrat remise en vigeur is impayé => Fermer fiche source impayé
            // Check fiche impayé existe for this contrat => remise all fiche
            $ficheImpaye = Fiche::where('contrat_impaye_id', $contrat->id)
                            ->whereNotNull('contrat_impaye_id')
                            ->latest()
                            ->first();

            if($ficheImpaye){
                $statutRemise = Statut::where('slug', 'remise-en-vigueur')->first();
                $ficheImpaye->statut_id = $statutRemise->id;
                $ficheImpaye->save();

                // trace changement statut fiche impayé
                if($request->type == 'remise-vigueur-contrat-prt') {
                    $traceC = 'remise-vigueur-contrat-prt';
                    $titre = 'Remise en vigueur du contrat injection compagnie';  
                }else{
                    $traceC = 'remise-en-vigeur-contrat';
                    $titre = 'Remise en vigueur du contrat';  
                }

                $tabInfoTraceMatiere = [  
                    'courtier_id'        => $user->courtier_id, 
                    'fiche_id'           => $ficheImpaye->id,
                    'user_id'            => $user->id,
                    'userable_id'        => $ficheImpaye->userable_id,
                    'userable_type'      => $ficheImpaye->userable_type,
                    'entitable_id'       => $ficheImpaye->id,
                    'entitable_type'     => 'Fiche',
                    'dispatchable_id'    => $ficheImpaye->dispatchable_id,
                    'dispatchable_type'  => $ficheImpaye->dispatchable_type,
                    'statut_id'          => $ficheImpaye->statut_id,
                    'slug'               => $traceC,
                    'commentaire'        => $titre.' : '.$statutRemise->libelle,
                    'complement'         => $request->commentaire,
                    'active'             => 1, 
                ]; 
        
                event(new EventTraceMatieres($tabInfoTraceMatiere));

                $statusActionFiche = new Statutaction;
                $statusActionFiche->user_id = Auth::user()->id;
                $statusActionFiche->statut_id = $ficheImpaye->statut_id;
                $statusActionFiche->entitable_id = $ficheImpaye->id;
                $statusActionFiche->entitable_type = "Fiche"; 
                $statusActionFiche->save();
            }

                            
            $statusActionFiche = new Statutaction;
            $statusActionFiche->user_id = Auth::user()->id;
            $statusActionFiche->statut_id = $fiche->statut_id;
            $statusActionFiche->entitable_id = $fiche->id;
            $statusActionFiche->entitable_type = "Fiche"; 
            $statusActionFiche->save();

            $devis->save();

            $devisController->recalculerCadSante($devis);
            $lettres = DevisLR::where('devis_id', $devis->id)->get();
                
            foreach ($lettres as $key => $lettre) {
                $lettre->active = 1;
                $lettre->statut_id = 31;
                $lettre->save();
            } 
                DB::commit();
            } catch (ValidationException $e) {
                DB::rollback();
                throw $e;
            }
        }else if ($request->action == 33) {     // Annulation remise en vigeur
            $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first(); 
            
            $lastStatutContrat = Statutaction::where('entitable_id', $contrat->id)
                                    ->where('entitable_type', 'Contrat')
                                    ->whereActive(1)
                                    ->where('statut_id', '!=', 57)
                                    ->latest()
                                    ->take(1)
                                    ->first();
            
            $contrat->statut_id = $lastStatutContrat ? $lastStatutContrat->statut_id : 57;    // No last statuts found => keep Attente remise en vigueur
            $contrat->save();


            $model   = ModelMail::where('nom','Annulation remise en vigueur de contrat')->first();
            if($model) {
                $emails = [];
                $emails = $model->getModelProfilsEmails($devis, 2);
                $request['id'] = $model->id;
                
                foreach ($emails as $email) {
                    $request['tier'] = $email;
                    $sendemail->envoi($request);
                }
            }

        } else if ($contrat->statut_id == 34 && $request->action == 4) {
            $contrat->statut_id = 40;
            $devis->statut_id = 41;
            $devis->save();
            $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();
            $fiche->statut_id = 2;
            $fiche->save();
                
            $statusActionFiche = new Statutaction;
            $statusActionFiche->user_id = Auth::user()->id;
            $statusActionFiche->statut_id = $fiche->statut_id;
            $statusActionFiche->entitable_id = $fiche->id;
            $statusActionFiche->entitable_type = "Fiche"; 
            $statusActionFiche->save();
        }else if ($request->action == 5) {//Dépot en remise en vigeur

            $contrat->statut_id = 57; //Attente remise en vigueur
            $model   = ModelMail::where('nom','Demande de remise en vigueur de contrat')->first();
            //dd($model);
            if($model) {
                $emails = [];
                $emails = $model->getModelProfilsEmails($devis, 2);
                $request['id'] = $model->id;
                
                foreach ($emails as $email) {
                    $request['tier'] = $email;
                    $sendemail->envoi($request);
                }
            }
            
        }

         //mettre contrat comme contentieux

        else if($request->action == 6 && $contrat->statut_id == 20){

            $contrat->statut_id = 92;

        }

         //régler la situation du  contrat  contentieux

        else if($request->action == 7 && $contrat->statut_id == 92){

            $contrat->statut_id = 20;

        }

        $contrat->save();

        $statutAction = new Statutaction();

        if ($request->motifId) {
            $motif = (gettype($request->motifId) == 'array') ? $request->motifId : [$request->motifId];
        }
        
        $statutAction->entitable_id = $contrat->id;
        $statutAction->entitable_type = 'Contrat';
        $statutAction->user_id = $user->id;
        $statutAction->statut_id = $contrat->statut_id;
        $statutAction->motif_id = ($request->motifId) ? json_encode($motif) : NULL; // keeping this temporarily for support
        $statutAction->complement = ($request->commentaire) ? $request->commentaire : NULL;

        $statutAction->save();

        // insert in the pivot table (motif_statutaction)
        if (!empty($request->motifId)) {
            $motifIds = is_array($request->motifId) ? $request->motifId : [$request->motifId];
        
            $statutAction->motifs()->syncWithoutDetaching($motifIds); 
        }

        $statut = Statut::whereId($contrat->statut_id)->first();
            
        if (preg_match_all('/\b(\w)/', strtoupper($statut->libelle), $m)) {
            $abr = implode('',$m[1]); // $v is now SOQTU
        }

        $statut->abr = $abr;
        $contrat->statut = $statut;

        foreach ($devisFiche as $key => $value) {
            $d = Devis::find($value->id);
            if ($request->action == 4 && !in_array($d->statut_id, [19,26,27,38,41,43]))
                $d->etat = 1;
            else $d->etat = 0;
            $d->save();
        }

        $fiche = Fiche::whereNumFiche(Devis::find($contrat->devis_id)->num_fiche)->first();
        
        if ($request->action == 1) {
            if($contrat->statut_id == 20) {
                $traceC = 'validation-contrat-client';
                $titre = 'Validation du contrat';
            }
            if($contrat->statut_id == 21) {
                $traceC = 'validation-contrat-n-police';
                $titre = 'Validation du contrat';  
            }
        } else if ($request->action == 2) {
            $traceC ='rejeter-contrat';
            $titre =' Rejet du contrat';
            //Envoyer emails
            $motifs = "";

            foreach ($request->motifId as $key => $value) {

                    $motifs = count($request->motifId) > 1 ? $motifs.Motif::find($value)->libelle.' ,' :  $motifs.Motif::find($value)->libelle;
                }
            

              
        } else if ($request->action == 3) {
            if($request->type == 'remise-vigueur-contrat-prt') {
                $traceC = 'remise-vigueur-contrat-prt';
                $titre = 'Remise en vigueur du contrat injection compagnie';  
            }else{
            $traceC = 'remise-en-vigeur-contrat';
            $titre = 'Remise en vigueur du contrat';  
            }
            
        }else if ($request->action == 33) {
            $traceC = 'annulation-remise-en-vigeur-contrat';
            $titre = 'Annulation remise en vigueur de contrat';  
            
        } else if ($request->action == 4) {
            $traceC = 'confirmer-rejet-compagnie';
            $titre = 'Confirmation du rejet compagnie du contrat';  
        }else if ($request->action == 5) {
            $traceC = 'deposer-contrat-en-remise-en-vigeur';
            $titre = 'Déposer en remise en vigueur du contrat';  
        }else if ($request->action == 6) {
            $traceC = 'contentieux-contrat';
            $titre = 'Mettre contrat comme contentieux';  
        }

        else if ($request->action == 7) {
            $traceC = 'regler-contrat-contentieux';
            $titre = 'Régler contrat contentieux';  
        }

        $tabInfoTraceMatiere = [  
            'courtier_id'        => $user->courtier_id, 
            'fiche_id'           => $fiche->id,
            'user_id'            => $user->id,
            'userable_id'        => $fiche->userable_id,
            'userable_type'      => $fiche->userable_type,
            'entitable_id'       => $contrat->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $fiche->dispatchable_id,
            'dispatchable_type'  => $fiche->dispatchable_type,
            'statut_id'          => $contrat->statut_id,
            'slug'               => $traceC,
            'commentaire'        => $titre.' : '.$statut->libelle,
            'complement'         => $request->commentaire,
            'active'             => 1, 
        ]; 

        event(new EventTraceMatieres($tabInfoTraceMatiere));

        $tiers = Tiers::find($devis->tiers_id);
        $tiers = new TiersResource($tiers); 

        $link = '/tiers/'.collect($tiers)['hash_id']. '/contrat/'.collect(new ContratResource($contrat))['hash_id'];
        $entity = ['nom' => $titre, 'object' => '', 'message' => '', 'link'=> $link];

        $user_id = Devis::find($contrat->devis_id)->user_id;

        $users_id[] = collect($user_id);
        
        /** @var User $devisUser */
        $devisUser = User::with('profil')->find($user_id);
        
        $animateur = [];

        if (($devisUser->isConseillerOrCommercial()) && $request->action != 3) {
            $equipeId = EquipeUser::whereUserId($user_id)->whereDeleted(NULL)->pluck('equipe_id');
            $equipeUsers = EquipeUser::whereIn('equipe_id', $equipeId)->where('deleted', NULL)->pluck('user_id');

            $animateur = User::whereIn('id', $equipeUsers)->whereProfilId(6)->get()->pluck('id');

        }
        if($request->action == 3 || $request->action == 2){
            if($request->action == 3) {
                $devisC = new DevisController();
               $gestionnaire = $devisC->getAllowedGestionnaires(User::find($user_id));
               $model   = ModelMail::where('nom','Remise en vigueur de contrat')->first();
            }
            else $model   = ModelMail::where('nom','rejet contrat')->first();

            $emails = [];
            if ($model) {
                $request['id'] = $model->id;
                $emails = $model->getModelProfilsEmails($devis, 2);
            }
            
            $sendemail = new ConfigMailingController();
            
            foreach ($emails as $email) {
                $request['tier'] = $email;
                
                if ($request->motifId && isset($motifs)) {
                    $request['motif'] = $motifs;
                }

                $sendemail->envoi($request);
            }
        }

        $users_id[] = $request->action == 3?$gestionnaire:$animateur;

        $notificationSlug = $request->action == 1 || $request->action == 3 ? 'CONTRATVAL' : 'CONTRATREJ';
        
        event(new EventNotificationUsers(
            $user_id,
            $contrat->id,
            Auth::user()->courtier_id,
            $notificationSlug,
            'alert',
            'Contrat',
            'Changement statut contrat de client '.$tiers['raison_sociale'].' de la fiche : '. $devis->num_fiche.' à '. $statut->libelle,
            $titre. $devis->num_fiche,
            $link
        ));
        
        return $contrat;
    }

    public function nbrContratGroupedByBranche($tiersId, $export = null)
    {
        $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);
        // $tiersId = Contrat::find($contratId)->devis->tiers_id;

        $fiches = Fiche::where('tiers_id', $tiersId)->get();
        // $nbrDevis  = Devis::whereIn('num_fiche', $numFiches)->get()->pluck('id');
        $contrats = [];
        foreach ($fiches as $key => $fiche) {
            $contrat = Contrat::whereIn('devis_id', Devis::whereNumFiche($fiche->num_fiche)->get()->pluck('id'))->first();
            if($contrat){
                $contrat->branche_id = $fiche->branche_id;
                $contrats[] = $contrat;
            }
        }

        $branchesCount = collect($contrats)->groupBy('branche_id');

        $compteurs = ['id' => [], 'nbr' => []];
        
        foreach ($branchesCount as $branche) {
            $compteurs['id'][] = $branche[0]->branche_id;
            $compteurs['nbr'][] = count($branche);     
        }
        
        
        

        $cpts = [];
        for ($i=0; $i <count($compteurs['id']); $i++) { 
            $branche = Branche::find($compteurs['id'][$i]);
            
            $cpts[$i]['libelle'] = $branche->libelle;
            $cpts[$i]['couleur'] = $branche->couleur;
            $cpts[$i]['icon'] = $branche->icon;
            $cpts[$i]['nbr'] = $compteurs['nbr'][$i];
        }

        return $cpts;
    }

    public function testDroitsFiche($fiche, $export = null)
    {

        if ($fiche->user_id) {
            
            /** @var User $user */  
            $user = $export ? User::where('profil_id', Profil::RESPONSABLE_SOCIETE)->first() : Auth::user();

            FonctionController::setConfigMySql2($user->courtier_id); 

            $t = new TiersController;

            // Récuperation des utilisateurs Ids

            if ($user->isResponsableSociete() || $user->isResponsableSite() || $user->isAnimateur()) {
                $userIds = array_collapse($t->listUsers($user->id));
            }

            elseif ($user->isResponsableCourtier() || $user->isStandard()) {
                $userIds = User::where('courtier_id', $user->courtier_id)->pluck('id')->all();                
            }

            elseif ($user->isConseillerOrCommercial() || $user->isAdminOrSuperAdminPrdv()) {
                $userIds = array($user->id);
            } 

            elseif ($user->isGestionnaire()) {
                $societeIds     = SocieteUser::where('user_id', $user->id)->where('deleted', NULL)->whereActive(1)->get()->pluck('societe_id');
                $usersId[]      = SocieteUser::whereIn('societe_id', $societeIds)->where('user_id', '!=' ,$user->id)->where('deleted', NULL)->whereActive(1)->get()->pluck('user_id');

                $siteIds        = Site::whereIn('societe_id', $societeIds)->whereActive(1)->get()->pluck('id');
                $usersId[]      = SiteUser::whereIn('site_id', $siteIds)->where('deleted', NULL)->whereActive(1)->get()->pluck('user_id');

                $equipeIds      = Equipe::whereIn('site_id', $siteIds)->whereActive(1)->get()->pluck('id');
                $usersId[]      = EquipeUser::whereIn('equipe_id', $equipeIds)->where('deleted', NULL)->whereActive(1)->get()->pluck('user_id');

                $collection = collect(array_collapse($usersId));

                $unique = $collection->unique();

                $userIds = $unique->values()->all();
            } else {
                return 0;
            }

            // Eliminer l'id du prestataire
            for ($i = 0; $i<count($userIds); $i++) {
                if ($userIds[$i] == 2) { 
                    // unset($userIds[$i]); 
                    $usersId = collect($userIds);
                    $a = $usersId->splice($i, 1);
                }
            }
            
            $fiche->traitement_user =  in_array($fiche->user_id, $userIds) ? true : false;
        }else{
            $fiche->traitement_user = false;
        }
        return $fiche;
    }
  public function ReafectationContrat(Request $request)
    {
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id); 
        $contratsData = Contrat::whereIn('contrats.id',$request->contrats);
        $usersId = Contrat::join('devis','devis.id','contrats.devis_id')
                          ->whereIn('contrats.id',$request->contrats)
                          ->select('devis.user_id')->pluck('user_id');

        $ancienUsers =  User::whereIn('id',$usersId)->get();
        $newuser = User::where('id',$request->userId)->first();
         
        $contrats = $contratsData->with(['devis' => function ($query) {
                                    $query->with('fiche');
                                    $query->with('typefiche');
                                    $query->with('modePaiement');
                                    $query->with('typePaiement');
                                    $query->with('infosBancairesPrelevement');
                                    $query->with('infosBancairesRemboursement');
                                    $query->with(['devisLR' => function ($q) {
                                        $q->with('statut');
                                        $q->with('motif');
                                        $q->whereActive(1);
                                    }]);
                                }])
                                ->with(['client' => function ($query) {
                                    $query->with('tiers');
                                }])
                                ->with('statut')
                                ->get();


        foreach ($contrats as $key => $value) {
            $oldUser =  $ancienUsers->where('id',$value->devis->user_id)->first();
            $value->devis->user_id = $request->userId;

            $entite = $newuser->entite($value->devis->date_validation);
            $value->devis->entitable_id = $entite['entitable_id'];
            $value->devis->entitable_type = $entite['entitable_type'];

            $value->devis->save();
            $tabInfoTraceMatiere = [  
                'courtier_id'        => $user->courtier_id, 
                'fiche_id'           => $value->devis->fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $value->devis->fiche->userable_id,
                'userable_type'      => $value->devis->fiche->userable_type,
                'entitable_id'       => $value->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $value->devis->fiche->dispatchable_id,
                'dispatchable_type'  => $value->devis->fiche->dispatchable_type,
                'statut_id'          => $value->statut_id,
                'slug'               => 'reaffectation-contrat',
                'commentaire'        => 'Réaffectation contrat du '.$oldUser->nom.' '.$oldUser->prenom.' à '.$newuser->nom.' '.$newuser->prenom,
                'complement'         => $request->comment ? $request->comment :'',
                'active'             => 1, 
        ]; 
        event(new EventTraceMatieres($tabInfoTraceMatiere));
        }
        return $data = [
            'title' => "Succès!", 
            'text' => "Contrat réaffecté avec succès", 
            'type' => "success",
            'etat' => true,
            'contrats' => $this->listContratss($contrats),
        ];
    }

    //Annulation de contrat
    public function FinContrat(Request $request)
    {
        DB::beginTransaction();

        try {
            $user = Auth::user();
            $request->validate([
                'dateFinContrat' => 'required',       
            ]);
            FonctionController::setConfigMySql2($user->courtier_id);
            $slug = '';
            $commentaire = '';
            $contrat = Contrat::findByHash($request->contrat_hash);
            $devis = Devis::where('id', $contrat->devis_id)->with('fiche')->first();
            //$devisFiche = Devis::where('num_fiche', $devis->num_fiche)->get();

            // Update fiche statut to annulation_contrats and remove old line => statut = appele
            $statutFinContrat = Statut::where('slug', 'fin-garantie-contrat')->first();
            $statutFinFiche = Statut::where('slug', 'fin-garantie-fiche')->first();
            $devis->fiche->statut_id = $statutFinFiche->id;
            $devis->fiche->save();
        
            $contrat->statut_id = $statutFinContrat->id;
            $dateFin = new DateTime($request->dateFinContrat);
            $slug = 'fin-garantie-contrat';
            $commentaire ='Fin contrat , la date de fin est : '.$dateFin->format('d/m/Y');

             
            $contrat->date_fin_garantie = $request->dateFinContrat;
            $contrat->save();

            

            $tabInfoTraceMatiere = [  
                'courtier_id'        => $user->courtier_id, 
                'fiche_id'           => $devis->fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $devis->fiche->userable_id,
                'userable_type'      => $devis->fiche->userable_type,
                'entitable_id'       => $contrat->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $devis->fiche->dispatchable_id,
                'dispatchable_type'  => $devis->fiche->dispatchable_type,
                'statut_id'          => $contrat->statut_id,
                'slug'               => $slug,
                'action'             => 'Fin Contrat ',
                'commentaire'        => $commentaire,
                'complement'         => $request->commentaire,
                'active'             => 1, 
            ];

            event(new EventTraceMatieres($tabInfoTraceMatiere));
            $statutAction = new Statutaction();

            $statutAction->entitable_id     = $contrat->id;
            $statutAction->entitable_type   = 'Contrat';
            $statutAction->user_id          = $user->id;
            $statutAction->statut_id        = $contrat->statut_id;
            $statutAction->motif_id         = ($request->motifId) ? json_encode($request->motifId) : NULL; // keep for backward compatibility
            $statutAction->complement       = ($request->commentaire) ? $request->commentaire : NULL;

            $statutAction->save();

            // insert in the pivot table (motif_statutaction)
            if (!empty($request->motifId)) {
                $motifIds = is_array($request->motifId) ? $request->motifId : [$request->motifId];
            
                $statutAction->motifs()->syncWithoutDetaching($motifIds); 
            }
            
            return $statutFinContrat;
        } catch (ValidationException $e) {
            DB::rollback();
            throw $e;
        }
    }

    //Annulation de contrat
        public function AnnulerContrat(Request $request)
    {
    DB::beginTransaction();

    try {
        $user = Auth::user();
        $request->validate([
            'dateAnnulation' => 'required',   
            'motifId' => 'required|array',
            'motifId.*' => ['integer'],    
        ]);
        FonctionController::setConfigMySql2($user->courtier_id);
        $slug = '';
        $commentaire ='';
        $contrat = Contrat::findByHash($request->contrat_hash);
        $devis = Devis::where('id', $contrat->devis_id)->with('fiche')->first();
        $devisFiche = Devis::where('num_fiche', $devis->num_fiche)->get();

        // Update fiche statut to annulation_contrats and remove old line => statut = appele
        $statutAnnulationContratID = Statut::where('slug', 'annulation-contrat-fiche')->value('id');
        $devis->fiche->statut_id = $statutAnnulationContratID;
        $devis->fiche->save();

        //Reprise
        $this->calculContratReprise($contrat, $request->dateAnnulation);

        // Format date annulation
        $dateAnnulationHist = Carbon::parse($request->dateAnnulation)->format('d/m/Y');

        if($request->action == 1 ) {
            $contrat->statut_id = 23;
            // $slug = 'annulation-contrat';
            // $commentaire ='Annulation contrat';
            // Interface annulation injection compagnie

            if($request->type == 'annulation-portefeuille-compagnie') {
                $slug = $request->slug;
                $commentaire = $request->commentaire_org;
            }else{
                $slug = 'annulation-contrat';
                $commentaire ="$dateAnnulationHist / Annulation contrat";
            }
        }
        else if($request->action == 2) {
            $contrat->statut_id = 58;
            $slug = 'retractation-contrat';
            $commentaire ="$dateAnnulationHist / Rétractation contrat";

        } 
        else if($request->action == 3) {
            $contrat->statut_id = 59;
            $slug = 'report-effet-contrat';
            $commentaire ="$dateAnnulationHist -- Sans effet / report effet";

        } 
        $contrat->date_annulation = $request->dateAnnulation;
        $contrat->save();

        $devis->statut_id = 38;
        $devis->etat = 0;
        $devis->active = 0;
        $devis->save();

        $devisController = new DevisController();
        $devisController->recalculerCadSante($devis);

        $motifs = "";
        foreach ($devisFiche as $key => $value) {
            $d = Devis::find($value->id);
            if (!in_array($d->statut_id, [19,26,27,38,41,43]))
                $d->etat = 1;
            else $d->etat = 0;
            $d->save();
        }

        foreach ($request->motifId as $key => $value) {

            $motifs = count($request->motifId) > 1 ? $motifs.Motif::find($value)->libelle.' ,' :  $motifs.Motif::find($value)->libelle;
        }
        

        $tabInfoTraceMatiere = [  
            'courtier_id'        => $user->courtier_id, 
            'fiche_id'           => $devis->fiche->id,
            'user_id'            => $user->id,
            'userable_id'        => $devis->fiche->userable_id,
            'userable_type'      => $devis->fiche->userable_type,
            'entitable_id'       => $contrat->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $devis->fiche->dispatchable_id,
            'dispatchable_type'  => $devis->fiche->dispatchable_type,
            'statut_id'          => $contrat->statut_id,
            'slug'               => $slug,
            'commentaire'        => $commentaire,
            'complement'         => $request->commentaire,
            'active'             => 1, 
        ];

        event(new EventTraceMatieres($tabInfoTraceMatiere));
        //Envoyer emails
        if($request->action == 1) $model   = ModelMail::where('nom','Annulation de contrat')->first();
        else if($request->action == 2) $model   = ModelMail::where('nom','Rétractation de contrat')->first();
        else if($request->action == 3) $model   = ModelMail::where('nom','Sans effet /report effet')->first();

        $emails = [];
        if($model) {
            $request['id'] = $model->id;
            $emails = $model->getModelProfilsEmails($devis);
        }
        
        
      /*  $serviceValidation = Service::whereRaw("JSON_CONTAINS(type , '2')")->get()->toArray();

        $emetteurs = array_merge($MembresEquipe,$serviceValidation);
        */
        $sendemail = new ConfigMailingController();
        //animateurs
        foreach ($emails as $email) {
            $request['tier'] = $email;
            if($motifs) $request['motif'] = $motifs;
            //dd($request,$membre);
            $sendemail->envoi($request);
        }

        $statutAction = new Statutaction();

         if ($request->motifId) 
            $motif = (gettype($request->motifId) == 'array') ? $request->motifId : [$request->motifId];
        //     $motifRetractation = Motif::where('slug','retractation')
        //                           ->where('groupemotif_id',4)
        //                           ->first();
        //     if($motifRetractation) {
        //         if(in_array($motifRetractation->id,$motif)){
        //             //Annulation LR
        //             $lettres = DevisLR::where('devis_id', $devis->id)->get();

        //             foreach ($lettres as $key => $lettre) {
        //                 $lettre->active = 0;
        //                 $lettre->save();
        //             }  

        //         }
        //     }
        // }
        //Désactivation LR
        $lettres = DevisLR::where('devis_id', $devis->id)->get();
                    foreach ($lettres as $key => $lettre) {
                        $lettre->active = 0;
                        $lettre->save();
                    }  
        
        $statutAction->entitable_id     = $contrat->id;
        $statutAction->entitable_type   = 'Contrat';
        $statutAction->user_id          = $user->id;
        $statutAction->statut_id        = $contrat->statut_id;
        $statutAction->motif_id         = ($request->motifId) ? json_encode($motif) : NULL; // keep for backward compatibility
        $statutAction->complement       = ($request->commentaire) ? $request->commentaire : NULL;

        $statutAction->save();

        // insert in the pivot table (motif_statutaction)
        if (!empty($request->motifId)) {
            $motifIds = is_array($request->motifId) ? $request->motifId : [$request->motifId];
        
            $statutAction->motifs()->syncWithoutDetaching($motifIds); 
        }
        
        $contrat->statut = Statut::where('id', $contrat->statut_id)->first();
        
        if (preg_match_all('/\b(\w)/', strtoupper($contrat->statut->libelle), $m)) {
            $abr = implode('',$m[1]); // $v is now SOQTU
        }

        $contrat->statut->abr = $abr;

        DB::commit();

        //Notification
        $tiers = Tiers::find($devis->tiers_id);
        $tiers = new TiersResource($tiers); 
        $link = '/tiers/'.collect($tiers)['hash_id']. '/contrat/'.collect(new ContratResource($contrat))['hash_id'];
        $user_id = Devis::find($contrat->devis_id)->user_id;

        $titre = '';
        $notificationSlug = '';

        if ($request->action == 1) {
            $titre = 'Annulation du contrat';
            $notificationSlug = 'CONTRATANNU'; 
        } else if ($request->action == 2) {
            $titre = 'Rétractation du contrat';
            $notificationSlug = 'CONTRATRET';
        } else if ($request->action == 3) {
            $titre = 'Contrat sans effet';
            $notificationSlug = 'CONTRATREPEFF';

        } 
        
        event(new EventNotificationUsers(
            $user_id,
            $contrat->id,
            Auth::user()->courtier_id,
            $notificationSlug,
            'alert',
            'Contrat',
            'Changement statut contrat de client '.$tiers['raison_sociale'].' de la fiche : '. $devis->num_fiche.' à '. $contrat->statut->libelle,
            $titre . ' : ' . $devis->num_fiche,
            $link
        ));
        
        return $contrat;  
    } catch (ValidationException $e) {
        DB::rollback();
        throw $e;
    }
    }

    // Résiliation de contrat
    public function ResilierContrat(Request $request)
    {
        DB::beginTransaction();

        try {
            $user = Auth::user();
            FonctionController::setConfigMySql2($user->courtier_id);
            $request->validate([
                'dateResiliation' => 'required',       
                'motifId' => 'required|array',
                'motifId.*' => ['integer'],
            ]);

            $contrat = Contrat::findByHash($request->contrat_hash);
            $devis = Devis::where('id', $contrat->devis_id)->with('fiche')->first();
            $devisFiche = Devis::where('num_fiche', $devis->num_fiche)->get();

            
            //Reprise
            $this->calculContratReprise($contrat, $request->dateResiliation);


            $contrat->statut_id = 22;
            $contrat->date_resiliation = $request->dateResiliation;
            $contrat->save();
            
            $devis->statut_id = 43;
            $devis->save();

            $devisController = new DevisController();
            $devisController->recalculerCadSante($devis);
            
            $devis->fiche->statut_id = 44;
            $devis->fiche->save();
            $motifs = "";
            foreach ($devisFiche as $key => $value) {
                $d = Devis::find($value->id);
                if (!in_array($d->statut_id, [19,26,27,38,41,43]))
                    $d->etat = 1;
                else $d->etat = 0;
                $d->save();
            }

            foreach ($request->motifId as $key => $value) {

                $motifs = count($request->motifId) > 1 ? $motifs.Motif::find($value)->libelle.' ,' :  $motifs.Motif::find($value)->libelle;
            }

        // Format date Resiliation
        $dateResiliationHist = Carbon::parse($request->dateResiliation)->format('d/m/Y');
        // Interface résiliation injection compagnie

        if($request->action == 'resiliation-portefeuille-compagnie') {
            $slug = $request->slug;
            $commentaire = $request->commentaire_org;
        }else{
            $slug = 'resilier-contrat';
            $commentaire = "$dateResiliationHist / Résiliation contrat";
        }

            //sent Email
            $model   = ModelMail::where('nom', 'Resiliation contrat')->first();
            if ($model) $request['id'] = $model->id;

            
            
            // $targetUser = User::find(453); //'laetitia_id'
            
            

            $sendemail = new ConfigMailingController();

            $request['motif'] = $motifs;

            //send mail to selected profiles 
            $emails = [];
            if ($model) {
                $emails = $model->getModelProfilsEmails($devis);
            }
            
            foreach ($emails as $email) {
                $request['tier'] = $email;
                $sendemail->envoi($request);
            }


        $statusActionFiche = new Statutaction;
        $statusActionFiche->user_id = Auth::user()->id;
        $statusActionFiche->statut_id = $devis->fiche->statut_id;
        $statusActionFiche->entitable_id = $devis->fiche->id;
        $statusActionFiche->entitable_type = "Fiche"; 
        $statusActionFiche->save();
        
        $tabInfoTraceMatiere = [  
            'courtier_id'        => $user->courtier_id, 
            'fiche_id'           => $devis->fiche->id,
            'user_id'            => $user->id,
            'userable_id'        => $devis->fiche->userable_id,
            'userable_type'      => $devis->fiche->userable_type,
            'entitable_id'       => $contrat->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $devis->fiche->dispatchable_id,
            'dispatchable_type'  => $devis->fiche->dispatchable_type,
            'statut_id'          => $contrat->statut_id,
            'slug'               => $slug,
            'commentaire'        => $commentaire,
            'complement'         => '',
            'active'             => 1, 
        ];

            event(new EventTraceMatieres($tabInfoTraceMatiere));
            $statutAction = new Statutaction();

            if ($request->motifId) {
                $motif = (gettype($request->motifId) == 'array') ? $request->motifId : [$request->motifId];
            }
            
            $statutAction->entitable_id     = $contrat->id;
            $statutAction->entitable_type   = 'Contrat';
            $statutAction->user_id          = $user->id;
            $statutAction->statut_id        = $contrat->statut_id;
            $statutAction->motif_id         = ($request->motifId) ? json_encode($motif) : NULL; // keep for backward compatibility
            $statutAction->complement       = ($request->commentaire) ? $request->commentaire : NULL;

            $statutAction->save();

            // insert in the pivot table (motif_statutaction)
            if (!empty($request->motifId)) {
                $motifIds = is_array($request->motifId) ? $request->motifId : [$request->motifId];
            
                $statutAction->motifs()->syncWithoutDetaching($motifIds); 
            }
            
            $contrat->statut = Statut::where('id',$contrat->statut_id)->first();
            
            if (preg_match_all('/\b(\w)/', strtoupper($contrat->statut->libelle), $m)) {
                $abr = implode('', $m[1]); // $v is now SOQTU
            }
            
            $contrat->statut->abr = $abr;
            
            DB::commit();

            //Notification
            $tiers = Tiers::find($devis->tiers_id);
            $tiers = new TiersResource($tiers); 
            $link = '/tiers/'.collect($tiers)['hash_id']. '/contrat/'.collect(new ContratResource($contrat))['hash_id'];
            $user_id = Devis::find($contrat->devis_id)->user_id;

            $titre = 'Résiliation du contrat';  

            event(new EventNotificationUsers(
                $user_id,
                $contrat->id,
                Auth::user()->courtier_id,
                'CONTRATRES',
                'Résiliation du contrat',
                'Contrat',
                'Changement statut contrat de client '.$tiers['raison_sociale'].' de la fiche : '. $devis->num_fiche.' à '. $contrat->statut->libelle,
                $titre . ' : ' . $devis->num_fiche,
                $link
            ));
            
            return $contrat;  
        } catch (ValidationException $e) {
            DB::rollback();
            throw $e;
        }
    }

    
    public function insertDateChuteContrat(Request $request)
    {
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);

        DB::connection('mysql2')->transaction(function () use ($request, $user) {
            $contrat = Contrat::findByHash($request->contrat_hash);
            $devis = $contrat->devis;
            $contrat->date_chute = Carbon::parse($request->dateChute);
            $contrat->save();
            $this->calculContratReprise($contrat, $request->dateChute);

            $tabInfoTraceMatiere = [  
                'courtier_id'        => $user->courtier_id, 
                'fiche_id'           => $devis->fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $devis->fiche->userable_id,
                'userable_type'      => $devis->fiche->userable_type,
                'entitable_id'       => $contrat->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $devis->fiche->dispatchable_id,
                'dispatchable_type'  => $devis->fiche->dispatchable_type,
                'statut_id'          => $contrat->statut_id,
                'slug'               => 'entrer-date-chute-contrat',
                'commentaire'        => 'Entrer date chute contrat',
                'complement'         => null,
                'active'             => 1, 
            ];
    
            event(new EventTraceMatieres($tabInfoTraceMatiere));
        });
    }


    public function calculContratReprise($contrat, $dateChute)
    {
        FonctionController::setConfigMySqlTarificateur();

        $commissions = $contrat->commissionCabinet;
        if (!$commissions->isEmpty()) {
            $formuleCommission = new \App\FormuleCommission();
            $formuleConfiguration = new \App\FormuleConfiguration();

            $latestCommission = $commissions->sortByDesc('created_at')->first();

            $devis = $contrat->devis;
            $devisFormule = json_decode($devis->formule, true);
            $configs = $formuleConfiguration->getDevisConfig($devis);
            $gamme = Gamme::where('id', $devisFormule['idGamme'])->first();
            // $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();
            $dateChute = Carbon::parse($dateChute);
            $now = Carbon::now();

            $dateEffet = Carbon::parse($devis->date_effet);
            // $dureeContrat = $dateEffet->lt(Carbon::now()) ? Carbon::now()->diffInMonths($dateEffet) : 0; //check if date_effet less than cancel_date else 0
            $dureeContrat = $dateEffet->lt($dateChute) ? $dateChute->diffInMonths($dateEffet) : 0; //check if date_effet less than cancel_date else 0
            // dd('game' , $gamme, 'fichge', $fiche ,' $dateChute',  $dateChute, '$dateEffet', $dateEffet, '$dureeContrat ', $dureeContrat );


            if (!$contrat->num_police) { //if Contrat doent have NUM POLICE Yet => DELETE all current commissions
                foreach ($commissions as $commission) {
                    $commission->delete();
                }
            } else {
                // Get commissions type
                $commissionType = '';
                $lastComType = $latestCommission->types_commission_id;
                
                if ($lastComType == 1) {
                    if ($latestCommission->precompte_partial_partie) {
                        $commissionType = 'partiel';
                    } else {
                        $commissionType = 'precompte';
                    }
                } elseif ($lastComType == 2 || $lastComType == 3 || $lastComType == 4 || $lastComType == 11) {
                    $commissionType = 'lineaire';
                } elseif ($lastComType == 5 || $lastComType == 6 || $lastComType == 7 || $lastComType == 12) {
                    $commissionType = 'terme';
                } elseif ($lastComType == 8 || $lastComType == 9 || $lastComType == 10 || $lastComType == 13) {
                    $commissionType = 'escompté';
                }

                //caculate Reprise
                if ($commissionType == 'precompte' || $commissionType == 'partiel') {
                    //Fetching slugged formules
                    $sluggedFormule = $formuleCommission->getFormuleBySlug($devis, 'reprise');
                    $formuleSurCom = $configs->reprise_sur_com ? $formuleCommission->getFormuleBySlug($devis, 'repr_sur_com') : null;

                    $formules['formuleCommission'] = $sluggedFormule;
                    $formuleSurCom ? $formules['formuleSurCommission'] = $formuleSurCom : '';

                    $createdAt = null;
                    if ($devis->contrat && isset($devis->contrat->created_at)) {
                        $createdAt = $devis->contrat->created_at;
                    } else {
                        $createdAt = Carbon::now()->format("Y-m-d H:i:s");
                    }

                    if (!$formules['formuleCommission']) {
                        // throw new Exception("La formule de reprise de " . $gamme->libelle . " est introuvable.", 500);
                        throw ValidationException::withMessages(['error' => "La formule de reprise de la gamme " . $gamme->libelle . " est introuvable. Date : ". Carbon::parse($createdAt)->format('d/m/Y')]);
                    }
                    if ($configs->reprise_sur_com && !isset($formules['formuleSurCommission'])) {
                        // throw new Exception("La formule de reprise sur-commission de " . $gamme->libelle . " est introuvable.", 500);
                        throw ValidationException::withMessages(['error' => "La formule de reprise sur-commission de la gamme " . $gamme->libelle . " est introuvable. Date : ". Carbon::parse($createdAt)->format('d/m/Y')]);
                    }

                    foreach ($formules as $key => $formule) {
                        //check Reprise min & max
                        $min = $formule->mois_min;
                        $max = $formule->mois_max;

                        if ($formule) {
                            if ($min !== null && $max !== null && $dureeContrat >= $min) {
                                if ($dureeContrat <= $max) {
                                    $sluggedFormule = $formule->formule;

                                    //replacing formule params from string to values
                                    $sluggedFormule = $formuleCommission->transformSlugsToValues($sluggedFormule, $devis, $dateChute);

                                    //converting formule to string & getting its result.
                                    $reprise = round(eval('return ' . $sluggedFormule['formule'] . ';'), 2);
                                    $reprises[$formule->mode->slug]['value'] = $reprise;
                                    $reprises[$formule->mode->slug]['formule'] = $sluggedFormule['formule'];
                                    $reprises[$formule->mode->slug]['formuleId'] = $formule->id;

                                    $commissionFormule = CommissionFormule::where('commission_id', $latestCommission->id)->first();

                                    $commissionFormule->formule_reprise_id = $reprises['reprise']['formuleId'];
                                    $commissionFormule->formule_reprise = $reprises['reprise']['formule'];
                                    $commissionFormule->formule_reprise_surcom_id = isset($reprises['repr_sur_com']) ? $reprises['repr_sur_com']['formuleId'] : null;
                                    $commissionFormule->formule_reprise_sur_com  = isset($reprises['repr_sur_com']) ? $reprises['repr_sur_com']['formule'] : null;
                                    $commissionFormule->save();
                                } elseif ($dureeContrat > $max) { //if dureeContrat greater than Max: No reprise
                                    $reprises[$formule->mode->slug]['value'] = 0;
                                }
                            } else {
                                if ($commissionType == 'partiel') {
                                    $sumSurCommission = 0;
                                }

                                //Reprise total
                                if ($key == 'formuleCommission') {
                                    if ($commissionType == 'partiel') { // if partiel sum both partes commissions in one
                                        $sumCommission = 0;
                                        foreach ($commissions as $commission) {
                                            $sumCommission = $sumCommission + $commission->commission;
                                        }
                                        $reprises[$formule->mode->slug]['value'] = $sumCommission;
                                    } else {
                                        $reprises[$formule->mode->slug]['value'] = $latestCommission->commission;
                                    }
                                } elseif ($key == 'formuleSurCommission') {
                                    if ($commissionType == 'partiel') { // if partiel sum both  partes sur_commissions in one
                                        $sumSurCommission = 0;
                                        foreach ($commissions as $commission) {
                                            $sumSurCommission = $sumSurCommission + $commission->sur_commission;
                                        }
                                        $reprises[$formule->mode->slug]['value'] = $sumSurCommission;
                                    } else {
                                        $reprises[$formule->mode->slug]['value'] = $latestCommission->sur_commission;
                                    }
                                }
                            }
                        }
                    }

                    //save Reprise
                    if ($commissionType == 'partiel') {
                        foreach ($commissions as $commission) {
                            $commission->reprise =  $reprises['reprise']['value'] ? ($commission->precompte_partial / 100) * $reprises['reprise']['value'] : null;
                            $commission->reprise_sur_commission =  isset($reprises['repr_sur_com']) ? ($commission->precompte_partial / 100) * $reprises['repr_sur_com']['value'] : null;

                            $commission->save();
                        }
                    } else {
                        $latestCommission->reprise = $reprises['reprise']['value'];
                        $latestCommission->reprise_sur_commission = isset($reprises['repr_sur_com']) ? $reprises['repr_sur_com']['value'] : null;
                        $latestCommission->save();
                    }
                } else if ($commissionType == 'lineaire' || $commissionType == 'terme') {
                    //check if commission before 'Date chute'            => KEEP   
                    //      if commission between 'Date chute' & 'Now'   => REPRISE
                    //      if commission after 'Now'                    => DELETE


                    foreach ($commissions as $key => $commission) {
                        //remove decalage months from 'date commission'
                        $dateCommission = $configs->decalage_lineaire 
                            ? Carbon::parse($commission->date_commission)->subMonths($configs->decalage_lineaire)
                            : Carbon::parse($commission->date_commission);
                        
                        if ($dateCommission->gt($now)) {   //      if commission after 'Now' => DELETE
                            $commission->delete();
                        } elseif ($dateCommission->gt($dateChute) && $dateCommission->lt($now)) { //if commission between 'Date chute' & 'Now'   => REPRISE
                            $commission->reprise = $commission->commission;
                            $commission->reprise_sur_commission = ($commission->sur_commission && $configs->reprise_sur_com) ? $commission->sur_commission : null;
                            $commission->save();
                        } elseif ($dateCommission->lt($dateChute)) {
                            //calculate reprise when 'type paiment' trimestriel & semestriel & annuel (lineare / terme)
                            //make sure that the commission start before 'Date chute' & Ends after 'Date chute' then 'reprise' Else donc 'reprise'
                            $nextDateCom = null;
                            if (isset($commissions[$key + 1])) {
                                $nextDateCom = $configs->decalage_lineaire
                                    ? Carbon::parse($commissions[$key + 1]->date_commission)->subMonths($configs->decalage_lineaire)
                                    : Carbon::parse($commissions[$key + 1]->date_commission);
                            }

                            if (!isset($commissions[$key + 1]) || isset($commissions[$key + 1]) && $nextDateCom && $nextDateCom->gt($dateChute)) {
                                $this->calculeTypePaimentReprise($key, $commission, $dateCommission, $dateChute, $configs);
                            }
                        }
                    }
                } else if ($commissionType == 'escompté') {
                    foreach ($commissions as $key => $commission) {
                        //remove decalage months from 'date commission'
                        $dateCommission = $configs->decalage_lineaire 
                            ? Carbon::parse($commission->date_commission)->subMonths($configs->decalage_lineaire)
                            : Carbon::parse($commission->date_commission);

                        // escompte commission 
                        if ($commission->types_commission_id == 8 || $commission->types_commission_id == 9 || $commission->types_commission_id == 10 || $commission->types_commission_id == 13) {
                            if ($dateChute->lt($dateEffet)) { // if date chute before date effet -> Reprise total
                                $commission->reprise = $commission->commission;
                                $commission->reprise_sur_commission = $commission->sur_commission ? $commission->sur_commission : null;
                                $commission->save();
                            } else if ($dateChute->gt($dateEffet)) { //if date chute After date effet -> Reprise only the months that are after date chute & before now
                                //check if diffrence by months between 'date chute' and 'date effet' is (X months + extra days ) : add +1 month to diffrence 
                                $dureePayed = (clone $dateEffet)->startOfMonth()->diffInMonths((clone $dateChute)->startOfMonth());
                                $dateEffetDays = (clone $dateEffet)->diffInDays((clone $dateEffet)->startOfMonth());
                                $dateChuteDays = (clone $dateChute)->diffInDays((clone $dateChute)->startOfMonth());
                                $dureePayed = $dateEffetDays < $dateChuteDays ? $dureePayed + 1 : $dureePayed;

                                if (($commission->mois_escomptee) > $dureePayed) { //check if escompte is fully payedoff or not, if yes KEEP IT else REPRISE
                                    $repriseMonths = $commission->mois_escomptee - $dureePayed;

                                    $repriseCommission = ($commission->commission / $commission->mois_escomptee) * $repriseMonths; //by doing: escompte commssion / mois escompte, we get 1 month commission value. reprise = 1 month commission value * repriseMonths
                                    $repriseSurCommission = ($commission->sur_commission && $configs->reprise_sur_com) ? ($commission->sur_commission / $commission->mois_escomptee) * $repriseMonths : null;

                                    $commission->reprise = $repriseCommission;
                                    $commission->reprise_sur_commission = $repriseSurCommission;
                                    // dd('$dateEffet', $dateEffet, '$dateChute' , $dateChute ,  '$dureePayed',  $dureePayed, 'repriseMonths', $repriseMonths, 'commission', $commission->commission, '$repriseCommission', $repriseCommission);
                                    $commission->save();
                                }
                            }
                        } else { // lineaire commissions after Escompté
                            if ($dateCommission->gt($now)) {
                                $commission->delete();
                            } elseif ($dateCommission->gt($dateChute) && $dateCommission->lt($now)) {
                                $commission->reprise = $commission->commission;
                                $commission->reprise_sur_commission = ($commission->sur_commission && $configs->reprise_sur_com) ? $commission->sur_commission : null;
                                $commission->save();
                            } elseif ($dateCommission->lt($dateChute)) {
                                $firstCom = $commissions[0];
                                $oneMonthCom = null;
                                $oneMonthSurCom = null;

                                if (in_array($firstCom->types_commission_id, [8, 9, 10, 13])) {
                                    $oneMonthCom = $firstCom->commission / $firstCom->mois_escomptee;
                                    $oneMonthSurCom = $firstCom->sur_commission / $firstCom->mois_escomptee;
                                }

                                //calculate reprise of 'type paiment' trimestriel & semestriel & annuel (lineare / terme)
                                //make sure that the commission start before 'Date chute' & Ends after 'Date chute' then 'reprise' Else donc 'reprise'
                                if (!isset($commissions[$key + 1]) || isset($commissions[$key + 1]) && Carbon::parse($commissions[$key + 1]->date_commission)->gt($dateChute)) {
                                    $this->calculeTypePaimentReprise($key, $commission, $dateCommission, $dateChute, $configs, $firstCom->mois_escomptee, $oneMonthCom, $oneMonthSurCom);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public function editDateCommissionCabinet(Request $request, $commissionId){
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);

        $commissionResource = null;
        
        $oldCommission = CommissionCabinet::find($commissionId);
        $contrat = Contrat::find($oldCommission->contrat_id);
        $devis = $contrat->devis;
        
        $oldDateComm = $oldCommission->date_commission;
        $newDateComm =  Carbon::parse($request->newDateCommission)->format('Y-m-d');


        // Copy all attributes from the old commission to a new one
        $newCommission = new CommissionCabinet;
        foreach ($oldCommission->getAttributes() as $key => $value) {
            if ($key != 'id' && $key != 'created_at' && $key != 'updated_at' && $key != 'deleted_at') {
                $newCommission->$key = $value;
            }
        }

        //apply new date
        $newCommission->date_commission = $newDateComm;
        $newCommission->save();
        
        $oldCommissionFormules = CommissionFormule::where('commission_id', $oldCommission->id)->get();
        
        foreach ($oldCommissionFormules as $commissionFormule) {
            $newCommissionFormule = new CommissionFormule;
            foreach ($commissionFormule->getAttributes() as $key => $value) {
                if ($key != 'id' && $key != 'created_at' && $key != 'updated_at') {
                    $newCommissionFormule->$key = $value;
                }
            }
            $newCommissionFormule->commission_id = $newCommission->id;
            $newCommissionFormule->save();
        }
    
        // Delete the old commission
        $oldCommission->delete();

        $tabInfoTraceMatiere = [
            'courtier_id'        => $user->courtier_id,
            'fiche_id'           => $devis->fiche->id,
            'user_id'            => $user->id,
            'userable_id'        => $devis->fiche->userable_id,
            'userable_type'      => $devis->fiche->userable_type,
            'entitable_id'       => $contrat->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $devis->fiche->dispatchable_id,
            'dispatchable_type'  => $devis->fiche->dispatchable_type,
            'statut_id'          => $contrat->statut_id,
            'slug'               => 'modifier-date-commission-cabinet',
            'commentaire'        => 'Date commission: '. Carbon::parse($oldDateComm)->format('Y-m-d') . ' -> '. $newDateComm,
            'complement'         => '',
            'active'             => 1,
        ];

        event(new EventTraceMatieres($tabInfoTraceMatiere));

        $commissionResource = new CommissionCabinetResource($newCommission);
        return $commissionResource;
    }

    public function calculeTypePaimentReprise($key, $commission, $dateCommission, $dateChute, $configs, $escompteMonth = null, $oneMonthCom = null, $oneMonthSurCom = null)
    {
        $nbrOfMonths = null;

        //calculate reprise of type paiment trimestriel & semestriel & annuel (lineare / terme)
        if (in_array($commission->types_commission_id, [3, 4, 11, 6, 7, 13])) {
            //if the first commission after escompté 'key == 1' remove used months in escompté ex: '6 - $escompteMonth'
            if (in_array($commission->types_commission_id, [3, 4, 11])) { //lineare 
                if ($commission->types_commission_id == 3) {            //lineaire_trimestriel
                    $nbrOfMonths = ($escompteMonth && $key == 1 && $escompteMonth < 3)
                        ? 3 - $escompteMonth
                        : 3;
                } elseif ($commission->types_commission_id == 4) {      //lineaire_semestriel
                    $nbrOfMonths = ($escompteMonth && $key == 1  && $escompteMonth < 6)
                        ? 6 - $escompteMonth
                        : 6;
                } elseif ($commission->types_commission_id == 11) {     //lineaire_annuel
                    $nbrOfMonths = ($escompteMonth && $key == 1 && $escompteMonth < 12)
                        ? 12 - $escompteMonth
                        : 12;
                }
            } elseif (in_array($commission->types_commission_id, [6, 7, 13])) { //terme 
                if ($commission->types_commission_id == 6) {            //terme_trimestriel
                    $nbrOfMonths = 3;
                } elseif ($commission->types_commission_id == 7) {      //terme_semestriel
                    $nbrOfMonths = 6;
                } elseif ($commission->types_commission_id == 13) {     //terme_annuel
                    $nbrOfMonths = 12;
                }
            }

            if (!$oneMonthCom) {
                $oneMonthCom = $commission->commission / $nbrOfMonths;
                $oneMonthSurCom = $commission->sur_commission / $nbrOfMonths;
            }

            //check if diffrence by months between 'date chute' and 'date commissions' is (X months + extra days ) : add +1 month to diffrence 
            $payedMonths = (clone $dateCommission)->startOfMonth()->diffInMonths((clone $dateChute)->startOfMonth());
            $dateCommissionDays = (clone $dateCommission)->diffInDays((clone $dateCommission)->startOfMonth());
            $dateChuteDays = (clone $dateChute)->diffInDays((clone $dateChute)->startOfMonth());
            $payedMonths = $dateCommissionDays < $dateChuteDays ? $payedMonths + 1 : $payedMonths;

            $commission->reprise = $oneMonthCom * ($nbrOfMonths - $payedMonths);
            $commission->reprise_sur_commission = ($commission->sur_commission && $configs->reprise_sur_com)
                ? $oneMonthSurCom * ($nbrOfMonths - $payedMonths)
                : null;
            $commission->save();
        }
    }

    public function loadContratsConseiller(Request $request) 
    {
        $user = Auth::user();

        $dateDebut = $request->dateDebut ? (new Carbon($request->dateDebut))->copy() : '';
        $dateFin = $request->dateFin ? (new Carbon($request->dateFin))->copy()->addHour(23)->addMinutes(59)->addSeconds(59) : '';

        FonctionController::setConfigMySql2($user->courtier_id);

        $grpBranche = Groupebranche::find($request->grpBranche);
        $brancheIds = ($request->branche) ? [$request->branche] : json_decode($grpBranche->branches, TRUE);

        $equipeUser = EquipeUser::where('user_id', $request->userId)
                                ->where('equipe_id', $request->equipeId)
                                ->orderby('integration_date', 'desc')
                                ->first();

        $nbrEquipes = EquipeUser::where('user_id', $request->userId)->count();
        
        $contratCons = Contrat::whereHas('devis', function($q) use($request, $dateDebut, $dateFin, $brancheIds) {
                            $q->where('user_id', $request->userId)
                                ->where(function ($query) use ($request, $dateDebut, $dateFin) {
                                    if ($dateDebut && $dateFin) {
                                        $query->whereBetween('date_validation', [$dateDebut, $dateFin]);
                                    } elseif ($dateDebut) {
                                        $query->where('date_validation', '>=' , $dateDebut);
                                    } elseif ($dateFin) {
                                        $query->where('date_validation', '<=' , $dateFin);
                                    }
                                })
                                ->whereHas('fiche',function($q) use ($request, $brancheIds) {
                                    $q->whereIn('branche_id', $brancheIds);
                                });
                        })->with(['devis' => function ($query) {
                                $query->with('typefiche');
                                $query->with('modePaiement');
                                $query->with('typePaiement');
                                $query->with('infosBancairesPrelevement');
                                $query->with('infosBancairesRemboursement');
                                $query->with(['reglements' => function($query) {
                                    $query->with('mode');
                                    $query->with('statut');
                                }]);
                                $query->with(['devisLR' => function ($q) {
                                    $q->with('statut');
                                    $q->with('motif');
                                    $q->whereActive(1);
                                }]);
                            }])
                            ->with(['client' => function ($query) {
                                $query->with('tiers');
                            }])
                            ->with('statut')
                        ->latest()
                        ->get();

        $contratsConseiller = $contratCons->when($nbrEquipes > 1, function($query) use ($equipeUser) {
            return $query->where('devis.date_validation', '>=', $equipeUser->integration_date)
                ->when($equipeUser->deleted, function ($contratCons) use ($equipeUser) {
                    return $contratCons->where('devis.date_validation', '<=', $equipeUser->deleted);
                });
        });
        
        $devisCons = Devis::where('user_id', $request->userId)
                        ->where(function ($query) use ($request, $dateDebut, $dateFin) {
                            if ($dateDebut && $dateFin) {
                                $query->whereBetween('date_validation', [$dateDebut, $dateFin]);
                            } elseif ($dateDebut) {
                                $query->where('date_validation', '>=' , $dateDebut);
                            } elseif ($dateFin) {
                                $query->where('date_validation', '<=' , $dateFin);
                            }
                        })
                        ->where('etat',1)
                        ->whereIn('statut_id', [13,17,39])
                        ->with('typefiche')
                        ->with('statut')
                        ->whereHas('fiche',function($q) use ($request, $brancheIds) {
                            $q->whereIn('branche_id', $brancheIds);
                        })
                        ->latest()
                        ->get();

        // $devisConseiller = $devisCons->where('date_validation', '>=', $equipeUser->integration_date)
        //                                 ->when($equipeUser->deleted, function ($devisCons) use ($equipeUser) {
        //                                     return $devisCons->where('date_validation', '<=', $equipeUser->deleted);
        //                                 });

        $devisConseiller = $devisCons->when($nbrEquipes > 1, function($query) use ($equipeUser) {
            return $query->where('date_validation', '>=', $equipeUser->integration_date)
                ->when($equipeUser->deleted, function ($devisCons) use ($equipeUser) {
                    return $devisCons->where('date_validation', '<=', $equipeUser->deleted);
                });
        });

        $devisC = new DevisController();
        $contrats = $this->listContratss($contratsConseiller);
        $devis = $devisC->listDeviss($devisConseiller);
        
        return ['contrats' => $contrats->values(), 'devis' => $devis->values()];
    }

    public function loadContratsConseillerr(Request $request) 
    {
        $user = Auth::user();

        $dateDebut = $request->dateDebut ? (new Carbon($request->dateDebut))->copy() : '';
        $dateFin = $request->dateFin ? (new Carbon($request->dateFin))->copy()->addHour(23)->addMinutes(59)->addSeconds(59) : '';

        FonctionController::setConfigMySql2($user->courtier_id);

        $grpBranche = Groupebranche::find($request->grpBranche);
        $brancheIds = ($request->branche) ? [$request->branche] : json_decode($grpBranche->branches, TRUE);

        $equipeUser = EquipeUser::where('user_id', $request->userId)
                                ->where('equipe_id', $request->equipeId)
                                ->orderby('integration_date', 'desc')
                                ->first();
        
        $contratCons = Contrat::whereHas('devis', function($q) use($request, $dateDebut, $dateFin, $brancheIds) {
                            $q->where('user_id', $request->userId)
                                ->where(function ($query) use ($request, $dateDebut, $dateFin) {
                                    if ($dateDebut && $dateFin) {
                                        $query->whereBetween('date_validation', [$dateDebut, $dateFin]);
                                    } elseif ($dateDebut) {
                                        $query->where('date_validation', '>=' , $dateDebut);
                                    } elseif ($dateFin) {
                                        $query->where('date_validation', '<=' , $dateFin);
                                    }
                                })
                                ->whereHas('fiche',function($q) use ($request, $brancheIds) {
                                    $q->whereIn('branche_id', $brancheIds);
                                });
                        })->with(['devis' => function ($query) {
                                $query->with('typefiche');
                                $query->with('modePaiement');
                                $query->with('typePaiement');
                                $query->with('infosBancairesPrelevement');
                                $query->with('infosBancairesRemboursement');
                                $query->with(['devisLR' => function ($q) {
                                    $q->with('statut');
                                    $q->with('motif');
                                    $q->whereActive(1);
                                }]);
                            }])
                            ->with(['client' => function ($query) {
                                $query->with('tiers');
                            }])
                            ->with('statut')
                        ->latest()
                        ->get();

        $contratsConseiller = $contratCons->where('devis.date_validation', '>=', $equipeUser->integration_date)
                                        ->when($equipeUser->deleted, function ($contratCons) use ($equipeUser) {
                                            return $contratCons->where('devis.date_validation', '<=', $equipeUser->deleted);
                                        });
        
        $devisCons = Devis::where('user_id', $request->userId)
                        ->where(function ($query) use ($request, $dateDebut, $dateFin) {
                            if ($dateDebut && $dateFin) {
                                $query->whereBetween('date_validation', [$dateDebut, $dateFin]);
                            } elseif ($dateDebut) {
                                $query->where('date_validation', '>=' , $dateDebut);
                            } elseif ($dateFin) {
                                $query->where('date_validation', '<=' , $dateFin);
                            }
                        })
                        ->where('etat',1)
                        ->whereIn('statut_id', [13,17,39])
                        ->with('typefiche')
                        ->whereHas('fiche',function($q) use ($request, $brancheIds) {
                            $q->whereIn('branche_id', $brancheIds);
                        })
                        ->latest()
                        ->get();

        $devisConseiller = $devisCons->where('date_validation', '>=', $equipeUser->integration_date)
                                        ->when($equipeUser->deleted, function ($devisCons) use ($equipeUser) {
                                            return $devisCons->where('date_validation', '<=', $equipeUser->deleted);
                                        });

        $devisC = new DevisController();
        $contrats = $this->listContratss($contratsConseiller);
        $devis = $devisC->listDevis($devisConseiller);
        
        return ['contrats' => $contrats->values(), 'devis' => $devis->values()];
    }

    public function exportContratSynthese(Request $request) 
    { 
       $contrats = $this->loadContratsConseiller($request)['contrats'];
        return Excel::download(new ViewDataExport(compact('contrats'), 'exports.exportContratSynthese'), 'contratsSynthese.xlsx');
    }

    public function exportBelair(Request $request)
    {
        $contrats = $this->getContratsExport($request);
        $nbrContrat = 0;
        
        foreach ($contrats as $key => $value) { // count and sizeof n'a pas fonctionné
            $nbrContrat = $key;
        }

        $xml = '<?xml version="1.0" encoding="utf-8"?>
                    <crm_inputcontent>
                        <header>
                            <Society>
                                <orias>07009027</orias>
                                <siret>----</siret>
                            </Society>
                            <crm_imputCount>'.($nbrContrat + 1).'</crm_imputCount>
                        </header>
                        <crm_inputs>
                        ';
                foreach ($contrats as $key => $contrat) {
                    $xml .= '<crm_input rank="'.($key + 1).'">
                                <tiers_create>
                                    <params>
                                        <param name="nature" type="ptString">P</param><!-- P:personne physiqe-->
                                        <param name="typtiers" type="ptString">1</param><!--1:Client -->
                                    </params>
                                    <input>
                                        <objects>
                                            <object typename="TIERS">
                                                <param name="Adr1" type="ptString">'.$contrat->fiche->tiers->adresse1.'</param>
                                                <param name="Adr2" type="ptString">'.$contrat->fiche->tiers->adresse2.'</param>
                                                <param name="Adr3" type="ptString">'.$contrat->fiche->tiers->adresse3.'</param>
                                                <param name="Codepays" type="ptUnknown" is_null="true"/>
                                                <param name="Codp" type="ptString">'.$contrat->fiche->tiers->code_postal.'</param>
                                                <param name="Ntel" type="ptString">'.$contrat->fiche->tiers->num_tel.'</param>
                                                <param name="Numemail" type="ptString">'.$contrat->fiche->tiers->numemail.'</param>
                                                <param name="Ville" type="ptString">'.$contrat->fiche->tiers->ville.'</param>
                                            </object>
                                            <object typename="DPP"><!-- Assuré -->
                                                <param name="Activite" type="ptString">----</param>
                                                <param name="Datenais" type="ptDateTime" date_val="'.$contrat->fiche->tiers->dpp->date_naissance.'T00:00:00.000"/>
                                                <param name="Nom" type="ptString">'.$contrat->fiche->tiers->dpp->nom.'</param>
                                                <param name="Portable" type="ptString">'.$contrat->fiche->tiers->portable.'</param>
                                                <param name="Prenom" type="ptString">'.$contrat->fiche->tiers->dpp->prenom.'</param>
                                                <param name="RegimeSocial" type="ptString">'.$contrat->fiche->tiers->dpp->regime_id.'</param>
                                                <param name="Titre" type="ptString">'.$contrat->fiche->tiers->dpp->titre.'</param>
                                            </object>
                                        </objects>
                                    </input>
                                    <familymembers>
                                    '; // autres bénéficaires (membres de la famille) -->
                                    if($contrat->fiche->listBeneficiaires['conjoint']){
                                        $xml.= '<familymember>
                                                    <object typename="Fam">
                                                        <param name="Parente" type="ptString">C</param>
                                                    </object>
                                                    <object typename="DPP">
                                                        <param name="Activite" type="ptString">----</param>
                                                        <param name="Datenais" type="ptDateTime" date_val="'.$contrat->fiche->listBeneficiaires['conjoint']->date_naissance.'T00:00:00.000"/>
                                                        <param name="Nom" type="ptString">'.$contrat->fiche->listBeneficiaires['conjoint']->nom.'</param>
                                                        <param name="Nomfille" type="ptUnknown" is_null="true"/>
                                                        <param name="Numss" type="ptUnknown" is_null="true"/>
                                                        <param name="Prenom" type="ptString">'.$contrat->fiche->listBeneficiaires['conjoint']->prenom.'</param>
                                                        <param name="RegimeSocial" type="ptString">'.$contrat->fiche->listBeneficiaires['conjoint']->regime_id.'</param>
                                                        <param name="Titre" type="ptString">----</param>
                                                    </object>
                                                </familymember>
                                                ';
                                    } 
                                    foreach ($contrat->fiche->listBeneficiaires['enfants'] as $enfant){
                                        $xml.= '<familymember>
                                                    <object typename="Fam">
                                                        <param name="Parente" type="ptString">E</param>                                 
                                                    </object>
                                                    <object typename="DPP">
                                                        <param name="Activite" type="ptString">----</param>
                                                        <param name="Datenais" type="ptDateTime" date_val="'.$enfant->date_naissance.'T00:00:00.000"/>
                                                        <param name="Nom" type="ptString">'.$enfant->nom.'</param>
                                                        <param name="Prenom" type="ptString">'.$enfant->prenom.'</param>
                                                        <param name="RegimeSocial" type="ptString">'.$enfant->regime_id.'</param>
                                                        <param name="Titre" type="ptString">'.$enfant->titre.'</param>
                                                    </object>
                                                </familymember>
                                                ';
                                    }                             
                            $xml .= '</familymembers>
                                </tiers_create>
                                <cont_create>
                                    <params>
                                        <param name="effet" type="ptDateTime" date_val="'.$contrat->fiche->date_effet.'T00:00:00.000"/>
                                        <param name="produit" type="ptString">'.$contrat->formule['codeGamme'].'</param>
                                        <param name="formule" type="ptString">'.$contrat->formule['codeGarantie'].'</param>
                                    </params>
                                    <input>
                                        <objects>
                                            <object typename="CONT">
                                                <param name="Affnouv" type="ptDateTime" date_val="----T00:00:00.000"/>
                                                <param name="Apport1" type="ptInt" int_val="----"/>
                                                <param name="Commann" type="ptFloat" float_val="----E+0002"/>
                                                <param name="Commann1" type="ptString">---EUR</param>
                                                <param name="Datereal" type="ptDateTime" date_val="-----T00:00:00.000"/>
                                                <param name="Echpjj" type="ptString">---</param>
                                                <param name="Echpmm" type="ptString">---</param>
                                                <param name="Frac" type="ptString">---</param>
                                                <param name="Intitule" type="ptString">----</param>
                                                <param name="Portef" type="ptString">'.$contrat->formule['cieCode'].'</param>
                                                <param name="Primann" type="ptFloat" float_val="----E+0003"/>
                                                <param name="Primann1" type="ptString">----</param>
                                                <param name="Txcomm" type="ptFloat" float_val="----E+0009"/>
                                            </object>
                                            <object typename="piec">
                                                <param name="Datcreat" type="ptDateTime" date_val="---T15:34:07.147"/>
                                                <param name="Datesit" type="ptDateTime" date_val="----T00:00:00.000"/>
                                                <param name="Oripiece" type="ptString">1---</param>
                                                <param name="Piece" type="ptInt" int_val="---"/>
                                                <param name="Sitpiece" type="ptString">----</param>
                                            </object>
                                            <object typename="poli">
                                                <param name="Police" type="ptString">'.$contrat->num_police.'</param>
                                                <param name="Role" type="ptString">----</param>
                                            </object>
                                        </objects>
                                    </input>
                                </cont_create>
                            </crm_input>
                            ';
                }

            $xml .= '</crm_inputs>
                    </crm_inputcontent>';
                
            Storage::put('file.xml', $xml);
            
            return response()->download(storage_path("app/file.xml"));
    }
    public function ModifierNumPolice(Request $request) {
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);
        $contrat = Contrat::findByHash($request->contrat_hash);
        $ancienNp =  $contrat->num_police;
        $etat = false;
        if(strcmp($ancienNp, $request->num_police) != 0){
                $contrat->num_police = $request->num_police;
                $devis = Devis::where('id', $contrat->devis_id)->with('fiche')->first();
                $commentaire = '';
                    if(!$request->num_police) {
                        $contrat->statut_id = 21;
                        $statusActionContrat = new Statutaction;
                        $statusActionContrat->user_id = Auth::user()->id;
                        $statusActionContrat->statut_id = $contrat->statut_id;
                        $statusActionContrat->entitable_id = $contrat->id;
                        $statusActionContrat->entitable_type = "Contrat"; 
                        $statusActionContrat->complement       = ($request->commentaire) ? $request->commentaire : NULL;
                        $statusActionContrat->save();
                        $commentaire = 'Suppression du numéro de police';
                        $slug = 'supprimer-numero-police';
                    }
                    else
                        {
                            $commentaire = 'Modification du numéro de police  Ancien N° police :'.$ancienNp .' Nouveau N° police :'.$request->num_police;
                            $slug = 'modifier-numero-police';
                        }
               
                $contrat->save();
                $tabInfoTraceMatiere = [  
                    'courtier_id'        => $user->courtier_id, 
                    'fiche_id'           => $devis->fiche->id,
                    'user_id'            => $user->id,
                    'userable_id'        => $devis->fiche->userable_id,
                    'userable_type'      => $devis->fiche->userable_type,
                    'entitable_id'       => $contrat->id,
                    'entitable_type'     => 'Contrat',
                    'dispatchable_id'    => $devis->fiche->dispatchable_id,
                    'dispatchable_type'  => $devis->fiche->dispatchable_type,
                    'statut_id'          => $contrat->statut_id,
                    'slug'               => $slug,
                    'commentaire'        => $commentaire,
                    'complement'         => $request->commentaire,
                    'active'             => 1, 
                ];

                event(new EventTraceMatieres($tabInfoTraceMatiere));
           
                $contrat->statut = Statut::where('id',$contrat->statut_id)->first();
                
                if (preg_match_all('/\b(\w)/', strtoupper($contrat->statut->libelle), $m)) {
                    $abr = implode('', $m[1]); // $v is now SOQTU
                }
                
                $contrat->statut->abr = $abr;
                $etat = true;
        }        
        
        return ['contrat'=>$contrat,'etat'=>$etat];  

    }

    public function ExtractRejetProd() {
        $user = Auth::user();
        $master = DB::connection()->getDatabaseName();
        FonctionController::setConfigMySql2($user->courtier_id);

        $data = Statutaction::join('devis','devis.id','statutactions.entitable_id')
                            ->join($master.'.users', $master.'.users.id','statutactions.user_id')
                            ->join('fiches','devis.num_fiche','fiches.num_fiche')
                            ->leftjoin('statuts','statuts.id','devis.statut_id')
                            ->where('statutactions.statut_id',17)
                            ->where('statutactions.entitable_type','Devis')
                            ->whereBetween('statutactions.created_at',['2020-01-01 00:00:00','2020-04-20  23:59:59'])
                            ->select($master.'.users.nom as nomgestionnaire',
                                     $master.'.users.prenom as prenomGestionnaire',
                                     'devis.num_devis',
                                     'devis.formule->libelle as formule',
                                     'devis.formule->cieCode as cie',
                                     'devis.formule->gamme as gamme',
                                     'devis.date_effet as DATE_EFFET',
                                     'statuts.libelle as statutactuel',
                                     'devis.user_id',
                                     'devis.statut_id',
                                     'fiches.id as ficheId',
                                     'statutactions.motif_id',
                                     'statutactions.created_at as Date_du_rejet',
                                     'devis.date_validation as DATE_VAL_PRODUCTION',
                                     'statutactions.complement as commentaire'
                                      )
                            ->get()->toArray();
        
        $ficheController = new FicheController;
        foreach ($data as $key => $rejet) {
            
            $data[$key]['motifs'] = Motif::whereIn('id',(array)json_decode($rejet['motif_id']))->get();
            
            $data[$key]['affecte_a'] = $ficheController->affectee_a($rejet['ficheId'], $rejet['user_id']);

        }

        return Excel::download(new ViewDataExport(compact('data'), 'contrats.exportRejet'), 'rejet-prod.xlsx');
    }

    public function loadPermissions()
    {
        $permissions = Permission::whereActive(1)->whereIn('name',['valider-depot-production', 'rejeter-depot-production', 
                                                                    'rejeter-contrat', 'annulation-contrat', 'validation-contrat',
                                                                    'valider-contrats-en-attente-de-saisie', 'retractation-contrat',
                                                                    'resilier-contrat','valider-contrat-depose-en-remise-en-vigeur',
                                                                    'valider-retractation','validation-contrat-n-police',
                                                                    'validation-contrat-client', 'remise-en-vigeur-contrat',
                                                                    'contentieux-contrat', 'report-effet-contrat', 'reserver-lr','liberer-lr','ajouter-complement-l-r','modifier-l-r','ajouter-date-envoi','modifier-date-envoi','ajouter-num-envoi','modifier-num-envoi'
                                                                    ])->orderBy('display_name', 'asc')->get();                                                 
        return $permissions;
    }

    public function loadFiltreStatus($id)
    {

        /** @var User $authUser */
        $authUser = Auth::user();
        $authUser->load('profil');

        FonctionController::setConfigMySql2(Auth::user()->courtier_id);

        return $statuts = ($authUser->isGestionnaire() && $id == 2) ? (Statut::whereIn('id', [13, 17])->where('groupestatut_id', 2)->whereActive(1)->orderBy('libelle')->get()) : (Statut::where('groupestatut_id', $id)->whereNotIn('id', [19, 26, 38])->whereActive(1)->orderBy('libelle')->get());
    }

    
    public function testValGestionnaireContrat($devis_id)
    {
        $devis = Devis::find($devis_id);
        $grpBranchsIARDids = json_decode(groupebranche::find(2)->branches);

        if(in_array($devis->fiche->branche_id, $grpBranchsIARDids)){
            // $reglement = Reglement::where('devis_id',$devis_id)->where('statut_id','!=',78)->first();
            $reglSansGdf = Statut::where('slug', 'regle-sans-gdf')->first();
            $reglement = Reglement::where('ordre',1)->where('devis_id',$devis_id)->whereIn('statut_id',[78, $reglSansGdf->id])->first();

            if(!$reglement) return false;

            else return true;

        }else return true;
        
    }

    public function getStructure($user)
    {
        /** @var User $user */

        if ($user->isEquipeMember()) {
            $equipeUser = EquipeUser::where('user_id', $user->id)->where('deleted', null)->first();

            return ['userable_id' => $equipeUser->equipe_id, 'userable_type' => 'Equipe'];
        }

        if ($user->isResponsableSite()) { 
            $siteUser = SiteUser::where('user_id', $user->id)->where('deleted', null)->first();

            return ['userable_id' => $siteUser->site_id, 'userable_type' => 'Site'];
        }

        if ($user->isResponsableSociete()) { 
            $societeUser = SocieteUser::where('user_id', $user->id)->where('deleted', null)->first();

            return ['userable_id' => $societeUser->societe_id, 'userable_type' => 'Societe'];
        }

        if ($user->isGestionnaire()) { 
            $gestionnaire = ServiceUser::where('user_id', $user->id)->first();
            $serviceGestion = Service::find($gestionnaire->service_id);

            return ['userable_id' => $serviceGestion->id, 'userable_type' => 'ServiceGestion'];
        }

        if ($user->profil_id == 20) { // "Prestataire" is a user only used for fiches insertion (profil 20 does not exist) @TODO: look into this later
            return ['userable_id' => $user->id, 'userable_type' => 'User'];
        }

        return ['userable_id' => $user->courtier_id, 'userable_type' => 'Courtier'];
    }
    public function noterContrat(Request $request){
        $userCnx     = Auth::user();
        FonctionController::setConfigMySql2($userCnx->courtier_id);
        $request->validate([
            "noteclients.note"        => "required",
            "noteclients.commentaire" => "required"
        ]);
        $noteclients   = $request->get("noteclients");
        $conseiller_id = $request->get("conseiller_id");
        $contrat_id    = $request->get("contrat_id");
        $devis_id      = $request->get("devis_id");
        $user          = User::find($conseiller_id);
        $user_stucture = $this->getStructure($user);

        $fiche_id                  = Devis::join('fiches', 'fiches.num_fiche', 'devis.num_fiche')->where('devis.id', $devis_id)->first()->id;
        $fiche                     = Fiche::find($fiche_id);
        $traces                    = new TraceMatiere;
        $traces->courtier_id       = Auth::user()->courtier_id;
        $traces->fiche_id          = $fiche->id;
        $traces->user_id           = Auth::user()->id;
        $traces->dispatchable_id   = $fiche->dispatchable_id;
        $traces->dispatchable_type = $fiche->dispatchable_type;
        $traces->statut_id         = 2;
        $traces->complement        = '';
        $traces->active            = 1;
        
        $tabInfoTraceMatiere = [  
            'courtier_id'       => Auth::user()->courtier_id,
            'fiche_id'          => $fiche->id,
            'user_id'           => $user->id,
            'userable_id'       => $fiche->userable_id,
            'userable_type'     => $fiche->userable_type,
            'dispatchable_id'   => $fiche->dispatchable_id,
            'dispatchable_type' => $fiche->dispatchable_type,
            'entitable_id'      => $contrat_id,
            'entitable_type'    => "Contrat",
            'statut_id'         => 2,
            'complement'        => '',
            'active'            => 1,
        ]; 
        if(Arr::exists($noteclients, "id") == true){
            $currentNote                 = Noteclient::find($noteclients["id"]);
            $currentNote->note           = $noteclients["note"];
            $currentNote->contrat_id     = $contrat_id;
            $currentNote->commentaire    = $noteclients["commentaire"];
            $currentNote->save();
            
            $tabInfoTraceMatiere["commentaire"]    = "Modifier la notation de l'entité ".$currentNote->entitable_type . $currentNote->entitable_id." ";
            $tabInfoTraceMatiere["slug"]           = "notation-client";
            event(new EventTraceMatieres($tabInfoTraceMatiere));

            return response()->json(["note" => $currentNote,"state" => true,"type" => $currentNote->type]);
        }   
        else{
            // servicesClient
            if($noteclients["type"] == "conseiller" ){

                $newNote =   Noteclient::create([
                "note"           => $noteclients["note"],
                "contrat_id"     => $contrat_id,
                "user_id"        => $conseiller_id,
                "commentaire"    => $noteclients["commentaire"],
                "type"           => "conseiller",
                "entitable_id"   => $user_stucture["userable_id"],
                "entitable_type" => $user_stucture["userable_type"],
            ]);
            $traces->commentaire = "Notation conseiller par l'entité".$user_stucture['userable_type'] . $user_stucture["userable_id"]." ";
            $traces->slug = "notation-client-conseiller";
            
            $tabInfoTraceMatiere["commentaire"]    = "Notation conseiller par l'entité ".$user_stucture['userable_type'] . $user_stucture["userable_id"]." ";
            $tabInfoTraceMatiere["slug"]           = "notation-client";
            event(new EventTraceMatieres($tabInfoTraceMatiere));

            return response()->json(["note" => $newNote,"state" => true, "type" => $newNote->type]);
        }
        else if($noteclients["type"] == "serviceVal" ){
        $type_service_id = TypeService::where('libelle','=','Validation')->first()->id;
        
        $serviceValidation = Service::whereRaw("JSON_CONTAINS(type , '" . $type_service_id . "')")->first();
        $newNote  = Noteclient::create([
                    "note"           => $noteclients["note"],
                    "contrat_id"     => $contrat_id,
                    "commentaire"    => $noteclients["commentaire"],
                    "type"           => "serviceVal",
                    "entitable_id"   => $serviceValidation->id,
                    "entitable_type" => "Service",
                ]);
            $tabInfoTraceMatiere["commentaire"]    = "Notation service validation par l'entité ".$newNote->entitable_type . $newNote->entitable_id." ";
            $tabInfoTraceMatiere["slug"]           = "notation-client";
            event(new EventTraceMatieres($tabInfoTraceMatiere));
                return response()->json(["note" => $newNote,"state" => true, "type" => $newNote->type]);
            }

            if($noteclients["type"] == "SServiceClient" ){
             $type_service_id = TypeService::where('libelle','=','Gestion')->first()->id;
             $serviceGestion = Service::whereRaw("JSON_CONTAINS(type , '" . $type_service_id . "')")->first();
             $newNote = Noteclient::create([
                    "note"           => $noteclients["note"],
                    "contrat_id"     => $contrat_id,
                    "commentaire"    => $noteclients["commentaire"],
                    "type"           => "SServiceClient",
                    "entitable_id"   => $serviceGestion->id,
                    "entitable_type" => "Service",
                ]);
                $tabInfoTraceMatiere["commentaire"]    = "Notation service client par l'entité ".$newNote->entitable_type . $newNote->entitable_id." ";
                $tabInfoTraceMatiere["slug"]           = "notation-client";
                event(new EventTraceMatieres($tabInfoTraceMatiere));

                return response()->json(["note" => $newNote,"state" => true, "type" => $newNote->type]);
            }
        }
    }
    public function showContrat($id){
        $d = Contrat::first();
        dd($d);
        $contrat =  Contrat::with('devis')->with('noteclients')->find($id);

        return response()->json($contrat);
    }

    public function updateRGPD(Request $request){
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);

        $contratId = $request->contrat['id'];
        $contratRGPD = Contrat::find($contratId);
        $contratRGPD->rgpd = !$contratRGPD->rgpd;
        
        $devisContrat = $contratRGPD->devis;
        $fichesContrat = $contratRGPD->devis->fiche;
        
        $tierContrat = Tiers::find($devisContrat->tiers_id);
        $tierFiches = Fiche::where('tiers_id', $tierContrat->id)->get();

        if($fichesContrat){
            foreach($tierFiches as $fiche){
                if($contratRGPD->rgpd == 1){
                // Change all contrat fiches statuts to RGPD-Contrat
                    $fiche->statut_id = 121;    // RGPD Contrat
                    $fiche->save();
                }else{
                    // Change fiche contrat statut to last status
                    $lastStatut = Statutaction::where('entitable_id', $fiche->id)
                                            ->where('entitable_type', 'Fiche')
                                            ->whereActive(1)
                                            ->whereNotIn('statut_id',[121])
                                            ->latest()
                                            ->take(1)
                                            ->first();

                    if($lastStatut){
                        $fiche->statut_id = $lastStatut->statut_id;
                    }else{
                        if($fichesContrat && $fiche->id == $fichesContrat->id){
                            $fiche->statut_id = 25;     // ferme passage contrat
                        }else{
                            $fiche->statut_id = 2;      // appel
                        }
                    }
                    $fiche->save();
                }
            }
        }

        // save contrat rgpd change in db after fiche process
        $contratRGPD->save();

        $slug = 'rgpd-contrat';
        if($contratRGPD->rgpd){
            $commentaire = "RGPD activer par $user->nom $user->prenom";
        }else{
            $commentaire = "RGPD désactiver par $user->nom $user->prenom";
        }
        $devis = Devis::where('id', $contratRGPD->devis_id)->with('fiche')->first();

        $tabInfoTraceMatiere = [  
            'courtier_id'        => $user->courtier_id, 
            'fiche_id'           => $devis->fiche->id,
            'user_id'            => $user->id,
            'userable_id'        => $devis->fiche->userable_id,
            'userable_type'      => $devis->fiche->userable_type,
            'entitable_id'       => $contratRGPD->id,
            'entitable_type'     => 'Contrat',
            'dispatchable_id'    => $devis->fiche->dispatchable_id,
            'dispatchable_type'  => $devis->fiche->dispatchable_type,
            'statut_id'          => $contratRGPD->statut_id,
            'slug'               => $slug,
            'commentaire'        => $commentaire,
            'complement'         => null,
            'active'             => 1, 
        ];

        event(new EventTraceMatieres($tabInfoTraceMatiere));
    }

    public function getBrnches(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        FonctionController::setConfigMySql2($user->courtier_id); 
        $branchesIds = [];

        if ($user->isAdminCourtier() || $user->isResponsableCourtier())  {
            $branchesIds = BrancheCourtier::whereActive(1)->pluck('branche_id');
        } else {
            $branchesIds = BrancheUser::where('user_id', $user->id)
                            ->whereActive(1)
                            ->pluck('branche_id');
        }

        return Branche::whereIn('id', $branchesIds)->whereActive(1)->get();
    }

    public function recalculeCacIndex()
    {
        return view('contrats.recalculeCac');
    }

    public function searchContratRecalculeCac(Request $request){
        FonctionController::setConfigMySql2(Auth::user()->courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        // Gets and validate inputs
        if(!$request->dateDebut && !$request->dateFin) {
            return response()->json([
                'message' => 'Vous devez sélectionner une période'
            ], 455);
        }
        if(!$request->branche) {
            return response()->json([
                'message' => 'Vous devez sélectionner une branche'
            ], 455);
        }

        if(!$request->typeDate) {
            return response()->json([
                'message' => 'Vous devez sélectionner un type de date'
            ], 455);
        }

        if(!$request->compagnie && !$request->prestataireId) {
            return response()->json([
                'message' => 'Vous devez sélectionner une compagnie ou un prestataire'
            ], 455);
        }

        if(!$request->cacRecalculeCheck && !$request->commRecalculeCheck && !$request->newCommCheck) {
            return response()->json([
                'message' => 'Vous devez sélectionner CAC, Commission Cabinet, les deux, ou nouveau commission'
            ], 455);
        }

        if($request->cacTaux && $request->cacTaux > 100) {
            return response()->json([
                'message' => 'CAC taux doit être inférieur à 100'
            ], 455);
        }

        if($request->comCabTaux && $request->comCabTaux > 100) {
            return response()->json([
                'message' => 'Commission cabinet taux doit être inférieur à 100'
            ], 455);
        }

        $dateDebut = $request->dateDebut? Carbon::createFromFormat('d/m/Y H:i', $request->dateDebut)->format('Y-m-d H:i:s') : '';
        $dateFin = $request->dateFin? Carbon::createFromFormat('d/m/Y H:i', $request->dateFin)->format('Y-m-d H:i:s') : '';
        $typeDate = $request->typeDate;
        $statutsContrats = $request->statutsContrats;
       
        // groupe pub request
        $grpPubProvenances = null;

        if ($request->grpPubId) {
            $grpPubProvenances = GroupepubProvenance::where('groupepub_id', $request->grpPubId )->pluck('id')->toArray();

        } elseif ($request->prestataireId && !$request->grpPubId) {
            $grpPubs = Groupepub::where('prestataire_id', $request->prestataireId)->pluck('id');
            $grpPubProvenances = GroupepubProvenance::whereIn('groupepub_id', $grpPubs)->pluck('id')->toArray();
        }

        if($request->branche) $typeFicheId = $request->typeFicheId;
        else $typeFicheId = null;

        $typeDateQueryString = null;
        if($typeDate == 1){
            $typeDateQueryString = 'contrats.created_at';
        }elseif($typeDate == 2){
            $typeDateQueryString = 'devis.date_validation';
        }

        if($typeDate == 2 || $request->compagnie || $request->gamme || $request->garantie || $typeFicheId){
            $searchDevisTable = true;
        }else{
            $searchDevisTable = false;
        }
        if($typeDate == 1 || $statutsContrats){
            $searchContratTable = true;
        }else{
            $searchContratTable = false;
        }
        if($request->branche && $request->branche > 0 || $request->prestataireId || $request->grpPubId){
            $searchFicheTable = true;
        }else{
            $searchFicheTable = false;
        }


        $contratsResults = Devis::
                        when($typeDate == 2, function($query) use($typeDateQueryString, $dateDebut, $dateFin){
                            $query->whereBetween($typeDateQueryString, [$dateDebut, $dateFin]);
                        })
                        ->when($request->compagnie, function($query) use($request){
                            $query->where('devis.formule->cieCode', $request->compagnie);
                        })
                        ->when($request->gamme, function($query) use($request){
                            $query->where('devis.formule->gid', $request->gamme);
                        })
                        ->when($request->garantie, function($query) use($request){
                            $query->where('devis.formule->id', $request->garantie);
                        })
                        ->when($typeFicheId, function($query) use($typeFicheId){
                            $query->where('devis.typefiche_id', $typeFicheId);
                        })
                        ->when($searchContratTable, function($query) use($typeDate, $typeDateQueryString, $dateDebut, $dateFin, $statutsContrats) {
                            $query->join('contrats', function ($join) use($typeDate, $typeDateQueryString, $dateDebut, $dateFin, $statutsContrats) {
                                $join->on('contrats.devis_id', '=', 'devis.id')
                                ->when($typeDate == 1, function($query) use($typeDateQueryString, $dateDebut, $dateFin){
                                    $query->whereBetween($typeDateQueryString, [$dateDebut, $dateFin]);
                                });
                            });
                            $query->when($statutsContrats, function($query) use($statutsContrats){
                                $query->where('contrats.statut_id', $statutsContrats);
                            });
                        })
                        ->when($searchFicheTable, function($query) use($request, $grpPubProvenances) {
                            $query->join('fiches', function ($join) use($request, $grpPubProvenances) {
                                $join->on('fiches.num_fiche', '=', 'devis.num_fiche')
                                ->when($request->branche, function($query) use($request){
                                    $query->where('fiches.branche_id', $request->branche);
                                })
                                ->when($grpPubProvenances, function($query) use($grpPubProvenances){
                                    $query->whereIn('fiches.groupepub_provenance_id', $grpPubProvenances);
                                });
                            });
                        })
                        ->select('devis.*');

        
        
        if($request->countSearchContrats){
            // $contratsResultsCount = $contratsResults->count();
            return response()->json([
                'count' => $contratsResults->count(),
                'data' => $contratsResults->get()->pluck('num_devis'),
            ]);
        }else{
            $contratsResults = $contratsResults->get();
        }
        $responseCheck = $this->recalculeCACComm($request, $contratsResults);
        if($responseCheck && ($responseCheck['etat'] == 'info')) return response()->json($responseCheck['message'], 466);
        else return response()->json('Recalcul effectué avec succès');
    
    }

    public function recalculeCACComm(Request $request, $devis)
    {
        
        $cacRecalculeCheck = $request->cacRecalculeCheck;
        $commRecalculeCheck = $request->commRecalculeCheck;
        $newCommCheck = $request->newCommCheck;
        $new_comm_taux = $newCommCheck ? 1 : 0;
        $cacTaux = $cacRecalculeCheck ? $request->cacTaux : null;
        $comCabTaux = $commRecalculeCheck ? $request->comCabTaux : null;
        
        $user = Auth::user();
        $devisController = new DevisController();

        /// Add 1 action with diff paramas and infos before traces
        $slug = null;
        if($cacRecalculeCheck){
            $slug = 'Recalcule cac et commission';
        }
        if(!$cacRecalculeCheck && $commRecalculeCheck){
            $slug = 'Recalcule commission';
        }
        if($newCommCheck){
            $slug.= ' + nouveau commission';

            // If now contrat throw message
            // $devisIds = $devis->pluck('id');
            // $contratExistcount = Contrat::whereIn('devis_id', $devisIds)->count();
            // if($contratExistcount <= 0){
            //     // No contrats only devis
            //     return [
            //         'etat' => 'info',
            //         'message' => 'Il n\'y a aucun contrat',
            //     ];
            // }

        }
        if(!$request->recalculeContratsECH){
            $action = RecalculeCacActions::create([
                'type' => $slug,
                'user_id' => $user->id,
                'cac_taux' => $cacTaux,
                'comm_taux' => $comCabTaux,
                'new_comm_taux' => $new_comm_taux,
                'branche_id' => $request->branche,
                'typefiche_id' => $request->typeFicheId,
                'prestataire_id' => $request->prestataireId,
                'groupepub_id' => $request->grpPubId,
                'company_id' => $request->compagnie_id,
                'gamme_id' => $request->gamme,
                'statut_id' => $request->statutsContrats,
                'active' => 1,
            ]);
        }else{
            $action = null;
            // $action = RecalculeCacActions::find($request->action_id);
        }

            $recalculeErrorExiste = false;
            foreach ($devis as $dev) {
                $con = $dev->contrat;
                try{
                    if(!$request->notMatchAny){
                        // if request->notMatchAny == true => dont do anything
                        DB::connection('mysql2')->transaction(function() use ($cacRecalculeCheck, $commRecalculeCheck, $dev, $user, $devisController, $cacTaux, $comCabTaux, $action, $con, $request, $newCommCheck) {
                            $changed_data = [];
                            if($cacRecalculeCheck){    
                                $this->recalculeCACInt($dev, $user, $devisController, $cacTaux, $changed_data);
                                // Auto recalcule comm when cac recalcule 
                                if($con){
                                    $this->recalculeCommInt($dev, $user, $devisController, $comCabTaux, $changed_data);
                                }
                            }elseif($commRecalculeCheck && !$cacRecalculeCheck){
                                // Recalcule only commission cabinet if contrat
                                if($con){
                                    $this->recalculeCommInt($dev, $user, $devisController, $comCabTaux, $changed_data);
                                }
                            }
                            if($newCommCheck){
                                // Recalcule only new commission cabinet if contrat
                                if($con){
                                    // Contrat client only recalcule new comm
                                    if($con->statut_id == 20 || $con->statut_id == 21 || $con->statut_id == 75 || $con->statut_id == 107 || $con->statut_id == 92){     //92 : Contentieux    
                                        $devisController->recalculCommissionCabinet($request, $con->id);
                                    }
                                }
                            }
                            /// success recalcule
                            /// 1- trace each devis in recalcule_cac_succedd table with changement and action id if not recalcule
                            if(!$request->recalculeContratsECH){
                                $recSucc = RecalculeCacSucceed::create([
                                    'action_id' => $action->id,
                                    'tracable_id' => $dev->id,
                                    'tracable_type' => 'App\Devis',
                                    'commentaires' => json_encode($changed_data),     // TODO
                                    'active' => 1,
                                ]);
                            }else{
                                // Recalcule failed contrats case
                                // delete failed record
                                $failedDelete = RecalculeCacFailed::where('tracable_id', $dev->id)->where('action_id', $request->action_id)->first();
                                if($failedDelete){
                                    $failedDelete->delete();
                                }
                                // create success record 
                                $recSucc = RecalculeCacSucceed::create([
                                    'action_id' => $request->action_id,
                                    'tracable_id' => $dev->id,
                                    'tracable_type' => 'App\Devis',
                                    'commentaires' => json_encode($changed_data),     // TODO
                                    'active' => 1,
                                ]);
                            }
                        });
                    }
                } catch (\Exception $e) {
                    /// 1- trace each devis in recalcule_cac_failed table with errors and action id if not recalcule failed
                    // throw $e;

                    // Get validation error based on exception type
                    if (isset($e->validator)) {
                        $errorMessage = $e->validator->errors()->messages()['error'][0];
                        $fullError = null;
                    }else{
                        $errorMessage = $e->getMessage();
                        $fullError = $e;
                    }

                    if(!$request->recalculeContratsECH){
                        $recFailed = RecalculeCacFailed::create([
                            'action_id' => $action->id,
                            'tracable_id' => $dev->id,
                            'tracable_type' => 'App\Devis',
                            'errors' => $errorMessage,     // TODO
                            'full_errors' => $fullError,
                            'active' => 1,
                        ]);
                    }else{
                        // throw $e;

                        // Recalcule failed case

                        // delete failed record and replace with new failed record with same action id
                        // delete failed record
                        $failedDelete = RecalculeCacFailed::where('tracable_id', $dev->id)->where('action_id', $request->action_id)->first();
                        if($failedDelete){
                            $failedDelete->delete();
                        }

                        // create failed record 
                        $recSucc = RecalculeCacFailed::create([
                            'action_id' => $request->action_id,
                            'tracable_id' => $dev->id,
                            'tracable_type' => 'App\Devis',
                            'errors' => $errorMessage,     // TODO
                            'full_errors' => $fullError,
                            'active' => 1,
                        ]);

                        // Mark recalcule error existe error existe 
                        $recalculeErrorExiste = true;
                    }
                }


            }
    

            if($recalculeErrorExiste){
                return [
                    'etat' => 'failed',
                    'message' => 'Il existe des erreurs, revérifier',
                ];
            }
        
    }


    public function recalculeCACInt($devis, $user, $devisController, $cacTaux = null, &$changed_data){
        $con = $devis->contrat;
        $oldCAC = $devis->montant;
        // if($con){
            // $oldChiffreReel = $con->chiffre_reel;
        // }
        if($cacTaux){       //CAC avec Taux
                
                $chiffre_reel = $devisController->calculChiffreReel($devis);
                $tarifReducComm = ($chiffre_reel * $cacTaux) / 100;
                $cad = number_format($tarifReducComm, 2, '.', '');
                $devis->montant = $cad;
                $devis->save();
                
                if($con){
                    $con->cad = $cad;
                    $con->chiffre_reel = $chiffre_reel;
                    $con->save();
                }

                array_push($changed_data, [
                    'old_CAC' => number_format($oldCAC, 2, '.', ''),
                    'new_CAC' => number_format($cad, 2, '.', ''),
                ]);

                // array_push($changed_data, [
                //     'old_Chiffre reel' => number_format($oldChiffreReel, 2, '.', ''),
                //     'new_Chiffre reel' => number_format($chiffre_reel, 2, '.', ''),
                // ]);

                if($con){
                    $tabInfoTraceMatiere = [
                        'courtier_id'        => $user->courtier_id,
                        'fiche_id'           => $devis->fiche->id,
                        'user_id'            => $user->id,
                        'userable_id'        => $devis->fiche->userable_id,
                        'userable_type'      => $devis->fiche->userable_type,
                        'entitable_id'       => $con->id,
                        'entitable_type'     => 'Contrat',
                        'dispatchable_id'    => $devis->fiche->dispatchable_id,
                        'dispatchable_type'  => $devis->fiche->dispatchable_type,
                        'statut_id'          => $con->statut_id,
                        'slug'               => 'recalculer-cac-taux',
                        'commentaire'        => "Recalculer le CAC avec le taux $cacTaux% ",
                        'complement'         => '',
                        'active'             => 1,
                    ];
                    event(new EventTraceMatieres($tabInfoTraceMatiere));
                }else{
                    // Devis
                    $tabInfoTraceMatiere = [
                        'courtier_id'        => $user->courtier_id,
                        'fiche_id'           => $devis->fiche->id,
                        'user_id'            => $user->id,
                        'userable_id'        => $devis->fiche->userable_id,
                        'userable_type'      => $devis->fiche->userable_type,
                        'entitable_id'       => $devis->id,
                        'entitable_type'     => 'Devis',
                        'dispatchable_id'    => $devis->fiche->dispatchable_id,
                        'dispatchable_type'  => $devis->fiche->dispatchable_type,
                        'statut_id'          => $devis->statut_id,
                        'slug'               => 'recalculer-cac-taux',
                        'commentaire'        => "Recalculer le CAC avec le taux $cacTaux% ",
                        'complement'         => '',
                        'active'             => 1,
                    ];
                    event(new EventTraceMatieres($tabInfoTraceMatiere));

                }
        }else{
            $cad = $devisController->claculCad($devis);
            $chiffre_reel = $devisController->calculChiffreReel($devis);
            
            $devis->montant = $cad;
            $devis->save();

            if($con){
                $con->cad = $cad;
                $con->chiffre_reel = $chiffre_reel;
                $con->save();
            }

            array_push($changed_data, [
                'old_CAC' => number_format($oldCAC, 2, '.', ''),
                'new_CAC' => number_format($cad, 2, '.', ''),
            ]);

            // array_push($changed_data, [
            //     'old_Chiffre reel' => number_format($oldChiffreReel, 2, '.', ''),
            //     'new_Chiffre reel' => number_format($chiffre_reel, 2, '.', ''),
            // ]);

            if($con){
                $tabInfoTraceMatiere = [
                    'courtier_id'        => $user->courtier_id,
                    'fiche_id'           => $devis->fiche->id,
                    'user_id'            => $user->id,
                    'userable_id'        => $devis->fiche->userable_id,
                    'userable_type'      => $devis->fiche->userable_type,
                    'entitable_id'       => $con->id,
                    'entitable_type'     => 'Contrat',
                    'dispatchable_id'    => $devis->fiche->dispatchable_id,
                    'dispatchable_type'  => $devis->fiche->dispatchable_type,
                    'statut_id'          => $con->statut_id,
                    'slug'               => 'recalculer-cac',
                    'commentaire'        => 'Recalculer le CAC',
                    'complement'         => '',
                    'active'             => 1,
                ];
                event(new EventTraceMatieres($tabInfoTraceMatiere));
            }else{
                // devis
                $tabInfoTraceMatiere = [
                    'courtier_id'        => $user->courtier_id,
                    'fiche_id'           => $devis->fiche->id,
                    'user_id'            => $user->id,
                    'userable_id'        => $devis->fiche->userable_id,
                    'userable_type'      => $devis->fiche->userable_type,
                    'entitable_id'       => $devis->id,
                    'entitable_type'     => 'Devis',
                    'dispatchable_id'    => $devis->fiche->dispatchable_id,
                    'dispatchable_type'  => $devis->fiche->dispatchable_type,
                    'statut_id'          => $devis->statut_id,
                    'slug'               => 'recalculer-cac',
                    'commentaire'        => 'Recalculer le CAC',
                    'complement'         => '',
                    'active'             => 1,
                ];
                event(new EventTraceMatieres($tabInfoTraceMatiere));

            }

        }
        
        // return $changed_data;
    }

    public function recalculeCommInt($devis, $user, $devisController, $comCabTaux = null, &$changed_data){
        $con = $devis->contrat;
        // $oldCAC = $con->cad;
        // $oldChiffreReel = $con->chiffre_reel;
        $oldCommission = $con->commission;
        $oldCommissionCabinet = $con->commission_cabinet;

        if($comCabTaux){  //Commission avec taux
            $tauxCommission = $comCabTaux;
            $chiffreReel = $devisController->calculChiffreReel($devis);
            $commission = $tauxCommission ? ($chiffreReel * $tauxCommission / 100) : 0;
            $commissionCabinet = $tauxCommission ? ($devis->montant * $tauxCommission / 100) : 0;
            $con->commission = $commission;
            $con->commission_cabinet = $commissionCabinet;

            array_push($changed_data, [
                'old_Commission' => number_format($oldCommission, 2, '.', ''),
                'new_Commission' => number_format($commission, 2, '.', ''),
            ]);

            array_push($changed_data, [
                'old_Commission cabinet' => number_format($oldCommissionCabinet, 2, '.', ''),
                'new_Commission cabinet' => number_format($commissionCabinet, 2, '.', ''),
            ]);

            $con->save();
            $tabInfoTraceMatiere = [
                'courtier_id'        => $user->courtier_id,
                'fiche_id'           => $devis->fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $devis->fiche->userable_id,
                'userable_type'      => $devis->fiche->userable_type,
                'entitable_id'       => $con->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $devis->fiche->dispatchable_id,
                'dispatchable_type'  => $devis->fiche->dispatchable_type,
                'statut_id'          => $con->statut_id,
                'slug'               => 'recalculer-comm-taux',
                'commentaire'        => "Recalculer la commission avec le taux $tauxCommission% ",
                'complement'         => '',
                'active'             => 1,
            ];
            event(new EventTraceMatieres($tabInfoTraceMatiere));

        }else{
            $commissions = $devisController->calculCommissionsOld($devis, true);
            $con->commission = $commissions['commission'];
            $con->commission_cabinet = $commissions['commissionCabinet'];

            array_push($changed_data, [
                'old_Commission' => number_format($oldCommission, 2, '.', ''),
                'new_Commission' => number_format($commissions['commission'], 2, '.', ''),
            ]);

            array_push($changed_data, [
                'old_Commission cabinet' => number_format($oldCommissionCabinet, 2, '.', ''),
                'new_Commission cabinet' => number_format($commissions['commissionCabinet'], 2, '.', ''),
            ]);

            $con->save();

            $tabInfoTraceMatiere = [
                'courtier_id'        => $user->courtier_id,
                'fiche_id'           => $devis->fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $devis->fiche->userable_id,
                'userable_type'      => $devis->fiche->userable_type,
                'entitable_id'       => $con->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $devis->fiche->dispatchable_id,
                'dispatchable_type'  => $devis->fiche->dispatchable_type,
                'statut_id'          => $con->statut_id,
                'slug'               => 'recalculer-comm',
                'commentaire'        => 'Recalculer la commission',
                'complement'         => '',
                'active'             => 1,
            ];
            event(new EventTraceMatieres($tabInfoTraceMatiere));

        }
    }

    public function recalculeCacGetActions(Request $request){
        FonctionController::setConfigMySql2(Auth::user()->courtier_id);
        FonctionController::setConfigMySqlTarificateur();

        $dateDebut = $request->dateDebut? Carbon::createFromFormat('d/m/Y H:i', $request->dateDebut)->format('Y-m-d 00:00:00') : '';
        $dateFin = $request->dateFin? Carbon::createFromFormat('d/m/Y H:i', $request->dateFin)->format('Y-m-d 23:59:59') : '';

        $actions = RecalculeCacActions::orderBy('created_at', 'desc')
            ->where('active', 1)
            ->where(function($query) use($request, $dateDebut, $dateFin){
                if($dateDebut) $query->where('created_at', '>=', $dateDebut);
                if($dateFin) $query->where('created_at', '<=', $dateFin);
                if($request->branche) $query->where('branche_id', $request->branche);
                if($request->typeFicheId) $query->where('typefiche_id', $request->typeFicheId);
                if($request->grpPubId) $query->where('groupepub_id', $request->grpPubId);
                if($request->prestataireId) $query->where('prestataire_id', $request->prestataireId);
                if($request->compagnie) $query->where('company_id', '=', $request->compagnie_id);
                if($request->gamme) $query->where('gamme_id', '=', $request->gamme);
                if($request->statutsContrats) $query->where('statut_id', '=', $request->statutsContrats);

            })
            ->with('user')
            ->withCount('cac_failed')
            ->withCount('cac_succeed')
            ->with('branche')
            ->with('type_fiche')
            ->with('prestataire')
            ->with('Groupepub')
            ->with('company')
            ->with('gamme')
            ->paginate(20);

        return $actions;

    }

    public function getRecalculeContratsRS(Request $request){

        FonctionController::setConfigMySql2(Auth::user()->courtier_id);
        $actionId = $request->actionId;
        if($actionId){
            $recalculeContartsRS = RecalculeCacSucceed::
                                    where('action_id', $actionId)
                                    ->where('active', 1)
                                    ->with('tracable.contrat')
                                    ->get();
            
            foreach($recalculeContartsRS as $suc){
                if($suc->commentaires && !is_array($suc->commentaires)){
                    $suc->commentaires = json_decode($suc->commentaires, true);
                }
            }
        }else{
            $recalculeContartsRS = null;  
        }
        return $recalculeContartsRS;
    }

    public function getRecalculeContratsECH(Request $request){

        FonctionController::setConfigMySql2(Auth::user()->courtier_id);
        $actionId = $request->actionId;
        if($actionId){
            $recalculeContartsECH = RecalculeCacFailed::
                                    where('action_id', $actionId)
                                    ->where('active', 1)
                                    ->with('tracable.contrat')
                                    ->get();
            
        }else{
            $recalculeContartsECH = null;  
        }
        return $recalculeContartsECH;
    }

    public function recalculeContratsECH(Request $request){
        FonctionController::setConfigMySql2(Auth::user()->courtier_id);

        $actionId = $request->action_id;
        $action = RecalculeCacActions::find($request->action_id);
        $cac_failed_count = RecalculeCacFailed::where('action_id', $actionId)->get()->count();
        if(!$cac_failed_count || $cac_failed_count <= 0){
            return response()->json('Il n\'y a pas de contrats', 455);
        }
        $devisIds = RecalculeCacFailed::where('action_id', $actionId)->pluck('tracable_id');
        if($devisIds){
            $devis = Devis::whereIn('id', $devisIds)->get();
            if($action->type == 'Recalcule commission'){
                $request->merge([ 'commRecalculeCheck' => true ]);
            }elseif($action->type == 'Recalcule cac et commission'){
                $request->merge([ 'cacRecalculeCheck' => true ]);
                $request->merge([ 'commRecalculeCheck' => true ]);
            }elseif($action->type == ' + nouveau commission'){
                $request->merge([ 'newCommCheck' => true ]);
            }elseif($action->type == 'Recalcule cac et commission + nouveau commission'){
                $request->merge([ 'cacRecalculeCheck' => true ]);
                $request->merge([ 'commRecalculeCheck' => true ]);
                $request->merge([ 'newCommCheck' => true ]);
            }elseif($action->type == 'Recalcule commission + nouveau commission'){
                $request->merge([ 'commRecalculeCheck' => true ]);
                $request->merge([ 'newCommCheck' => true ]);
            }else{
                // Not match any
                // $request->merge([ 'cacRecalculeCheck' => true ]);
                $request->merge([ 'notMatchAny' => true ]);
            }
            $request->merge([ 
                'cacTaux' => $action->cac_taux, 
                'comCabTaux' => $action->comm_taux, 
                // 'new_comm_taux' => $action->new_comm_taux, 
                'recalculeContratsECH' => true, 
            ]);
            // dd($request);
            $response = $this->recalculeCACComm($request, $devis);

            if($response['etat'] == 'failed') return response()->json($response['message'], 455);
            else return response()->json('Recalcule fait avec success');
        }else{
            return response()->json('Il n\'y a pas de contrats', 455);
        }
    }

    public function changerStatutContrats(Request $request){

        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);

        $contratIds = $request->contrats;
        $statutId = $request->statutId;
        $comment = $request->comment;

        $allStatuts = Statut::get();

        foreach ($contratIds as $conId) {
            $contrat = Contrat::find($conId);
            $devis = Devis::find($contrat->devis_id);
            $fiche = Fiche::where('num_fiche', $devis->num_fiche)->first();

            $oldStatutLibelle = $allStatuts->where('id', $contrat->statut_id)->first()['libelle'] ?? null;
            $newStatutLibelle = $allStatuts->where('id', $statutId)->first()['libelle'] ?? null;

            $contrat->statut_id = $statutId;
            // Update date impaye if statut = impaye
            if($statutId == 107){
                $contrat->date_impaye = Carbon::now();
            }
            $contrat->save();

            // Trace statut
            $statutAction = new Statutaction();
            $statutAction->entitable_id = $contrat->id;
            $statutAction->entitable_type = 'Contrat';
            $statutAction->user_id = $user->id;
            $statutAction->statut_id = $contrat->statut_id;
            $statutAction->motif_id =  NULL;
            $statutAction->complement = $comment ? $comment : NULL;
            $statutAction->save();

            // Trace Histr
            $tabInfoTraceMatiere = [  
                'courtier_id'        => $user->courtier_id, 
                'fiche_id'           => $fiche->id,
                'user_id'            => $user->id,
                'userable_id'        => $fiche->userable_id,
                'userable_type'      => $fiche->userable_type,
                'entitable_id'       => $contrat->id,
                'entitable_type'     => 'Contrat',
                'dispatchable_id'    => $fiche->dispatchable_id,
                'dispatchable_type'  => $fiche->dispatchable_type,
                'statut_id'          => $contrat->statut_id,
                'slug'               => 'changer-statut-contrat',
                'commentaire'        => "Changer le statut du contrat de $oldStatutLibelle à $newStatutLibelle",
                'complement'         => $comment ? $comment : null,
                'active'             => 1, 
            ]; 
    
            event(new EventTraceMatieres($tabInfoTraceMatiere));

        }

        return response()->json('Operation fait avec success');

    }

    public function deleteContrat(Request $request){
        $user = Auth::user();
        FonctionController::setConfigMySql2($user->courtier_id);

        $contratId = $request->id;
        $contrat = Contrat::find($contratId);

        // detect if 2 contrat existe in the same devis
        $countratCount = Contrat::where('devis_id', $contrat->devis_id)->count();
        if($countratCount <= 1){
            return response()->json('Aucune duplication de contrat trouvée', 455);
        }
        DB::connection('mysql2')->transaction(function() use ($contrat, $contratId) {
            $contrat->delete();
            // delete commision cabinet records of this contrat
            $commissionIds = CommissionCabinet::where('contrat_id', $contratId)->pluck('id');
            $deleteCommForm = CommissionFormule::whereIn('commission_id', $commissionIds)->delete();
            $deleteCommCabinet = CommissionCabinet::where('contrat_id', $contratId)->delete();
        });
        return response()->json('Contrat supprimé avec succès');
        

    }

}